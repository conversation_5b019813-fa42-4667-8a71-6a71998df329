# Stage 1: Set up the base Amazon Linux image
FROM amazonlinux:2023 AS base

ARG USERNAME
ARG LOGIN_KEY
ENV USERNAME=$USERNAME
ENV LOGIN_KEY=$LOGIN_KEY
USER root

# Install necessary utilities for the final image
RUN dnf install -y wget shadow-utils && \
    dnf clean all

# Stage 2: Copy Prometheus components from the official image
FROM quay.io/prometheus/prometheus:latest AS prometheus_source

# Stage 3: Set up the final image with Prometheus components
FROM amazonlinux:2023 AS final

# Install shadow-utils for user management
RUN dnf install -y shadow-utils && dnf clean all

# Install required packages
RUN dnf install -y dos2unix
RUN dnf install -y less wget vim httpd-tools procps gettext kmod
RUN dnf install -y fuse3 findutils
#RUN dnf install -y fuse
#RUN dnf install -y systemd
RUN dnf install -y openssh-clients

# Copy RPMs and install them
COPY /docker-images/cwiqfs/fuse-libs-2.9.2-11.el7.x86_64.rpm  /tmp
COPY /docker-images/cwiqfs/fuse-sshfs-2.10-1.el7.x86_64.rpm /tmp
COPY docker-images/cwiqfs/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm /tmp

# Install cwiqfs client RPM
RUN rpm -i /tmp/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm

# Ensure the symbolic link exists or create it
RUN [ ! -e /etc/ssl/certs/ca-certificates.crt ] && ln -s /etc/ssl/certs/ca-bundle.crt /etc/ssl/certs/ca-certificates.crt || echo "Link already exists"

# Copy cwiqfs config and configure it
COPY docker-images/cwiqfs/cwiqfs.yaml /etc/cwiq/cwiqfs/config.yaml
#RUN envsubst '${USERNAME},${LOGIN_KEY}' < /etc/cwiq/cwiqfs/config_temp.yaml > /etc/cwiq/cwiqfs/config.yaml

# Modify fuse.conf
RUN sed -i -e 's/# user_allow_other/user_allow_other/g' /etc/fuse.conf

# Create directories and user
RUN mkdir /jfs
RUN useradd jsvc-tech1

# Copy mount-jfs.sh script
RUN mkdir /usr/lib/jfs && chmod -R 755 /usr/lib/jfs
COPY docker-images/cwiqfs/mount-jfs.sh /usr/lib/jfs/mount-jfs.sh
RUN chmod +x /usr/lib/jfs/mount-jfs.sh


# Convert mount-jfs.sh script to Unix format
RUN dos2unix /usr/lib/jfs/mount-jfs.sh

# Copy Prometheus binaries and default configuration from the official Prometheus image
COPY --from=prometheus_source /bin/prometheus /bin/prometheus
COPY --from=prometheus_source /bin/promtool /bin/promtool
COPY --from=prometheus_source /etc/prometheus /etc/prometheus

# Create Prometheus user and set up directories
RUN useradd -ms /bin/bash prometheus && \
    mkdir -p /prometheus && \
    chown -R prometheus:prometheus /prometheus /etc/prometheus

# Expose Prometheus port
EXPOSE 9090

# Run Prometheus as a non-root user
USER prometheus

# Default command for running Prometheus
CMD ["/bin/prometheus", "--config.file=/etc/prometheus/prometheus.yml", "--storage.tsdb.path=/prometheus"]
