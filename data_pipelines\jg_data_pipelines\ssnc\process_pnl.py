import pandas as pd
import re
import logging
import os, sys,json
from datetime import datetime, timezone
sys.path.append(os.getcwd())
import json
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '../../../source/bin')))
from strunner import *
setupEnvironment()
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)
from utils.snowflake.adaptor import SnowflakeAdaptor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def process_data(file_path, latest_version, file_type):
    expect_currency = False
    capture_data=False
    new_section_start=False
    current_metadata={}
    current_fund_name = None
    metadata = []
    data = []
    temp_data=[]
    columns=[]
    
    with open(file_path,'r') as file:
        lines = file.readlines()
        for line in lines:
            line = line.strip()
            if line.startswith('"GlobeOp Profit and Loss (Detailed) with Amortization'):
                if current_metadata:
                    metadata.append(current_metadata)
                    if temp_data:
                        data.extend(temp_data)
                    current_metadata = {}
                    temp_data = []
                        
                current_metadata['File_Type'] = file_type
                current_metadata['Version'] = latest_version
                capture_data=False
                new_section_start=True
                continue
    
            if new_section_start:
                current_fund_name = line.replace('"', '')
                current_metadata['Fund_Name'] = current_fund_name
                new_section_start=False
                continue
            
            elif line.startswith('"Period Start Date'):
                current_metadata['Period_Start_Date'] = line.split(',')[1].replace('"', '').replace(':', ' ', 1)
                continue
            
            elif line.startswith('"Period End Date'):
                current_metadata['Period_End_Date'] = line.split(',')[1].replace('"', '').replace(':', ' ', 1)
                continue
            
            elif line.startswith('"Prior Knowledge Date'):
                current_metadata['Prior_Knowledge_Date'] = line.split(',')[1].replace('"', '').replace(':', ' ', 1)
                continue
                
            elif line.startswith('"Knowledge Date'):
                current_metadata['Knowledge_Date'] = line.split(',')[1].replace('"', '').replace(':', ' ', 1)
                continue
                
            elif line.startswith('"Accounting Calendar'):
                current_metadata['Accounting_Calendar'] = line.split(',')[1].replace('"', '')
                
                expect_currency=True
                continue
    
            elif expect_currency and line.strip():
                current_metadata['Currency'] = line.replace('"', '').strip()
                expect_currency=False
                capture_data=True
                continue
            
            if capture_data and not columns:
                columns = line.split(',')
            
            elif capture_data and line and not line.startswith('GlobeOp Profit and Loss (Detailed) with Amortization'):
                if columns:
                    row=line.split('","')
                    
                    columns[0] = "Type"
                    if 'Ending Quantity' not in row:
                        temp_data.append({
                            'File_Type': current_metadata['File_Type'],
                            'Version': current_metadata['Version'],
                            'Fund_Name': current_metadata['Fund_Name'],
                            **{columns[i].replace(' ', '_').replace('/', '_').replace('"', ''): row[i].replace('"', '').replace(',', '').strip() for i in range(len(columns)) if i != 1}
                        })
                    continue
    
        if current_metadata:
            metadata.append(current_metadata)
        if temp_data:
            data.extend(temp_data)
        
        metadata_df = pd.DataFrame(metadata)
        data_df = pd.DataFrame(data)
        
        return metadata_df, data_df 
    
def get_files_with_substring_and_date(rawdata_directory, substring, date):
        date_obj = datetime.strptime(date, '%Y-%m-%d')

        # Get the list of files in the current directory
        files = os.listdir(rawdata_directory)
        filtered_files = []
        
        for f in files:
            if substring in f :
                mod_time = datetime.fromtimestamp(os.path.getmtime(f'{rawdata_directory}/{f}'))
                if mod_time.date() == date_obj.date():
                    filtered_files.append(f'{rawdata_directory}/{f}')

        # Sort the filtered files by last modified time
        sorted_files = sorted(filtered_files, key=lambda x: os.path.getmtime(x))
        return sorted_files


if __name__ == "__main__":
    
    configPath = os.environ.get('CONFIG_PATH', os.getcwd())
    with open(f'{configPath}/config.json', 'r') as f:
        config = json.load(f)

    def jg_config_path():
        return config["JG_CONFIG_PATH"]
    
    def jg_rawdata_path():
        return config["JG_DATA_PATH"]

    def read_config_secrets():
        config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
        with open(config_secret_path, 'r') as f:
            config_secret = json.load(f)
        return config_secret
    objconfig = read_config_secrets()

    os.environ['SF_USERNAME'] = objconfig['sf_user']
    os.environ['SF_PASSWORD'] = objconfig['sf_password']
    os.environ['SF_DATABASE'] = objconfig['ssnc_database']
    os.environ['SF_WAREHOUSE'] = objconfig['ssnc_warehouse']
    os.environ['SF_ROLE'] = objconfig['ssnc_role']
    os.environ['SF_SCHEMA'] = schema = objconfig['ssnc_schema']
    
    jfs_rawdata_directory = f'{jg_rawdata_path()}/SSandC'
    sf_adaptor = SnowflakeAdaptor(database=objconfig['ssnc_database'], warehouse=objconfig['ssnc_warehouse'], role=objconfig['ssnc_role'])
    
    stage_list = sf_adaptor.read_data(schema, f'LIST @{schema}.STG_METADATA;')
    loaded_files_list = stage_list['name'].to_list()
    for i in range(len(loaded_files_list)):
        loaded_files_list[i] = loaded_files_list[i].replace('stg_metadata/', '').replace('.gz', '')     
    
    substring = 'GlobeOp_Profit_and_Loss__Detailed__with_Amortization'
    date = datetime.now(timezone.utc).date().strftime('%Y-%m-%d')
    files = get_files_with_substring_and_date(jfs_rawdata_directory, substring, date)
    
    for file_path in files:  
        logger.info(f"\n\n\nProcessing file:: {file_path}")
        file_path = file_path.replace(f'{jfs_rawdata_directory}/', '')
        file_type = file_path.replace('.csv', '').replace('.', '_').split('GlobeOp_Profit_and_Loss__Detailed__with_Amortization', 1)[-1]
        if file_type.startswith('_'):
            file_type=file_type[1:]
        
        if file_path in loaded_files_list:
            logger.info(f"Data is already loaded for file '{file_path}'")
            continue
            
        if file_type in ['CLOSED_PERIOD_MTD', 'CLOSED_PERIOD_YTD']:
            logger.info(f"Skipping file: {file_path}..")
            continue

        metadata_table = f'{schema}.PNL_METADATA'
        metadata_stage = f'{schema}.STG_METADATA'

        if "DYNAMIC" in file_path:
            schema_name = objconfig['ssnc_schema']
            table_name = f'{schema}.PNL_DYNAMIC'
            staging_table_name = f'STAGING.PNL_DYNAMIC'
            stage_name = f'{schema}.STG_DYNAMIC'

        elif 'CLOSED_PERIOD' in file_path: 
            schema_name = objconfig['ssnc_schema']
            table_name = f'{schema}.PNL_CLOSED_PERIOD'
            staging_table_name = f'STAGING.PNL_CLOSED_PERIOD'
            stage_name = f'{schema}.STG_CLOSED_PERIOD'

        fetch_query = f"SELECT * FROM {table_name} WHERE FILE_TYPE = '{file_type}' AND VERSION = (SELECT MAX(VERSION) FROM {table_name} WHERE FILE_TYPE = '{file_type}');"

        existing_data_df = sf_adaptor.read_data(schema_name, fetch_query, objconfig['ssnc_role']) 

        latest_version = sf_adaptor.read_data(schema_name, f"SELECT MAX(VERSION) FROM {table_name} WHERE FILE_TYPE='{file_type}'", objconfig['ssnc_role'])
        latest_version = 0 if latest_version.iloc[0,0] is None else latest_version.iloc[0,0]

        metadata_df, data_df = process_data(f'{jfs_rawdata_directory}/{file_path}', latest_version, file_type)
        logger.info("Completed processing data")

        # load staging data
        sf_adaptor.execute_query(schema_name, f'TRUNCATE {staging_table_name}')

        sf_adaptor.load_df(data_df, file_path, 'STAGING', staging_table_name, stage_name)
        staging_data_df = sf_adaptor.read_data(schema_name, f'SELECT * FROM {staging_table_name}', objconfig['ssnc_role'])

        if not existing_data_df.empty and existing_data_df.equals(staging_data_df):
            fetch_query = f"SELECT MAX(DATE(KNOWLEDGE_DATE)) FROM {metadata_table} WHERE FILE_TYPE = '{file_type}' AND VERSION = (SELECT MAX(VERSION) FROM {metadata_table} WHERE FILE_TYPE = '{file_type}');"
            KNOWLEDGE_DATE = sf_adaptor.read_data(schema_name, fetch_query, objconfig['ssnc_role'])
            if(str(KNOWLEDGE_DATE.iloc[0,0])==date):
                logger.info("Same data as previous file, no new data to load")
            else:
                logger.info("Updating METADATA table to point to previous version")
                sf_adaptor.load_df(metadata_df, file_path, schema_name, metadata_table, metadata_stage)

        else:
            logger.info("Uploading data...")
            metadata_df['File_Type'] = file_type
            data_df['File_Type'] = file_type
            metadata_df['Version'] = metadata_df['Version']+1 
            data_df['Version'] = data_df['Version']+1
            sf_adaptor.load_df(metadata_df, file_path, schema_name, metadata_table, metadata_stage)
            sf_adaptor.load_df(data_df, file_path, schema_name, table_name, stage_name, True)