import os
import numpy as np
import pandas as pd
import time
from datetime import datetime
import pytz
from spiderrock.mlink_restapi import (
    invoke_mlink_rest_api,
    SpiderRockMessageType,
    DICT_MARKET_STATUS,
)
from utils.postgres.adaptor import PostgresAdaptor

from sqlalchemy import (
    create_engine,
    MetaData,
    Table,
    Column,
    Integer,
    String,
    Date,
    Float,
    Sequence,
    SmallInteger,
)
from sqlalchemy.dialects.postgresql import insert


if __name__ == "__main__":
    loader = PostgresAdaptor(
        # host="tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com",
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df_securities = loader.execute_query(
        "select security_code_id, security_code, bbg_full_ticker from eqvol.sr_security_ref;"
    )
    dict_securities = dict(
        zip(df_securities["security_code"], df_securities["security_code_id"])
    )
    
    start_time = time.time()
    (df_options_quotes, opts_api_req_time, opts_api_ref_date) = invoke_mlink_rest_api(
        tickers=dict_securities.keys(),
        messageTyp=SpiderRockMessageType.OptionNbboQuote,
    )
    end_time = time.time()
    execution_time = end_time - start_time

    df_opt_time_stamps = pd.DataFrame({"jg_api_req_timestamp": [opts_api_req_time], "jg_api_req_timestamp_date": [opts_api_ref_date]})

    df_options_quotes["security_code_id"] = df_options_quotes["security_code"].map(
        dict_securities
    )
    df_options_quotes["expiration_date"] = pd.to_datetime(
        df_options_quotes["expiration_date"]
    )

    df_options_ref = df_options_quotes[
        ["security_code_id", "call_put", "expiration_date", "strike_price"]
    ]
    # df_options_ref['expiration_date'] = pd.to_datetime(df_options_ref['expiration_date'])

    df_options_existing = loader.execute_query(
        "select option_id, security_code_id, call_put, expiration_date, strike_price from eqvol.sr_option_quote_keys;"
    )
    df_options_existing["option_id"] = df_options_existing["option_id"].astype("int64")
    df_options_existing["security_code_id"] = df_options_existing[
        "security_code_id"
    ].astype("int64")
    df_options_existing["strike_price"] = df_options_existing["strike_price"].astype(
        "int32"
    )
    df_options_existing["expiration_date"] = pd.to_datetime(
        df_options_existing["expiration_date"]
    )

    # print(df_options_ref.dtypes)
    # print(df_options_existing.dtypes)

    df_options_ref_joined = df_options_ref.merge(
        df_options_existing,
        how="left",
        on=["security_code_id", "call_put", "expiration_date", "strike_price"],
    )

    # print(df_options_ref_joined.dtypes)
    # print(df_options_ref_joined.head())

    idx_option_id_na = df_options_ref_joined["option_id"].isna()
    df_options_new = df_options_ref_joined[idx_option_id_na]
    # print(df_options_new.head())

    # df_options_ref_new = df_options_ref.merge(df_options_existing, how="inner", left_index=True, right_index=True)

    df_options_new.reset_index(drop=True, inplace=True)
    df_options_new = df_options_new[
        ["security_code_id", "call_put", "expiration_date", "strike_price"]
    ]

    new_options = df_options_new.to_dict(orient="records")
    # print(new_options)
    if new_options:
        print(
            "New options found - {nos_of_new_options}".format(
                nos_of_new_options=len(new_options)
            )
        )
        options = Table(
            "sr_option_quote_keys",
            MetaData(),
            Column(
                "option_id",
                Integer,
                Sequence(
                    "sr_option_quote_keys_option_id_seq",
                    start=1,
                    schema="eqvol",
                ),
                primary_key=True,
            ),
            Column("security_code_id", Integer),
            Column("call_put", String),
            Column("strike_price", Integer),
            Column("expiration_date", Date),
            schema="eqvol",
        )

        stmt = (
            insert(options)
            .values(new_options)
            .returning(
                options.c.option_id,
                options.c.security_code_id,
                options.c.call_put,
                options.c.expiration_date,
                options.c.strike_price,
            )
        )
        new_option_ids = loader.execute_statement(stmt)
        df_new_option_ids = pd.DataFrame(
            new_option_ids,
            columns=[
                "option_id",
                "security_code_id",
                "call_put",
                "expiration_date",
                "strike_price",
            ],
        )
        df_new_option_ids["expiration_date"] = pd.to_datetime(
            df_new_option_ids["expiration_date"]
        )
        df_new_option_ids["strike_price"] = df_new_option_ids["strike_price"].astype(
            "int32"
        )
        df_options_existing = pd.concat(
            [df_options_existing, df_new_option_ids], axis=0, ignore_index=True
        )
        df_options_existing.reset_index(drop=True, inplace=True)
    else:
        print("No new options found.")

    df_options_quotes = df_options_quotes.merge(
        df_options_existing,
        how="left",
        on=["security_code_id", "call_put", "expiration_date", "strike_price"],
    )

    df_options_quotes = df_options_quotes[
        [
            "option_id",
            "bid_price",
            "bid_size",
            "cum_bid_size",
            "bid_time",
            "ask_price",
            "ask_size",
            "cum_ask_size",
            "ask_time",
            "sr_srctimestamp",
            "jg_api_req_timestamp",
            "jg_api_req_timestamp_date",
        ]
    ]
    
    idx_na_option_id = df_options_quotes["option_id"].isna()
    if any(idx_na_option_id):
        raise ValueError("Option ID is missing for some records.")
    

    success = loader.load_dataframe(
        df=df_options_quotes,
        table_name="sr_option_quotes_recent",
        if_exists="append",
    )

    if success:
        print("Option Quotes Recent loaded successfully.")
    else:
        raise ValueError("Option Quotes Recent loading failed!")
    
    print(f"Starting to truncate sr_option_quotes_latest")
    start_time = time.time()
    loader.execute_query_no_ret("truncate table eqvol.sr_option_quotes_latest")
    elapsed_time = time.time() - start_time
    print(f"truncate statement ran in {elapsed_time:.2f} seconds")

    success = loader.load_dataframe(
        df=df_options_quotes,
        table_name="sr_option_quotes_latest",
        if_exists="append",
    )

    if success:
        print("Option Quotes Latest loaded successfully.")
    else:
        raise ValueError("Option Quotes Latest loading failed!")

    # success = loader.load_dataframe(
    #     df=df_options_quotes,
    #     table_name="sr_option_quotes",
    #     if_exists="append",
    # )

    # if success:
    #     print("Option Quotes loaded successfully.")
    # else:
    #     raise ValueError("Option Quotes loading failed!")
    
    success = loader.load_dataframe(
        df=df_opt_time_stamps,
        table_name="sr_option_quote_timestamps",
        if_exists="append",
    )

    if success:
        print("Option timestamps loaded successfully.")
    else:
        raise ValueError("Option timestamps loading failed!")