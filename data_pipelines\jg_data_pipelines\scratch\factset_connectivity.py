import os
import time
import tempfile
import zipfile
import pandas as pd
from datetime import datetime
from utils.file import temp_folder
from utils.sftp import SftpManager


sftp_kwargs = {
    "host": "fts-sftp.factset.com",
    "port": 6671,
    "username": "jaingbl_qnt",
    # "password": "",
    "disable_hostkey": True,
}

def list_dir_contents(root_dir):
    sftp_manager = SftpManager(**sftp_kwargs)
    fts_files = sftp_manager.get_directory_structure(path=root, with_attributes=True)
    sftp_manager.close()

    files = {}
    for file_attr in fts_files:
        file_name = file_attr.filename
        last_modified = file_attr.st_mtime
        files[file_name] = last_modified
    
    return files


def download_files(root_dir, jfs_path, files):
    sftp_manager = SftpManager(**sftp_kwargs)
    for file in files:
        sftp_manager.fetch(file, root_dir, jfs_path)
        print(f"Downloaded {file}.")
    sftp_manager.close()


def parse_downloaded_files(jfs_path):
    for file in os.listdir(jfs_path):
        file_path = os.path.join(jfs_path, file)
        if os.path.isfile(file_path):
            mtime = os.path.getmtime(file_path)
            # print(f"{file}: {time.ctime(mtime)}")  # Human-readable format
            if "_full_" not in file:
                print(f"Processing {file}")
                with tempfile.TemporaryDirectory() as temp_dir:
                    with zipfile.ZipFile(file_path, "r") as zip_ref:
                        zip_ref.extractall(temp_dir)
                    for temp_f in os.listdir(temp_dir):
                        temp_f_path = os.path.join(temp_dir, temp_f)
                        # print(temp_f)
                        df = pd.read_csv(temp_f_path, sep="|")
                        df["file_mtime"] = mtime
                        print(df.head())

def unzip_file_contents(jfs_file_path):
    if not os.path.isfile(jfs_file_path):
        raise ValueError("Not a file!")
    
    print(f"Processing {jfs_file_path}")

    mtime = os.path.getmtime(jfs_file_path)
    print(mtime)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        with zipfile.ZipFile(jfs_file_path, "r") as zip_ref:
            zip_ref.extractall(temp_dir)
        for temp_f in os.listdir(temp_dir):
            if temp_f == "ff_basic_af_am.txt":
                temp_f_path = os.path.join(temp_dir, temp_f)
                print(temp_f_path)
                df = pd.read_csv(temp_f_path, sep="|")
                print(df.head())
                print(df.shape)
                
            # df = pd.read_csv(temp_f_path, sep="|")
            # df["file_mtime"] = mtime
            # print(df.head())

if __name__ == "__main__":
    root = "datafeeds/fundamentals/ff_basic_am_v3"
    jfs_path = "/jfs/tech1_share/pulkit.vora/jsvc.datait/factset/fundamentals/basic"
    
    files = list_dir_contents(root)
    print(files)
    # download_files(root, jfs_path, files)

    # parse_downloaded_files(jfs_path)

    # file = "ff_basic_am_v3_full_8784.zip"
    # full_file_path = os.path.join(jfs_path, file)
    # unzip_file_contents(full_file_path)


    
    

