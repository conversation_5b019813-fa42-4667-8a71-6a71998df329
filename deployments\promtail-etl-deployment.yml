apiVersion: apps/v1
kind: Deployment
metadata:
  name: promtail
spec:
  replicas: 1
  selector:
    matchLabels:
      app: promtail
  template:
    metadata:
      labels:
        app: promtail
    spec:
      containers:
        - name: promtail
          image: 471112829829.dkr.ecr.us-east-1.amazonaws.com/grafana-promtail-image-revised:latest
          command: ["/usr/bin/promtail", "-config.file", "/etc/promtail/promtail.yaml", "-config.expand-env=true"]
          volumeMounts:
            - name: promtail-config
              mountPath: /etc/promtail
            - mountPath: /logs
              name: airflow-logs
      volumes:
        - name: promtail-config
          configMap:
            name: promtail-config
        - name: airflow-logs
          persistentVolumeClaim:
            claimName: common-logs-airflow