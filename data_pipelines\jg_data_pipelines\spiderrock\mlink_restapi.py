import requests
from enum import Enum
import json
import pandas as pd
from datetime import datetime
import pytz

url = "https://mlink-live.nms.saturn.spiderrockconnect.com/rest/json"
# url = "https://mlink-delay.nms.saturn.spiderrockconnect.com/rest/json"


class SpiderRockMessageType(Enum):
    OptionNbboQuote = "OptionNbboQuote"
    StockQuote = "StockQuote"

DICT_MARKET_STATUS = {
    "None": 0,             
    "PreOpen": 1,
    "PreCross": 2,             
    "Cross": 3,             
    "Open": 4,            
    "Closed": 5,             
    "Halted": 6,            
    "AfterHours": 7
}       

DICT_CALL_PUT = {
    "Call": "C",
    "Put": "P",
}
      
def get_nested_value(data, nested_key):
    keys = nested_key.split(".")
    for key in keys:
        if isinstance(data, dict):
            data = data.get(key)
        else:
            return None
    return data


field_mapping_options = """
{
    "SECURITY_CODE": "message_pkey_okey_tk",
    "UPDATE_TYPE": "message_updateType",
    "CALL_PUT": "message_pkey_okey_cp",
    "STRIKE_PRICE": "message_pkey_okey_xx",
    "EXPIRATION_DATE": "message_pkey_okey_dt",
    "BID_PRICE": "message_bidPrice",
    "BID_SIZE": "message_bidSize",
    "CUM_BID_SIZE": "message_cumBidSize",
    "BID_TIME": "message_bidTime",
    "ASK_PRICE": "message_askPrice",
    "ASK_SIZE": "message_askSize",
    "CUM_ASK_SIZE": "message_cumAskSize",
    "ASK_TIME": "message_askTime",
    "SR_STIM" : "header_sTim",
    "SR_ENCTIME": "header_encT",
    "SR_SRCTIMESTAMP": "message_srcTimestamp",
    "SR_NETTIMESTAMP": "message_netTimestamp"
}
"""

field_mapping_stocks = """
{
    "SECURITY_CODE": "message_pkey_ticker_tk",
    "UPDATE_TYPE": "message_updateType",
    "MARKET_STATUS": "message_marketStatus",
    "BID_PRICE": "message_bidPrice1",
    "BID_SIZE": "message_bidSize1",
    "ASK_PRICE": "message_askPrice1",
    "ASK_SIZE": "message_askSize1",
    "SR_STIM" : "header_sTim",
    "SR_ENCTIME": "header_encT",
    "SR_SRCTIMESTAMP": "message_srcTimestamp",
    "SR_NETTIMESTAMP": "message_netTimestamp"
}
"""

params_options = {
    # "apikey": "76334b43-38a4-4d92-bda3-fc3977c67afe",
    "apikey": "76334B43-38A4-4D92-BDA3-FC3977C67AFE",
    "cmd": "getmsgs",
    "msgtype": "OptionNbboQuote",
    "limit": "250000",
    "where": "okey.tk:eq:{ticker}",
}

params_stocks = {
    # "apikey": "76334b43-38a4-4d92-bda3-fc3977c67afe",
    "apikey": "76334B43-38A4-4D92-BDA3-FC3977C67AFE",
    "cmd": "getmsgs",
    "msgtype": "StockBookQuote",
    "where": "ticker:eq:{ticker}-NMS-EQT",
}

unique_key_path_stocks = "pkey.ticker"
unique_key_path_options = "pkey.okey"

unique_id_format_stocks = "{at}_{ts}_{tk}"
unique_id_format_options = "{at}_{ts}_{tk}_{dt}_{xx}_{cp}"

stock_quote_columns = {
    "SECURITY_CODE": "security_code",
    "PKEY": "pkey",
    "UPDATE_TYPE": "update_type",
    "MARKET_STATUS": "market_status",
    "BID_PRICE": "bid_price",
    "BID_SIZE": "bid_size",
    "ASK_PRICE": "ask_price",
    "ASK_SIZE": "ask_size",
    "SR_SRCTIMESTAMP": "sr_srctimestamp",
    "SR_NETTIMESTAMP": "sr_nettimestamp",
    "JG_API_REQ_TIMESTAMP": "jg_api_req_timestamp",
    "JG_API_REQ_TIMESTAMP_DATE": "jg_api_req_timestamp_date",
}

option_quote_columns = {
    "SECURITY_CODE": "security_code",
    "PKEY": "pkey",
    "CALL_PUT": "call_put",
    "STRIKE_PRICE": "strike_price",
    "EXPIRATION_DATE": "expiration_date",
    "UPDATE_TYPE": "update_type",
    "BID_PRICE": "bid_price",
    "BID_SIZE": "bid_size",
    "CUM_BID_SIZE": "cum_bid_size",
    "BID_TIME": "bid_time",
    "ASK_PRICE": "ask_price",
    "ASK_SIZE": "ask_size",
    "CUM_ASK_SIZE": "cum_ask_size",
    "ASK_TIME": "ask_time",
    "SR_SRCTIMESTAMP": "sr_srctimestamp",
    "SR_NETTIMESTAMP": "sr_nettimestamp",
    "JG_API_REQ_TIMESTAMP": "jg_api_req_timestamp",
    "JG_API_REQ_TIMESTAMP_DATE": "jg_api_req_timestamp_date",
}


def invoke_mlink_rest_api(tickers: list, messageTyp: SpiderRockMessageType):

    if not tickers:
        raise ValueError("Tickers list is empty")

    if messageTyp == SpiderRockMessageType.OptionNbboQuote:
        params = params_options.copy()
        field_mapping = field_mapping_options
        unique_key_path = unique_key_path_options
        unique_id_format = unique_id_format_options
        where_conditions = ["okey.tk:eq:{ticker}".format(ticker=t) for t in tickers]
        dict_columns = option_quote_columns
    elif messageTyp == SpiderRockMessageType.StockQuote:
        params = params_stocks.copy()
        field_mapping = field_mapping_stocks
        unique_key_path = unique_key_path_stocks
        unique_id_format = unique_id_format_stocks
        where_conditions = [
            "ticker:eq:{ticker}-NMS-EQT".format(ticker=t) for t in tickers
        ]
        dict_columns = stock_quote_columns
    else:
        raise ValueError("Invalid message type")

    field_mapping = json.loads(field_mapping)
    where_conditions = " | ".join(where_conditions)
    params["where"] = where_conditions
    # print(params)
    api_req_time = datetime.now(pytz.utc)
    response = requests.get(url, params=params)
    ref_date = api_req_time.date()
    # print(response.status_code)
    all_rows = []
    if response.status_code == 200:
        data = response.json()
        for msg in data:
            dict_message = msg["message"]
            # print(dict_message)
            if "pkey" not in dict_message.keys():
                continue
            okey_data = get_nested_value(dict_message, unique_key_path)
            unique_id = unique_id_format.format(**okey_data)
            row = {
                sf_column: get_nested_value(msg, kafka_key.replace("_", "."))
                for sf_column, kafka_key in field_mapping.items()
            }
            row["SR_SRCTIMESTAMP"] = datetime.fromtimestamp(
                row["SR_SRCTIMESTAMP"] / 1_000_000_000, pytz.utc
            )
            row["SR_NETTIMESTAMP"] = datetime.fromtimestamp(
                row["SR_NETTIMESTAMP"] / 1_000_000_000, pytz.utc
            )
            row["PKEY"] = unique_id
            all_rows.append(row)

        df_all_rows = pd.DataFrame(all_rows)
        df_all_rows["JG_API_REQ_TIMESTAMP"] = api_req_time
        df_all_rows["JG_API_REQ_TIMESTAMP_DATE"] = ref_date

        if "MARKET_STATUS" in df_all_rows.columns:
            idx_market_status = df_all_rows["MARKET_STATUS"].isna()
            df_all_rows.loc[idx_market_status, "MARKET_STATUS"] = "None"
            df_all_rows["MARKET_STATUS"] = df_all_rows["MARKET_STATUS"].map(DICT_MARKET_STATUS)

        if "CALL_PUT" in df_all_rows.columns:
            df_all_rows["CALL_PUT"] = df_all_rows["CALL_PUT"].map(DICT_CALL_PUT)

        if "STRIKE_PRICE" in df_all_rows.columns:
            df_all_rows["STRIKE_PRICE"] = df_all_rows["STRIKE_PRICE"] * 1000
            df_all_rows["STRIKE_PRICE"] = df_all_rows["STRIKE_PRICE"].astype("int32")

        df_all_rows = df_all_rows[dict_columns.keys()]
        df_all_rows.rename(columns=dict_columns, inplace=True)
        return df_all_rows, api_req_time, ref_date
    else:
        print("Response content:", response.text)
        raise ValueError("Request failed with status code:", response.status_code)