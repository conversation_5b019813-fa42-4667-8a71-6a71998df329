raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/ice/mft/1.0/"  ## Location of Raw Files
  s3_bucket: "jg-data-dp-vendor-data" ## S3 with Snowflake Acess
  s3_prefix: "ice/mft" ## Internal S3path to files

  # trailing *l* to support both _early and _final renamed csvs
  structure: '[
    "ICE_Futures_US-Energy/Futures/icefutures_EOD_IFED_futures_$DATE$*l*.csv",
    "ICE_Futures_US-Energy/Options/icefutures_EOD_IFED_options_$DATE$*l*.csv",
    "ICE_Futures_S2FOil/Futures/icefutures_EOD_S2FOil_futures_$DATE$*l*.csv",
    "ICE_Futures_S2FOil/Options/icefutures_EOD_S2FOil_options_$DATE$*l*.csv",

    "ICE_Endex/Futures/icefutures_EOD_endex_futures_$DATE$*l*.csv",
    "ICE_Endex/Options/icefutures_EOD_endex_options_$DATE$*l*.csv",
    "ICE_Futures_EU-Commodities/Futures/icefutures_EOD_EUcmdty_futures_$DATE$*l*.csv",
    "ICE_Futures_EU-Commodities/Options/icefutures_EOD_EUcmdty_options_$DATE$*l*.csv",

    "**/*_UtilityMarkets_*$DATE$.csv"
  ]'


snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "ICE_MFT"

  table_map:
    FUTURES_RAW:
      pattern: ".*icefutures_EOD_.*futures.*$DATE$.*(early|final).*" ## Need to be a regex format
      col_num: 18
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/mft/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT"

    OPTIONS_RAW:
      pattern: ".*icefutures_EOD_.*options.*$DATE$.*(early|final).*" ## Need to be a regex format
      col_num: 22
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/mft/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT"

    UTILMKT_FUTURES_RAW:
      pattern: ".*Futures.*_UtilityMarkets_.*$DATE$.*" ## Need to be a regex format
      col_num: 11
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/mft/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT"

    UTILMKT_OPTIONS_RAW:
      pattern: ".*Options.*_UtilityMarkets_.*$DATE$.*" ## Need to be a regex format
      col_num: 14
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/mft/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT"  
