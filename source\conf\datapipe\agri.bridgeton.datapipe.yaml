raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/usda_agri"
  s3_bucket: "jg-data-dp-vendor-data"
  s3_prefix: "usda-agri"
  include_prefix: true

  structure: '[
   "BRG_POS_$DATE$.csv",
   "BRG_LEVEL_$DATE$.csv",
   "COTS_LOTS_$DATE$.csv",
   "COTS_Notional_$DATE$.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "USDA_COMMOD"

  table_map:

    BRIDGETON_POSITIONS_RAW:
      pattern: "^BRG_POS_$DATE$.csv" 
      col_num: 6
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 

    BRIDGETON_LEVELS_RAW :
        pattern: "^BRG_LEVEL_$DATE$.csv" 
        col_num: 13
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    COT_LOTS_RAW :
        pattern: "^COTS_LOTS_$DATE$.csv" 
        col_num: 42
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    COT_NOTIONAL_RAW :
        pattern: "^COTS_Notional_$DATE$.csv" 
        col_num: 30
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 
