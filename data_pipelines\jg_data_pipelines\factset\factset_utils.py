import os
import re
import time
import tempfile
import zipfile
import pandas as pd
import numpy as np
from io import String<PERSON>
from copy import deepcopy
from datetime import datetime
from utils.file import temp_folder
from utils.sftp import SftpManager
from utils.date_utils import get_now
from factset.factset_configs import FILE_TABLE_MAP, SNOWFLAKE_CONN, SNOWFLAKE_BULK_LOADER_CON, FactsetDataType, OVERRIDES
from pytz import timezone

from utils.snowflake.adaptor import SnowflakeAdaptor
from factset.factset_bulk_loader import SnowflakeBulkLoader
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

SF_DATABASE=os.getenv("SF_DATABASE")
FACTSET_FTP_PASSWORD=os.getenv("FACTSET_FTP_PASSWORD")
SF_ROLE=os.getenv("SF_ROLE")
sftp_kwargs = {
    "host": "fts-sftp.factset.com",
    "port": 6671,
    "username": "jaingbl_qnt",
    "disable_hostkey": True,
    "password": FACTSET_FTP_PASSWORD
}

def get_format_for_version_regex(version_regex) -> str:
    rep_file_pat = version_regex.replace(r"(?P<version>\d+)", "{version}")
    return rep_file_pat.replace(r"\.", ".")
    
def _get_snowflake_adaptor_params(schema=None):
    sf_adaptor_params = deepcopy(SNOWFLAKE_CONN)
    
    if not schema:
        sf_adaptor_params.pop("schema", None)
    else:
        sf_adaptor_params["schema"] = schema

    return sf_adaptor_params

def _get_snowflake_bulk_loader_params(schema=None):
    sf_bulk_loader_params = deepcopy(SNOWFLAKE_BULK_LOADER_CON)
    
    if not schema:
        sf_bulk_loader_params.pop("schema", None)
    else:
        sf_bulk_loader_params["schema"] = schema
    
    return sf_bulk_loader_params

def _normalize_factset_inc_filename(filename):
    temp_f=re.sub(r'_\d+(?=\.txt)', '', filename)
    return temp_f.replace("_update", "").replace("_delete", "")

def get_last_processed_version(factset_data_type: FactsetDataType):
    sf_adaptor_params = _get_snowflake_adaptor_params()
    adaptor = SnowflakeAdaptor(**sf_adaptor_params)
    query = f"SELECT MAX(FS_VERSION) AS FS_VERSION FROM {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG WHERE FS_FILE_TYPE = '{factset_data_type.value}' AND IS_SUCCESS = 1"
    # print(query)
    logger.info(query)
    df_versions = adaptor.read_data(_get_snowflake_adaptor_params()["role"], query)
    if df_versions.shape[0] == 0:
        last_processed_version = None
    else:  
        last_processed_version = df_versions["FS_VERSION"].values[0]
    # logger.info(f"Last Processed Version for {factset_data_type.value}: {last_processed_version}")
    return last_processed_version

def download_files(root_dir, jfs_path, files):
    sftp_manager = SftpManager(**sftp_kwargs)
    for file in files:
        if not os.path.exists(os.path.join(jfs_path, file)):
            sftp_manager.fetch(file, root_dir, jfs_path)
            logger.info(f"Downloaded {file}.")
        else:
            logger.debug(f"Path {file} exists")
    sftp_manager.close()

def list_dir_contents(jfs_dir, root_dir, file_type):
    sftp_manager = SftpManager(**sftp_kwargs)
    fts_files = sftp_manager.get_directory_structure(path=root_dir, with_attributes=True)
    sftp_manager.close()

    files = {}
    for file_attr in fts_files:
        file_name = file_attr.filename
        if file_type and file_name.startswith(file_type) and not os.path.isfile(os.path.join(jfs_dir, file_name)):
            last_modified = file_attr.st_mtime
            files[file_name] = last_modified
        else:
            last_modified = file_attr.st_mtime
            files[file_name] = last_modified
    
    return files

def sync_dir(factset_dir, jfs_dir, file_type):
    factset_zips = list_dir_contents(jfs_dir, factset_dir, file_type.value)
    filenames = factset_zips.keys()
    for file in filenames:
        if not os.path.exists(os.path.join(jfs_dir, file)):
            print(f"Will sync {file} to {jfs_dir}")
    download_files(factset_dir, jfs_dir, factset_zips)
    # check_sequence(jfs_dir, filenames, file_type.value)
    
def check_sequence(jfs_dir, incoming_file_list=None, file_type=None):
    files_in_jfs = sorted(os.listdir(jfs_dir))
    all_sequences = [int(file.split('_')[-1].split('.')[0]) for file in files_in_jfs if file.startswith(file_type)]
    if incoming_file_list and file_type:
        sequences_in_ftp = [int(file.split('_')[-1].split('.')[0]) for file in incoming_file_list]
        all_sequences = set(all_sequences + sequences_in_ftp)
    missing_sequences = []
    if all_sequences:
        missing_sequences = [seq for seq in range(min(all_sequences), max(all_sequences) + 1) if seq not in all_sequences]
    if missing_sequences:
        logger.error(f"Missing file versions in {jfs_dir}: {missing_sequences}")
    else:
        logger.info(f"No file versions are missing in {jfs_dir}\n")

def find_minimum_unprocessed_file_version(files, file_regex, last_processed_version: int = 0):
    min_unprocessed_version = np.inf
    pattern = re.compile(file_regex)
    for file in files:
        match = pattern.search(file)
        if match:
            version = int(match.group('version'))
            if version > last_processed_version:
                if version < min_unprocessed_version:
                    min_unprocessed_version = version
    
    if np.isinf(min_unprocessed_version):
        return None
    else:
        return min_unprocessed_version    

def _get_file_mtime(jfs_file_path,tz=None):    
    if not os.path.exists(jfs_file_path):
        print(f'File not found:{jfs_file_path}')
        return None
    max_time=None
    with zipfile.ZipFile(jfs_file_path, 'r') as zipf:
        for zip_info in zipf.infolist():
            mod_time = datetime(*zip_info.date_time)
            if max_time is None or mod_time > max_time:
                max_time = mod_time
    if not tz is None:
        return max_time.astimezone(timezone(tz)).strftime("%Y-%m-%d %H:%M:%S")
    else:
        return max_time.strftime("%Y-%m-%d %H:%M:%S")

def find_incremental_file_versions_to_process(files, file_regex, last_processed_version: int):
    if not last_processed_version:
        raise ValueError("Last Processed Version is required - at the minimum that of the full file.")
    
    file_format = get_format_for_version_regex(file_regex)

    incremental_file_version = last_processed_version
    incremental_files_to_process = []

    while True:
        incremental_file_version = find_minimum_unprocessed_file_version(files, file_regex, incremental_file_version)
        if incremental_file_version is None:
            break
        else:           
            incremental_file_name = file_format.format(version=incremental_file_version)
            incremental_files_to_process.append((incremental_file_name, incremental_file_version))
    
    return incremental_files_to_process

def stage_inc_file_to_sf(jfs_dir, file_version, file_name, factset_data_type: FactsetDataType, cleanupStage=False):
    inc_file_path = os.path.join(jfs_dir, file_name)
    if not os.path.isfile(inc_file_path):
        raise ValueError(f"Not a file - {inc_file_path}!")
    
    sf_adaptor_params = _get_snowflake_adaptor_params()
    sf_adaptor = SnowflakeAdaptor(**sf_adaptor_params)

    sf_bulk_loader_params = _get_snowflake_bulk_loader_params("STAGING")
    sf_bulk_loader = SnowflakeBulkLoader(**sf_bulk_loader_params)
    
    mtime = _get_file_mtime(inc_file_path,'utc')
    num_columns=None
    process_start_time = get_now('UTC').strftime("%Y-%m-%d %H:%M:%S")
    logger.info(f"Processing {inc_file_path}, {file_version}, {mtime}")
    with tempfile.TemporaryDirectory() as temp_dir:
        with zipfile.ZipFile(inc_file_path, "r") as zip_ref:
            zip_ref.extractall(temp_dir)
        for temp_f in os.listdir(temp_dir):
            temp_file = temp_f
            src=os.path.join(temp_dir, temp_file)
            dst=os.path.join(temp_dir, temp_file.split(".")[0]+"_"+str(file_version)+"."+temp_file.split(".")[1])
            os.rename(src, dst)
            temp_f_path = dst
            normalized_fs_inc_filename = _normalize_factset_inc_filename(temp_file)
            if normalized_fs_inc_filename in FILE_TABLE_MAP.keys():
                table_name = FILE_TABLE_MAP[normalized_fs_inc_filename]
                ### CHECK FOR OVERRIDES
                factset_overrides = OVERRIDES.get(factset_data_type.value,{})
                encoding = factset_overrides.get("encoding", 'utf-8') if not table_name.lower().endswith("_coverage") else factset_overrides.get("encoding", 'windows-1252')
                with open(temp_f_path, "r", encoding=encoding) as infile:
                    lines = [line.replace('\\"', '""') for line in infile]
                with open(temp_f_path, "w", encoding=encoding) as outfile:
                    outfile.writelines(lines)
                with open(temp_f_path, "r", encoding=encoding) as f:
                    num_columns=len(f.readline().strip().split('|'))
                    row_count = 1+sum(1 for _ in f)

                if row_count < 2:
                    logger.info(f"Skipping {temp_f_path} as it does not have any data.")
                    continue

                stage_name = f"STAGING.STG_{table_name}"
                
                temp_f_with_version = temp_f.split(".")[0]+"_"+str(file_version)+"."+temp_f.split(".")[1]

                if cleanupStage:
                    sf_bulk_loader.cleanup_stg(stage_name, temp_f_with_version)
                sf_bulk_loader.put_file(temp_f_path, stage_name, stage_path=temp_f_with_version)
                logger.info(f"Loaded {temp_f_path} into {stage_name}.")
                process_end_time = get_now('UTC').strftime("%Y-%m-%d %H:%M:%S")
                # df_file_process_log = pd.DataFrame({"FS_FILE_TYPE": [factset_data_type.value], "FS_FILE_FULL_PATH": [inc_file_path], "FS_VERSION": [file_version], "FS_FILE_MTIME": [mtime], "FILE_PROCESS_START_TIME_UTC": [process_start_time], "FILE_PROCESS_END_TIME_UTC": [None], "LAST_STAGE": ["PUT"], "STAGE_NAME":"@"+stage_name+"/"+temp_f_with_version, "PUT_END_TIME_UTC": [process_end_time], "COPY_END_TIME_UTC": [None], "IS_SUCCESS": [False]})
                query = f"""
                INSERT INTO {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG(FS_FILE_TYPE,FS_FILE_FULL_PATH,FS_VERSION,FS_FILE_MTIME,IS_SUCCESS,LAST_STAGE,FILE_PROCESS_START_TIME_UTC,FILE_PROCESS_END_TIME_UTC,STAGE_NAME,PUT_END_TIME_UTC,COPY_END_TIME_UTC)
                WITH source_data AS (
                SELECT '{factset_data_type.value}' AS FS_FILE_TYPE,
                    '{inc_file_path}' AS FS_FILE_FULL_PATH,
                    '{file_version}' AS FS_VERSION,
                    '{mtime}' AS FS_FILE_MTIME,
                    '{process_start_time}' FILE_PROCESS_START_TIME_UTC,
                    NULL AS FILE_PROCESS_END_TIME_UTC,
                    'PUT' AS LAST_STAGE,
                    '{"@"+stage_name+"/"+temp_f_with_version}' AS STAGE_NAME,
                    '{process_end_time}' AS PUT_END_TIME_UTC,
                    NULL AS COPY_END_TIME_UTC,
                    False AS IS_SUCCESS
                )
                SELECT FS_FILE_TYPE,FS_FILE_FULL_PATH,FS_VERSION,FS_FILE_MTIME,IS_SUCCESS,LAST_STAGE,FILE_PROCESS_START_TIME_UTC,FILE_PROCESS_END_TIME_UTC,STAGE_NAME,PUT_END_TIME_UTC,COPY_END_TIME_UTC
                FROM  source_data;
                """
                # print(query)
                sf_adaptor.execute_query("CORE", query, SF_ROLE)                
                #sf_adaptor.upsert_df(df_file_process_log, f"{SF_DATABASE}.CORE", f"{SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG", match_columns=['STAGE_NAME'])
            else:
                logger.info(f"Skipping {temp_f_path} as it is not in the FILE_TABLE_MAP.")
    
   
    sf_adaptor.close()
    sf_bulk_loader.cleanup()
    return num_columns

def truncate_staging(factset_data_type: FactsetDataType):
    sf_adaptor_params = _get_snowflake_adaptor_params()
    sf_adaptor = SnowflakeAdaptor(**sf_adaptor_params)
    query = f"SELECT DISTINCT SPLIT(REPLACE(STAGE_NAME,'@STAGING.STG_',''),'/')[0]::STRING TABLE_NAME FROM {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG WHERE FS_FILE_TYPE = '{factset_data_type.value}' AND STAGE_NAME IS NOT NULL"

    df = sf_adaptor.read_data("CORE", query)
    
    table_list = df['TABLE_NAME'].tolist()
    for table_name in table_list:
        sf_adaptor.execute_query("STAGING", f"TRUNCATE TABLE {SF_DATABASE}.STAGING.{table_name};", SF_ROLE)
    
def copy_staged_file_into_staging(jfs_dir, file_version, file_name, factset_data_type: FactsetDataType,num_columns):
    inc_file_path = os.path.join(jfs_dir, file_name)
    mtime = _get_file_mtime(inc_file_path,'utc')
    process_start_time = get_now('UTC').strftime("%Y-%m-%d %H:%M:%S")

    sf_adaptor_params = _get_snowflake_adaptor_params()
    sf_adaptor = SnowflakeAdaptor(**sf_adaptor_params)
    
    sf_bulk_loader_params = _get_snowflake_bulk_loader_params("STAGING")
    sf_bulk_loader = SnowflakeBulkLoader(**sf_bulk_loader_params)
    
    query = f"SELECT * FROM {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG WHERE FS_FILE_TYPE = '{factset_data_type.value}' AND LAST_STAGE='PUT' AND FS_VERSION={file_version}"

    df = sf_adaptor.read_data("CORE", query)
    
    fs_file_list = df['STAGE_NAME'].tolist()
    
    for file in fs_file_list:
            logger.info(f"PROCESSING....{file}")
            temp_f = file.split("/")[1]
            stage_name = file.split("/")[0]
            table_name = stage_name.replace("@STAGING.STG_", "")
            
            factset_overrides = OVERRIDES.get(factset_data_type.value,{})
            num_columns=factset_overrides.get("num_columns_"+table_name, num_columns)

            sf_bulk_loader.load_file_with_mtime(table_name, f"STG_{table_name}", temp_f, delimiter="|", num_columns=num_columns,mtime=mtime,error_on_column_count_mismatch=False,file_version=file_version)
            process_end_time = get_now('UTC').strftime("%Y-%m-%d %H:%M:%S")
            df.loc[df['STAGE_NAME'] == file, 'COPY_END_TIME_UTC'] = process_end_time
            df.loc[df['STAGE_NAME'] == file, 'LAST_STAGE'] = 'COPY'
            query = f"""
            UPDATE {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG
            SET LAST_STAGE = 'COPY',
                COPY_END_TIME_UTC = '{process_end_time}'
            WHERE STAGE_NAME = '{file}'
            """
            # print(query)
            sf_adaptor.execute_query("CORE", query, SF_ROLE)                
            # sf_adaptor.upsert_df(df, f"{SF_DATABASE}.CORE", f"{SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG", match_columns=['STAGE_NAME'])
            
    sf_adaptor.close()
    sf_bulk_loader.cleanup()

def merge_into_core(load_type, factset_data_type: FactsetDataType,file_version=None):
    sf_adaptor_params = _get_snowflake_adaptor_params()
    sf_adaptor = SnowflakeAdaptor(**sf_adaptor_params)
    query=None
    insert_flag=False
    delete_flag=False
    query_del=None
    if load_type=="FULL":
        query = f"SELECT DISTINCT SPLIT(REPLACE(STAGE_NAME,'@STAGING.STG_',''),'/')[0]::STRING TABLE_NAME FROM {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG WHERE FS_FILE_TYPE = '{factset_data_type.value}' AND LAST_STAGE='COPY' AND STAGE_NAME IS NOT NULL"
    elif load_type=="INC":
        query = f"SELECT DISTINCT SPLIT(REPLACE(STAGE_NAME,'@STAGING.STG_',''),'/')[0]::STRING TABLE_NAME FROM {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG WHERE FS_FILE_TYPE = '{factset_data_type.value}' AND LAST_STAGE='COPY' AND FS_VERSION={file_version} AND STAGE_NAME IS NOT NULL"
    df = sf_adaptor.read_data("CORE", query)
    table_list = df['TABLE_NAME'].tolist()
    
    if load_type=="FULL":
        for table_name in table_list:
            if table_name.lower().startswith("sym_sedol_hist"):
                query=f"""
                INSERT INTO {SF_DATABASE}.CORE.{table_name}
                SELECT *, CURRENT_TIMESTAMP() AS WHEN_UPDATED_UTC FROM {SF_DATABASE}.STAGING.{table_name} sssh
                WHERE VERSION_NUMBER = (SELECT MAX(VERSION_NUMBER) FROM {SF_DATABASE}.STAGING.{table_name} sssh2
                                        WHERE  sssh2.SEDOL = sssh.SEDOL
                                        AND    sssh2.FILENAME NOT LIKE '%delete%')
                AND  NOT EXISTS        (SELECT * FROM {SF_DATABASE}.STAGING.{table_name} sssh3
                                        WHERE  sssh3.SEDOL         = sssh.SEDOL
                                        AND    sssh3.FILENAME LIKE '%delete%'
                                        AND    sssh3.VERSION_NUMBER > sssh.VERSION_NUMBER)
                """
            elif table_name.lower().startswith("sym_bbg"):
                query=f"""
                INSERT INTO {SF_DATABASE}.CORE.{table_name}
                SELECT *, CURRENT_TIMESTAMP() AS WHEN_UPDATED_UTC FROM {SF_DATABASE}.STAGING.{table_name} sssh
                WHERE VERSION_NUMBER = (SELECT MAX(VERSION_NUMBER) FROM {SF_DATABASE}.STAGING.{table_name} sssh2
                                        WHERE  sssh2.FSYM_ID = sssh.FSYM_ID
                                        AND    sssh2.BBG_ID = sssh.BBG_ID
                                        AND    sssh2.FILENAME NOT LIKE '%delete%')
                AND  NOT EXISTS        (SELECT * FROM {SF_DATABASE}.STAGING.{table_name} sssh3
                                        WHERE  sssh3.FSYM_ID         = sssh.FSYM_ID
                                        AND    sssh3.BBG_ID = sssh.BBG_ID
                                        AND    sssh3.FILENAME LIKE '%delete%'
                                        AND    sssh3.VERSION_NUMBER > sssh.VERSION_NUMBER)
                """
            elif table_name.lower().startswith("sym_ticker_region_hist"):
                query=f"""
                INSERT INTO {SF_DATABASE}.CORE.{table_name}
                SELECT *, CURRENT_TIMESTAMP() AS WHEN_UPDATED_UTC FROM {SF_DATABASE}.STAGING.{table_name} sssh
                WHERE VERSION_NUMBER = (SELECT MAX(VERSION_NUMBER) FROM {SF_DATABASE}.STAGING.{table_name} sssh2
                                        WHERE  sssh2.TICKER_REGION = sssh.TICKER_REGION
                                        AND    sssh2.FSYM_ID       = sssh.FSYM_ID
                                        AND    sssh2.START_DATE    = sssh.START_DATE
                                        AND    sssh2.FILENAME NOT LIKE '%delete%')
                AND  NOT EXISTS        (SELECT * FROM {SF_DATABASE}.STAGING.{table_name} sssh3
                                        WHERE  sssh3.TICKER_REGION = sssh.TICKER_REGION
                                        AND    sssh3.FSYM_ID       = sssh.FSYM_ID
                                        AND    sssh3.START_DATE    = sssh.START_DATE
                                        AND    sssh3.FILENAME LIKE '%delete%'
                                        AND    sssh3.VERSION_NUMBER > sssh.VERSION_NUMBER)
                """
            elif table_name.lower().startswith("sym_coverage"):
                query=f"""
                INSERT INTO {SF_DATABASE}.CORE.{table_name}
                SELECT FSYM_ID,CURRENCY,PROPER_NAME,FSYM_PRIMARY_EQUITY_ID,FSYM_PRIMARY_LISTING_ID,ACTIVE_FLAG,FREF_SECURITY_TYPE,FREF_LISTING_EXCHANGE,LISTING_FLAG,REGIONAL_FLAG,SECURITY_FLAG,FSYM_REGIONAL_ID,FSYM_SECURITY_ID,UNIVERSE_TYPE,FILENAME,VALID_FROM_UTC,
                    CASE WHEN FILENAME LIKE '%delete%' THEN VALID_FROM_UTC ELSE COALESCE(VALID_TO_UTC,'2099-12-31') END VALID_TO_UTC,
                    CURRENT_TIMESTAMP() AS WHEN_UPDATED_UTC
                FROM (
                SELECT FSYM_ID,CURRENCY,PROPER_NAME,FSYM_PRIMARY_EQUITY_ID,FSYM_PRIMARY_LISTING_ID,ACTIVE_FLAG,FREF_SECURITY_TYPE,FREF_LISTING_EXCHANGE,LISTING_FLAG,REGIONAL_FLAG,SECURITY_FLAG,FSYM_REGIONAL_ID,FSYM_SECURITY_ID,UNIVERSE_TYPE,FILENAME,VALID_FROM_UTC,
                    LEAD(VALID_FROM_UTC) OVER(PARTITION BY FSYM_ID
                                                ORDER BY FSYM_ID, VALID_FROM_UTC) VALID_TO_UTC
                FROM   {SF_DATABASE}.STAGING.{table_name}
                ORDER BY FSYM_ID, VALID_FROM_UTC, VALID_TO_UTC
                );
                """
            elif table_name.lower().startswith("ff_") and table_name.lower() != "ff_sec_coverage":
                query=f"""
                    INSERT INTO {SF_DATABASE}.CORE.{table_name}
                    SELECT * EXCLUDE (VALID_TO_UTC),
                           CASE WHEN FILENAME LIKE '%delete%' THEN VALID_FROM_UTC ELSE COALESCE(VALID_TO_UTC,'2099-12-31') END VALID_TO_UTC,
                           CURRENT_TIMESTAMP()
                    FROM (
                    SELECT * EXCLUDE (VERSION_NUMBER),
                        LEAD(VALID_FROM_UTC) OVER(PARTITION BY FSYM_ID, DATE
                                                    ORDER BY FSYM_ID, DATE, VALID_FROM_UTC) VALID_TO_UTC
                    FROM   {SF_DATABASE}.STAGING.{table_name}
                    );
                """
            elif table_name.lower() == "ff_sec_coverage":
                query=f"""
                    INSERT INTO {SF_DATABASE}.CORE.{table_name}
                    SELECT * EXCLUDE (VALID_TO_UTC),
                           CASE WHEN FILENAME LIKE '%delete%' THEN VALID_FROM_UTC ELSE COALESCE(VALID_TO_UTC,'2099-12-31') END VALID_TO_UTC,
                           CURRENT_TIMESTAMP()
                    FROM (
                    SELECT * EXCLUDE (VERSION_NUMBER),
                        LEAD(VALID_FROM_UTC) OVER(PARTITION BY FSYM_ID
                                                    ORDER BY FSYM_ID, VALID_FROM_UTC) VALID_TO_UTC
                    FROM   {SF_DATABASE}.STAGING.{table_name}
                    );
                """
            elif table_name.lower().startswith("fe_") and table_name.lower() != "fe_sec_coverage":
                query=f"""
                    INSERT INTO {SF_DATABASE}.CORE.{table_name}
                    SELECT * EXCLUDE (VALID_TO_UTC),
                           CASE WHEN FILENAME LIKE '%delete%' THEN VALID_FROM_UTC ELSE COALESCE(VALID_TO_UTC,'2099-12-31') END VALID_TO_UTC,
                           CURRENT_TIMESTAMP()
                    FROM (
                    SELECT * EXCLUDE (VERSION_NUMBER),
                        LEAD(VALID_FROM_UTC) OVER(PARTITION BY FSYM_ID, FE_ITEM, FE_FP_END, CONS_START_DATE
                                                    ORDER BY FSYM_ID, FE_ITEM, FE_FP_END, CONS_START_DATE, VALID_FROM_UTC) VALID_TO_UTC
                    FROM   {SF_DATABASE}.STAGING.{table_name}
                    );
                """
            elif table_name.lower() == "fe_sec_coverage":
                query=f"""
                    INSERT INTO {SF_DATABASE}.CORE.{table_name}
                    SELECT * EXCLUDE (VALID_TO_UTC),
                           CASE WHEN FILENAME LIKE '%delete%' THEN VALID_FROM_UTC ELSE COALESCE(VALID_TO_UTC,'2099-12-31') END VALID_TO_UTC,
                           CURRENT_TIMESTAMP()
                    FROM (
                    SELECT * EXCLUDE (VERSION_NUMBER),
                        LEAD(VALID_FROM_UTC) OVER(PARTITION BY FSYM_ID
                                                    ORDER BY FSYM_ID, VALID_FROM_UTC) VALID_TO_UTC
                    FROM   {SF_DATABASE}.STAGING.{table_name}
                    );
                """
            logger.info(query)
            sf_adaptor.execute_query("CORE", query, SF_ROLE)

            process_end_time = get_now('UTC').strftime("%Y-%m-%d %H:%M:%S")

            query_process_log = f"""
            UPDATE {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG
            SET LAST_STAGE = 'MERGE',
                FILE_PROCESS_END_TIME_UTC = '{process_end_time}',
                IS_SUCCESS = True
            WHERE FS_FILE_TYPE = '{factset_data_type.value}'
            """
            logger.info(query_process_log)
            sf_adaptor.execute_query("CORE", query_process_log, SF_ROLE)
    elif load_type=="INC":
        for table_name in table_list:
            if table_name.lower().startswith("sym_sedol_hist"):
                query = f"""
                MERGE INTO {SF_DATABASE}.CORE.{table_name} AS T 
                USING (SELECT * FROM {SF_DATABASE}.STAGING.{table_name}
                    WHERE VERSION_NUMBER = {file_version}
                    AND   FILENAME like '%update%') AS S
                ON    T.SEDOL = S.SEDOL 
                AND   S.VERSION_NUMBER = {file_version} AND S.FILENAME like '%update%'
                WHEN  MATCHED THEN 
                UPDATE SET T.FSYM_ID = S.FSYM_ID,
                           T.START_DATE = S.START_DATE,
                           T.END_DATE = S.END_DATE,
                           T.FILENAME = S.FILENAME,
                           T.VERSION_NUMBER = S.VERSION_NUMBER,
                           T.FILE_MTIME_UTC = S.FILE_MTIME_UTC,
                           T.WHEN_UPDATED_UTC = CURRENT_TIMESTAMP()
                WHEN NOT MATCHED THEN 
                INSERT (SEDOL,  FSYM_ID,  START_DATE,  END_DATE,  MOST_RECENT,  FILENAME,  VERSION_NUMBER,  FILE_MTIME_UTC, WHEN_UPDATED_UTC  )
                VALUES (S.SEDOL,S.FSYM_ID,S.START_DATE,S.END_DATE,S.MOST_RECENT,S.FILENAME,S.VERSION_NUMBER,S.FILE_MTIME_UTC, CURRENT_TIMESTAMP());
                """
                insert_flag=False
                delete_flag=True
                query_del = f"""
                DELETE FROM {SF_DATABASE}.CORE.{table_name}
                WHERE  SEDOL IN (SELECT SEDOL
                                 FROM   {SF_DATABASE}.STAGING.{table_name}
                                 WHERE  VERSION_NUMBER = {file_version}
                                 AND    FILENAME like '%delete%')
                """
            elif table_name.lower().startswith("sym_bbg"):
                query = f"""
                MERGE INTO {SF_DATABASE}.CORE.{table_name} AS T 
                USING (SELECT * FROM {SF_DATABASE}.STAGING.{table_name}
                    WHERE VERSION_NUMBER = {file_version}
                    AND   FILENAME like '%update%') AS S
                ON    T.FSYM_ID = S.FSYM_ID
                AND   T.BBG_ID = S.BBG_ID
                AND   S.VERSION_NUMBER = {file_version} AND S.FILENAME like '%update%'
                WHEN  MATCHED THEN 
                UPDATE SET T.BBG_TICKER = S.BBG_TICKER,
                           T.MOST_RECENT = S.MOST_RECENT,
                           T.FILENAME = S.FILENAME,
                           T.VERSION_NUMBER = S.VERSION_NUMBER,
                           T.FILE_MTIME_UTC = S.FILE_MTIME_UTC,
                           T.WHEN_UPDATED_UTC = CURRENT_TIMESTAMP()
                WHEN NOT MATCHED THEN 
                INSERT ( FSYM_ID, BBG_ID, BBG_TICKER,  MOST_RECENT,  FILENAME,  FILE_MTIME_UTC, VERSION_NUMBER, WHEN_UPDATED_UTC  )
                VALUES (S.FSYM_ID,S.BBG_ID,S.BBG_TICKER,S.MOST_RECENT,S.FILENAME,S.FILE_MTIME_UTC,S.VERSION_NUMBER, CURRENT_TIMESTAMP());
                """
                insert_flag=False
                delete_flag=True
                query_del = f"""
                DELETE FROM {SF_DATABASE}.CORE.{table_name}
                WHERE  (FSYM_ID, BBG_ID) IN (SELECT FSYM_ID, BBG_ID
                                 FROM   {SF_DATABASE}.STAGING.{table_name}
                                 WHERE  VERSION_NUMBER = {file_version}
                                 AND    FILENAME like '%delete%')
                """
            elif table_name.lower().startswith("sym_ticker_region_hist"):
                query = f"""
                MERGE INTO {SF_DATABASE}.CORE.{table_name} AS T 
                USING (SELECT * FROM {SF_DATABASE}.STAGING.{table_name}
                    WHERE VERSION_NUMBER = {file_version}
                    AND   FILENAME like '%update%') AS S
                ON    T.TICKER_REGION = S.TICKER_REGION AND T.FSYM_ID = S.FSYM_ID AND T.START_DATE = S.START_DATE
                AND   S.VERSION_NUMBER = {file_version} AND S.FILENAME like '%update%'
                WHEN  MATCHED THEN 
                UPDATE SET END_DATE = S.END_DATE,
                           FILENAME = S.FILENAME,
                           VERSION_NUMBER = S.VERSION_NUMBER,
                           FILE_MTIME_UTC = S.FILE_MTIME_UTC,
                           WHEN_UPDATED_UTC = CURRENT_TIMESTAMP()
                WHEN NOT MATCHED THEN 
                INSERT (TICKER_REGION,  FSYM_ID,  START_DATE,  END_DATE,  MOST_RECENT,  FILENAME,  VERSION_NUMBER,  FILE_MTIME_UTC, WHEN_UPDATED_UTC  )
                VALUES (S.TICKER_REGION,S.FSYM_ID,S.START_DATE,S.END_DATE,S.MOST_RECENT,S.FILENAME,S.VERSION_NUMBER,S.FILE_MTIME_UTC, CURRENT_TIMESTAMP());
                """
                insert_flag=False
                delete_flag=True
                query_del = f"""
                DELETE FROM {SF_DATABASE}.CORE.{table_name}
                WHERE  (TICKER_REGION, FSYM_ID, START_DATE) IN
                                (SELECT TICKER_REGION, FSYM_ID, START_DATE
                                 FROM  {SF_DATABASE}.STAGING.{table_name}
                                 WHERE  VERSION_NUMBER = {file_version}
                                 AND    FILENAME like '%delete%')
                """
            elif table_name.lower().startswith("sym_coverage"):
                query = f"""
                MERGE INTO {SF_DATABASE}.CORE.{table_name} AS T USING {SF_DATABASE}.STAGING.{table_name} AS S
                ON    T.FSYM_ID = S.FSYM_ID
                AND   T.VALID_TO_UTC = '2099-12-31'AND S.VERSION_NUMBER = {file_version}
                WHEN  MATCHED THEN 
                UPDATE SET T.VALID_TO_UTC = S.VALID_FROM_UTC,
                           T.WHEN_UPDATED_UTC = CURRENT_TIMESTAMP()
                """
                insert_flag=True
            elif table_name.lower().startswith("ff_") and table_name.lower() != "ff_sec_coverage":
                query = f"""
                MERGE INTO {SF_DATABASE}.CORE.{table_name} AS T USING {SF_DATABASE}.STAGING.{table_name} AS S
                ON    T.FSYM_ID = S.FSYM_ID and T.DATE = S.DATE
                AND   T.VALID_TO_UTC = '2099-12-31'AND S.VERSION_NUMBER = {file_version}
                WHEN  MATCHED THEN 
                UPDATE SET T.VALID_TO_UTC = S.VALID_FROM_UTC,
                           T.FILENAME     = S.FILENAME,
                           T.WHEN_UPDATED_UTC = CURRENT_TIMESTAMP()
                """
                insert_flag=True
            elif table_name.lower() == "ff_sec_coverage":
                query = f"""
                MERGE INTO {SF_DATABASE}.CORE.{table_name} AS T USING {SF_DATABASE}.STAGING.{table_name} AS S
                ON    T.FSYM_ID = S.FSYM_ID 
                AND   T.VALID_TO_UTC = '2099-12-31'AND S.VERSION_NUMBER = {file_version}
                WHEN  MATCHED THEN 
                UPDATE SET T.VALID_TO_UTC = S.VALID_FROM_UTC,
                           T.FILENAME = S.FILENAME,
                           T.WHEN_UPDATED_UTC = CURRENT_TIMESTAMP()
                """
                insert_flag=True
            elif table_name.lower().startswith("fe_") and table_name.lower() != "fe_sec_coverage":
                query = f"""
                MERGE INTO {SF_DATABASE}.CORE.{table_name} AS T USING {SF_DATABASE}.STAGING.{table_name} AS S
                ON    T.FSYM_ID = S.FSYM_ID and T.FE_ITEM = S.FE_ITEM and T.FE_FP_END = S.FE_FP_END and T.CONS_START_DATE = S.CONS_START_DATE
                AND   T.VALID_TO_UTC = '2099-12-31'AND S.VERSION_NUMBER = {file_version}
                WHEN  MATCHED THEN 
                UPDATE SET T.VALID_TO_UTC = S.VALID_FROM_UTC,
                           T.FILENAME = S.FILENAME,
                           T.WHEN_UPDATED_UTC = CURRENT_TIMESTAMP()
                """
                insert_flag=True
            elif table_name.lower() == "fe_sec_coverage":
                query = f"""
                MERGE INTO {SF_DATABASE}.CORE.{table_name} AS T USING {SF_DATABASE}.STAGING.{table_name} AS S
                ON    T.FSYM_ID = S.FSYM_ID 
                AND   T.VALID_TO_UTC = '2099-12-31'AND S.VERSION_NUMBER = {file_version}
                WHEN  MATCHED THEN 
                UPDATE SET T.VALID_TO_UTC = S.VALID_FROM_UTC,
                           T.FILENAME = S.FILENAME,
                           T.WHEN_UPDATED_UTC = CURRENT_TIMESTAMP()
                """
                insert_flag=True

            logger.info(query)
            sf_adaptor.execute_query("CORE", query, SF_ROLE)

            if insert_flag==True:
                ## Insert data for both delete and insert files.. this way the count of staging will be same as core.
                ## Delete file records will be inserted with start adn end date as the same
                query_ins = f"""
                INSERT INTO {SF_DATABASE}.CORE.{table_name}
                SELECT * EXCLUDE (VERSION_NUMBER),
                CASE WHEN FILENAME LIKE '%delete%' THEN VALID_FROM_UTC ELSE '2099-12-31' END VALID_TO_UTC,
                CURRENT_TIMESTAMP()
                FROM   {SF_DATABASE}.STAGING.{table_name}
                WHERE  VERSION_NUMBER = {file_version}
                """
                logger.info(query_ins)
                sf_adaptor.execute_query("CORE", query_ins, SF_ROLE)

            if delete_flag==True:
                logger.info(query_del)
                sf_adaptor.execute_query("CORE", query_del, SF_ROLE)

            process_end_time = get_now('UTC').strftime("%Y-%m-%d %H:%M:%S")
            query_process_log = f"""
            UPDATE {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG
            SET LAST_STAGE = 'MERGE',
                FILE_PROCESS_END_TIME_UTC = '{process_end_time}',
                IS_SUCCESS = True
            WHERE FS_FILE_TYPE = '{factset_data_type.value}'
            AND   FS_VERSION = {file_version}
            """
            logger.info(query_process_log)
            sf_adaptor.execute_query("CORE", query_process_log, SF_ROLE)

        # process_end_time = get_now('UTC').strftime("%Y-%m-%d %H:%M:%S")
        # df.loc[df['STAGE_NAME'] == file, 'FILE_PROCESS_END_TIME_UTC'] = process_end_time
        # df.loc[df['STAGE_NAME'] == file, 'LAST_STAGE'] = 'LOAD'
        # df.loc[df['STAGE_NAME'] == file, 'IS_SUCCESS'] = True
       
        # query = f"""
        # UPDATE {SF_DATABASE}.CORE.FS_FILE_PROCESS_LOG
        # SET LAST_STAGE = 'COPY',
        #     COPY_END_TIME_UTC = '{process_end_time}',
        #     IS_SUCCESS = TRUE
        # WHERE STAGE_NAME = '{file}'
        # """
        # # print(query)
        # sf_adaptor.execute_query("CORE", query, SF_ROLE)                

    sf_adaptor.close()    

def read_data_zip(zip_path):
    dfs = {}
    if os.path.exists(zip_path):
        try:
            with zipfile.ZipFile(zip_path, 'r') as z:
                with tempfile.TemporaryDirectory() as tmpdir:
                    z.extractall(tmpdir)
                    for file_name in z.namelist():
                        file_path = os.path.join(tmpdir, file_name)
                        if "_full_" not in zip_path and file_name.endswith('.txt') and file_name in FILE_TABLE_MAP.keys():
                            encoding = 'windows-1252' if '_coverage' in file_name.lower() else 'utf-8'
                            with open(file_path, 'r', encoding=encoding) as f:
                                df = pd.read_csv(f, sep='|', quotechar='"')
                                df["file_name"] = file_name
                                dfs[zip_path] = df
        except zipfile.BadZipFile:
            print(f"Invalid zip file: {zip_path}")
    else:
        print(f"File not found: {zip_path}")
    return dfs
def verify_missing_sequence_files(jfs_dir, factset_vars, factset_data_type: FactsetDataType):
    sf_adaptor_params = _get_snowflake_adaptor_params()
    sf_adaptor = SnowflakeAdaptor(**sf_adaptor_params)
    # last_processed_version = get_last_processed_version(factset_data_type) or 1
    last_processed_version = 1
    query = f"""WITH NUMS AS
    (SELECT {last_processed_version} AS RN UNION ALL SELECT RN + 1 FROM NUMS WHERE RN < (SELECT MAX(VERSION_NUMBER) FROM {SF_DATABASE}.STAGING.{factset_data_type.value}))
    SELECT * FROM NUMS WHERE RN NOT IN (SELECT COALESCE(SPLIT(SPLIT(FILENAME,'.')[0]::STRING,'_')[6]::INTEGER,0) FROM   {SF_DATABASE}.STAGING.{factset_data_type.value})"""

    logger.info(query)
    df = sf_adaptor.read_data("CORE", query)
    versions = df['RN'].tolist()
    for version in versions:
        full_file_format = get_format_for_version_regex(factset_vars["full_file_regex"])
        full_file_name = os.path.join(jfs_dir, full_file_format.format(version=version))
        inc_file_format = get_format_for_version_regex(factset_vars["incremental_file_regex"])
        inc_file_name = os.path.join(jfs_dir, inc_file_format.format(version=version))
        dfs = read_data_zip(full_file_name) if os.path.isfile(full_file_name) else read_data_zip(inc_file_name)
        for filename, df in dfs.items():
            if len(df) > 0:
                print(f"{filename} - {len(df)} records.")
                # print(df)
                # load_type = "FULL" if "_full_" in filename else "INC"
                # process_file((filename,version),jfs_dir,factset_data_type)
                # merge_into_core(load_type, factset_data_type,version)

if __name__ == "__main__":
    # basic_inc_regex = r"ff_basic_am_v3_(?P<version>\d+)\.zip"
    # fundamental_basic_files = os.listdir("/jfs/tech1/apps/rawdata/factset/trial/fundamentals/ff_basic_am_v3/")
    # # print(find_minimum_unprocessed_file_version(fundamental_basic_files, basic_inc_regex, 8827))
    # print(find_incremental_file_versions_to_process(fundamental_basic_files, basic_inc_regex, "ff_basic_am_v3_{version}.zip", 8827))

    import factset.factset_configs as fc

    # print(get_format_for_version_regex(fc.FUNDAMENTAL_BASIC_VARS["full_file_regex"]))
    assert get_format_for_version_regex(fc.FUNDAMENTAL_BASIC_VARS["full_file_regex"]) == "ff_basic_am_v3_full_{version}.zip"
    assert get_format_for_version_regex(fc.FUNDAMENTAL_BASIC_VARS["incremental_file_regex"]) == "ff_basic_am_v3_{version}.zip"
    assert get_format_for_version_regex(fc.FUNDAMENTAL_BASIC_DER_VARS["full_file_regex"]) == "ff_basic_der_am_v3_full_{version}.zip"
    assert get_format_for_version_regex(fc.FUNDAMENTAL_BASIC_DER_VARS["incremental_file_regex"]) == "ff_basic_der_am_v3_{version}.zip"
    assert get_format_for_version_regex(fc.SYM_BBG_VARS["full_file_regex"]) == "sym_bbg_v1_full_{version}.zip"

    # print(get_last_processed_version(fc.FactsetDataType.FUNDAMENTAL_ADVANCED))
