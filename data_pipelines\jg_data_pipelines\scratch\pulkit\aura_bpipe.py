from datetime import datetime
from datetime import timedelta, datetime
import atexit

import blpapi
import numpy as np
import pandas as pd
from pandas import DataFrame


current_date = datetime.today()
EXCEPTIONS = blpapi.Name("exceptions")
FIELD_ID = blpapi.Name("fieldId")
REASON = blpapi.Name("reason")
CATEGORY = blpapi.Name("category")
DESCRIPTION = blpapi.Name("description")

SessionConnectionDown = blpapi.Name("SessionConnectionDown")
SessionConnectionUp = blpapi.Name("SessionConnectionUp")
SessionTerminated = blpapi.Name("SessionTerminated")
ServiceDown = blpapi.Name("ServiceDown")
SlowConsumerWarning = blpapi.Name("SlowConsumerWarning")
SlowConsumerWarningCleared = blpapi.Name("SlowConsumerWarningCleared")
DataLoss = blpapi.Name("DataLoss")

ServiceName = blpapi.Name("serviceName")
# authorization
AUTHORIZATION_SUCCESS = blpapi.Name("AuthorizationSuccess")
AUTHORIZATION_FAILURE = blpapi.Name("AuthorizationFailure")
AUTHORIZATION_REVOKED = blpapi.Name("AuthorizationRevoked")
TOKEN_SUCCESS = blpapi.Name("TokenGenerationSuccess")
TOKEN_FAILURE = blpapi.Name("TokenGenerationFailure")
TOKEN = blpapi.Name("token")

g_session = None
g_sessionStarted = False
g_subscriptions = None
g_identity = None
g_authCorrelationId = None


class BloombergHandler:
    # use a Singleton to manage the session and query while running dashboard scripts
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BloombergHandler, cls).__new__(cls)
            cls._instance.session = cls._instance._start_session_server()
            atexit.register(cls._instance._close_session)
        return cls._instance

    def _start_session_server(self):
        options = blpapi.SessionOptions()
        # Note: SessionOptions.SessionName require SDK version 3.23.x or later
        options.sessionName = "Example Session"

        options.setServerHost("localhost")
        options.setServerPort(8194)
        print("Session options: %s" % options)
        
        session = blpapi.Session(options)

        # Start a Session
        if not session.start():
            raise Exception("Failed to start session.")

        service = "//blp/refdata"
        if not session.openService(service):
            print("Failed to open %s service" % service)
            return
        return session

    def _close_session(self):
        if self.session:
            self.session.stop()
            print("Bloomberg session closed.")

    def _get_different_type(self, p_datatype, p_field, p_fieldData):
        if p_datatype == blpapi.DataType.STRING:
            ret = p_fieldData.getElementAsString(p_field)
        elif p_datatype == blpapi.DataType.FLOAT64:
            ret = p_fieldData.getElementAsFloat(p_field)
        elif p_datatype == blpapi.DataType.INT32:
            ret = p_fieldData.getElementAsInteger(p_field)
        elif p_datatype == blpapi.DataType.DATE:
            ret = p_fieldData.getElementAsDatetime(p_field)
        elif p_datatype == blpapi.DataType.DATETIME:
            ret = p_fieldData.getElementAsDatetime(p_field)
        else:
            ret = np.nan
        return ret

    def fetch_live(
        self, tickers, fields, p_overrides: dict = None, p_pd_datetime: bool = False
    ) -> pd.DataFrame | None:
        """
        Fetch live prices, similar to BDP

        :param tickers:
        :param fields:
        :param p_overrides: overrides for the request
        :param p_pd_datetime: convert index to pandas datetime
        :return:
        """
        refDataService = self.session.getService("//blp/refdata")
        request = refDataService.createRequest("ReferenceDataRequest")

        if isinstance(tickers, str):
            tickers = [tickers]
        if isinstance(fields, str):
            fields = [fields]

        for ticker in tickers:
            request.getElement("securities").appendValue(ticker)
        for field in fields:
            request.getElement("fields").appendValue(field)
        if p_overrides:
            overrides = request.getElement("overrides")
            override1 = overrides.appendElement()
            for k, v in p_overrides.items():
                override1.setElement(k, v)

        self.session.sendRequest(request)
        data = {}
        while True:
            event = self.session.nextEvent(500)
            for msg in event:
                if msg.hasElement("securityData"):
                    securityDataArray = msg.getElement("securityData").values()
                    for securityData in securityDataArray:
                        ticker = securityData.getElementAsString("security")
                        fieldData = securityData.getElement("fieldData")
                        data[ticker] = dict()
                        for field in fields:
                            try:
                                if fieldData.hasElement(field):
                                    datatype = fieldData.getElement(field).datatype()
                                    data[ticker][field] = self._get_different_type(
                                        datatype, field, fieldData
                                    )
                            except:
                                data[ticker][field] = np.nan
                                continue
            if event.eventType() == blpapi.Event.RESPONSE:
                break
        data = pd.DataFrame.from_dict(data, orient="index")
        if p_pd_datetime:
            data.index = pd.to_datetime(data.index)
        return data

    def fetch_hist(
        self,
        p_ticker,
        p_field,
        p_start: datetime = None,
        p_end: datetime = None,
        p_opt_args: dict = None,
    ) -> DataFrame | None:
        """
        Fetch hist prices, similar to BDH

        :param p_ticker: str or list of tickers
        :param p_field: str or list of fields
        :param p_start: start date
        :param p_end: end date
        :param p_opt_args: other optional arguments. Similar to BDH()'s arguments after end date
        :return: DataFrame with requested data in long form.
        """
        # use HistoricalDataRequest
        ref_data_service = self.session.getService("//blp/refdata")
        request = ref_data_service.createRequest("HistoricalDataRequest")
        if isinstance(p_ticker, str):
            p_ticker = [p_ticker]
        if isinstance(p_field, str):
            p_field = [p_field]
        for this_ticker in p_ticker:
            request.getElement("securities").appendValue(this_ticker)
        for this_field in p_field:
            request.getElement("fields").appendValue(this_field)
        request.set("startDate", p_start.strftime("%Y%m%d"))
        request.set("endDate", p_end.strftime("%Y%m%d"))
        if p_opt_args is not None:
            for k, v in p_opt_args.items():
                request.set(k, v)
        request.set("periodicitySelection", "DAILY")

        self.session.sendRequest(request)

        data = dict()
        while True:
            event = self.session.nextEvent()
            for msg in event:
                if msg.hasElement("securityData"):
                    security_data = msg.getElement("securityData")
                    ticker = security_data.getElementAsString("security")
                    field_data = security_data.getElement("fieldData")
                    if ticker not in data:
                        data[ticker] = []
                    for fields in field_data.values():
                        date = fields.getElementAsDatetime("date")
                        row = {"date": date}
                        for this_field in p_field:
                            if fields.hasElement(this_field):
                                datatype = fields.getElement(this_field).datatype()
                                row[this_field] = self._get_different_type(
                                    datatype, this_field, fields
                                )
                                # row[this_field] = fields.getElementAsFloat(this_field)
                            else:
                                row[this_field] = np.nan
                        data[ticker].append(row)
            if event.eventType() == blpapi.Event.RESPONSE:
                break
        
        df_all_data = pd.DataFrame()
        for ticker, rows in data.items():
            df = pd.DataFrame(rows)
            if df.empty:
                continue
            df_melted = pd.melt(df, id_vars=["date"], value_vars=p_field, var_name="field", value_name="value")
            df_melted["ticker"] = ticker
            df_all_data = pd.concat([df_all_data, df_melted], ignore_index=True)
        
        df_all_data.reset_index(drop=True, inplace=True)

        return df_all_data
            

if __name__ == "__main__":
    bpipe_handle = BloombergHandler()
    df_hist = bpipe_handle.fetch_hist(
        ["BCOMEN Index", "BCOMAG Index", "BCOMIN Index", "BCOMPR Index"],
        ["PX_LAST", "PX_OPEN", "PX_HIGH", "PX_LOW"],
        datetime(2025, 1, 1),
        datetime(2025, 1, 31),
    )
    print(df_hist)
