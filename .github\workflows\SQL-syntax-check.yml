name: SQL Quality Check

on:
  workflow_dispatch:

jobs:
  lint-format:
    runs-on:
      group: core

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install SQLFluff
        run: pip install sqlfluff

      - name: Lint SQL Files
        run: sqlfluff lint --dialect snowflake .

      - name: Format Check (Dry Run)
        run: |
          sqlfluff fix --dialect snowflake --exclude-rules L009 --nocolor --force --output-file lint_report.sql
          git diff --exit-code || (echo "SQL format issues found. Please run 'sqlfluff fix'." && exit 1)

  syntax-check:
    runs-on:
      group: core
    needs: lint-format

    steps:
      - uses: actions/checkout@v4

      - name: Set up SnowSQL
        run: pip install snowflake-connector-python

      - name: Validate Syntax (Dry Run)
        env:
          SNOWFLAKE_USER: ${{ secrets.SNOWFLAKE_USER }}
          SNOWFLAKE_PASSWORD: ${{ secrets.SNOWFLAKE_PASSWORD }}
          SNOWFLAKE_ACCOUNT: ${{ secrets.SNOWFLAKE_ACCOUNT }}
        run: |
          for file in $(find . -name "*.sql"); do
            echo "Checking syntax in $file"
            snowsql -a $SNOWFLAKE_ACCOUNT -u $SNOWFLAKE_USER -q "BEGIN; $(cat $file); ROLLBACK;" || exit 1
          done
