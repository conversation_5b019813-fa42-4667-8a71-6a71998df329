{"catalogs": [{"group": "IT", "catalog": ".*", "allow": "all"}, {"group": "lake", "catalog": "iceberg", "allow": "read-only"}, {"group": "cache", "catalog": "iceberg", "allow": "read-only"}], "schemas": [{"group": "IT", "schema": ".*", "owner": true}, {"group": "lake", "catalog": "iceberg", "schema": "lake.*", "owner": false}, {"group": "cache", "catalog": "iceberg", "schema": "cache.*", "owner": false}], "tables": [{"group": "IT", "catalog": ".*", "privileges": ["SELECT"]}, {"catalog": "system", "privileges": ["SELECT"]}, {"group": "lake", "catalog": "iceberg", "schema": "lake.*", "privileges": ["SELECT"]}, {"group": "cache", "catalog": "iceberg", "schema": "cache.*", "privileges": ["SELECT"]}], "procedures": [{"group": "IT", "catalog": ".*", "schema": ".*", "procedure": ".*", "privileges": ["EXECUTE", "GRANT_EXECUTE"]}]}