from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor

from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from bloomberg.per_security.parser import BloombergParser
import pandas as pd
import tempfile
import shutil
import gzip
import os

from utils.date_utils import get_now, get_n_bday_yyyymmdd

if __name__ == "__main__":

    securities = [
        "N2EXAM Index",
        "N2EXH01 Index",
        "N2EXEAM Index",
        "N2EXH16 Index",
        "N2EXH25 Index",
        "N2EXEH01 Index",
        "N2EXEH02 Index",
        "N2EXEH03 Index",
        "N2EXEH04 Index",
        "N2EXEH05 Index",
        "N2EXEH06 Index",
        "N2EXEH07 Index",
        "N2EXEH08 Index",
        "N2EXEH09 Index",
        "N2EXEH10 Index",
        "N2EXEH11 Index",
        "N2EXEH12 Index",
        "N2EXEH13 Index",
        "N2EXEH14 Index",
        "N2EXEH15 Index",
        "N2EXEH16 Index",
        "N2EXEH17 Index",
        "N2EXEH18 Index",
        "N2EXEH19 Index",
        "N2EXEH20 Index",
        "N2EXEH21 Index",
        "N2EXEH22 Index",
        "N2EXEH23 Index",
        "N2EXEH24 Index",
        "N2EXEH25 Index",
        "N2EXH02 Index",
        "N2EXH03 Index",
        "N2EXH04 Index",
        "N2EXH05 Index",
        "N2EXH06 Index",
        "N2EXH07 Index",
        "N2EXH08 Index",
        "N2EXH09 Index",
        "N2EXH10 Index",
        "N2EXH11 Index",
        "N2EXH12 Index",
        "N2EXH13 Index",
        "N2EXH14 Index",
        "N2EXH15 Index",
        "N2EXH17 Index",
        "N2EXH18 Index",
        "N2EXH19 Index",
        "N2EXH20 Index",
        "N2EXH21 Index",
        "N2EXH22 Index",
        "N2EXH23 Index",
        "N2EXH24 Index"
    ]

    per_sec_req_type = PerSecurityRequestType.gethistory
    batch_name = "dp_n2ex"

    from_date = get_n_bday_yyyymmdd(15)
    to_date = get_n_bday_yyyymmdd(0)

    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "date_range": f"{from_date}|{to_date}",
        "sec_id": "TICKER",
        "fields": [
            "PX_LAST",
        ],
        "securities": securities,
    }

    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/commodities"
    risk_idx_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)

    risk_idx_file_name = os.path.basename(risk_idx_file_path)
    df_risk_idx = pd.DataFrame()
    with tempfile.TemporaryDirectory() as temp_dir:
        tmp_file_path = f"{temp_dir}/{risk_idx_file_name.replace('.gz', '')}"
        with gzip.open(risk_idx_file_path, "rb") as f_in:
            with open(tmp_file_path, "wb") as f_out:
                shutil.copyfileobj(f_in, f_out)

        parser = BloombergParser(
            tmp_file_path, sep="|", skipinitialspace=True, on_bad_lines="error"
        )
        df_risk_idx = parser.parse_data()

    if df_risk_idx.shape[0] == 0:
        raise ValueError("No data returned from Bloomberg")

    df_risk_idx.rename(
        columns={
            "Ticker": "BBG_TICKER",
            "Field": "FIELD",
            "Date": "DATE",
            "Value": "VALUE",
        },
        inplace=True,
    )
    df_risk_idx["DATE"] = pd.to_datetime(
        df_risk_idx["DATE"], format="%m/%d/%Y", errors="coerce"
    )
    df_risk_idx["VALUE"] = pd.to_numeric(df_risk_idx["VALUE"], errors="coerce")

    df_risk_idx["WHEN_UPDATED"] = get_now()

    adaptor = SnowparkAdaptor(
        database="BLOOMBERG",
        schema="BBGH_ONDEMAND",
        warehouse="BLOOMBERG_HUB_WH",
        role="DR_BBGH_OWNER",
    )

    ret_val = adaptor.upsert(
        df_risk_idx, "BBG_COMMODITY_N2EX_HIST", ["BBG_TICKER", "FIELD", "DATE"]
    )
    print(ret_val)
