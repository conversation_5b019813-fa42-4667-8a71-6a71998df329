"use strict";
class WebSocketClient {
    constructor(symbol, subscriberId, onMessageCallback) {
        this.firstConnection = true;
        this.isClosing = false;
        this.symbol = symbol;
        this.subscriberId = subscriberId;
        this.onMessageCallback = onMessageCallback;
        // Determine the correct protocol to use ('ws:' or 'wss:')
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        // Use window.location.host to get the hostname and port
        const wsHost = window.location.host;
        this.wsUrl = `${wsProtocol}//${wsHost}/ws/${this.symbol}/${this.subscriberId}`;
        this.connection = this.initWebSocket();
    }
    initWebSocket() {
        this.connection = new WebSocket(this.wsUrl);
        this.connection.onopen = () => {
            if (this.firstConnection) {
                Logger.log('WebSocket connection established');
                this.firstConnection = false;
            }
            else {
                Logger.log('WebSocket has successfully reconnected to ' + this.wsUrl);
                Logger.showStatus('WebSocket has successfully reconnected' + this.wsUrl, 'info', Config.get('succesfullStatusMessageDuration'));
            }
        };
        this.connection.onmessage = (event) => {
            const data = JSON.parse(event.data);
            if (this.onMessageCallback && typeof this.onMessageCallback === 'function') {
                this.onMessageCallback(data);
            }
            else {
                Logger.error('onMessageCallback is not a function');
            }
        };
        this.connection.onerror = (error) => {
            Logger.error('WebSocket error: ' + error.message);
        };
        this.connection.onclose = (event) => {
            Logger.log('WebSocket connection closed');
            if (!this.isClosing) {
                this.reconnect();
            }
        };
        window.onbeforeunload = () => {
            this.isClosing = true;
            this.connection.close();
        };
        return this.connection;
    }
    reconnect() {
        setTimeout(() => {
            Logger.log('Attempting to reconnect WebSocket...');
            this.initWebSocket();
        }, 1000); // Reconnect after 1 second delay
    }
}
