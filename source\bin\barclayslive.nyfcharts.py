import sys, os, glob, argparse
from strunner import *
setupEnvironment() # sets up environment
from jgdata import *
import stcommon
from stcommon.infra.python.module import function_from_path

parser = argparse.ArgumentParser()
parser.add_argument("--sd", help="Start Business Date YYYYMMDD", required=True, type=str)
parser.add_argument("--ed", help="End Business Date YYYYMMDD", required=True, type=str)
options = parser.parse_args()

if options.sd and options.ed:
   sd=options.sd
   ed=options.ed
else:
   print("Missing Arguments..")

dataset="barclayslive.nyfcharts"
import jgdata.datasets.barclayslive.nyfcharts
initDataset('barclayslive.nyfcharts')

prefix = 'jgdata.datasets'
funct = function_from_path(f"{prefix}.{dataset}","buildDataset")

funct(sd,ed)
