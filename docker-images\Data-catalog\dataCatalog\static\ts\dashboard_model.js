"use strict";
class DashboardModel extends ModelBase {
    constructor() {
        super();
        this.baseUrl = `${window.location.protocol}//${window.location.host}`;
        this.freqDisplay = "10"; // default display text
        // Define properties to enable automatic HTML updates when these properties change
        this.defineProperties();
    }
    defineProperties() {
        this.defineProperty("freqDisplay", "freqDisplay", true); // Indicates that we use textContent, not value
    }
}
