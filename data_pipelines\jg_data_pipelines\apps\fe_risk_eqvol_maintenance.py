import os
import numpy as np
import pandas as pd
import logging
import time
from utils.postgres.adaptor import PostgresAdaptor
from utils.date_utils import get_n_bday

from sqlalchemy import (
    create_engine,
    MetaData,
    Table,
    Column,
    Integer,
    String,
    Date,
    Float,
    Sequence,
    SmallInteger,
)
from sqlalchemy.dialects.postgresql import insert
from datetime import datetime, timedelta

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    loader = PostgresAdaptor(
        # host="tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com",
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    prev_bus_day = get_n_bday(1).strftime("%Y-%m-%d")
    delete_statement = f"delete from eqvol.sr_option_quotes_recent where jg_api_req_timestamp_date < '{prev_bus_day}'::TIMESTAMP;"
    logger.info(f"Starting to run delete statement: {delete_statement}")
    start_time = time.time()
    loader.execute_query_no_ret(delete_statement)
    elapsed_time = time.time() - start_time
    logger.info(f"Ran delete statement in {elapsed_time:.2f} seconds")

    # logger.info(f"Starting to run VACCUUM ANALYZE on eqvol.sr_option_quotes_recent")
    # start_time = time.time()
    # loader.execute_statement("VACUUM ANALYZE eqvol.sr_option_quotes_recent")
    # elapsed_time = time.time() - start_time
    # logger.info(f"Ran VACCUUM ANALYZE on eqvol.sr_option_quotes_recent in {elapsed_time:.2f} seconds")
