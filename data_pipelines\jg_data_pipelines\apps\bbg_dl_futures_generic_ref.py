from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from bloomberg.utils import get_bbg_full_ticker_for_futures

from bloomberg.per_security.parser import BloombergParser

from utils.date_utils import get_now, get_today

import pandas as pd
import numpy as np

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    df_securities = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """select CASE WHEN LEN(FUTURE_ROOT) < 2 THEN FUTURE_ROOT || ' ' 
            ELSE FUTURE_ROOT END 
        || 'A' || ' ' || MARKET_SECTOR AS GENERIC_FULL_TICKER
        from BLOOMBERG.BBGH_FUTURES.FUTURE_SERIES 
        WHERE ENABLE_GENERIC_REF = 1;
        """,
    )

    securities = df_securities["GENERIC_FULL_TICKER"].tolist()

    per_sec_req_type = PerSecurityRequestType.getdata
    batch_name = "dp_futgen"
    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "sec_id": "TICKER",
        "fields": [
            "ID_BB_GLOBAL",
        ],
        "securities": securities,
    }
    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_generic_ref/"
    fut_gen_ref_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)

    print(fut_gen_ref_file_path)

    parser = BloombergParser(fut_gen_ref_file_path, sep='|', skipinitialspace=True, on_bad_lines='error') 
    df_futures = parser.parse_data()

    idx_missing_id_bb_global = df_futures['ID_BB_GLOBAL'].isin([None, "", "nan", pd.NA, np.nan])
    
    df_futures = df_futures[~idx_missing_id_bb_global]
    df_futures.reset_index(drop=True, inplace=True)

    df_futures.rename(columns={"SECURITY": "GENERIC_FULL_TICKER"}, inplace=True)
    df_futures = df_futures[["GENERIC_FULL_TICKER", "ID_BB_GLOBAL"]]

    df_futures["LAST_UPDATED"] = get_now()
    df_futures["DATE"] = get_today()

    print(df_futures.head())

    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_FUTURES",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
    )

    ret_val = adaptor.upsert(df_futures, "FUTURE_GENERIC_REF", ["DATE", "GENERIC_FULL_TICKER"])
    print(ret_val)
