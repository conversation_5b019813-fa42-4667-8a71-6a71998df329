[Unit]
Description=CWIQFS
Documentation=https://codewilling.com
After=network-online.target sssd.service
Wants=network-online.target

[Service]
Type=simple
WorkingDirectory=/tmp
User={{ fsdb_login_id }}
ExecStartPre=-{{ fusermount3 }} -uz {{ mnt }}
ExecStart=/usr/bin/cwiqfs -f {{ mnt }} -o cache_size_mb={{ cache_size_mb }} -o {{ allow_mode }}  -o cwiqfs_yaml={{ config }} -o log_file={{ log_file }}  -o tmpdir={{ cache_dir }}  -o audit_log_file={{ audit_log_file }}
ExecStop=/bin/kill -9 $MAINPID
ExecStop=-{{ fusermount3 }} -uz {{ mnt }}
TimeoutSec=30
RestartSec=29
Restart=always

LimitNOFILE=30000
Nice=-19
LimitCORE=infinity
# LimitNPROC=30000
OOMScoreAdjust=-1000

# Comment TasksMax if your systemd version does not supports it.
# Only systemd 226 and above support this option.
# TasksMax=infinity

# KillMode=process

[Install]
WantedBy=multi-user.target
