{"api_url": "https://mlink-live.nms.saturn.spiderrockconnect.com/rest/json", "pg_host": "apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com", "message_types": {"OptionNbboQuote": {"params": {"apikey": "76334B43-38A4-4D92-BDA3-FC3977C67AFE", "cmd": "getmsgs", "msgtype": "OptionNbboQuote", "limit": "2000000", "where": "okey.tk:eq:{ticker}"}, "where_template": "okey.tk:eq:{ticker}", "field_mapping": {"TICKER": "message_pkey_okey_tk", "UPDATE_TYPE": "message_updateType", "CALL_PUT": "message_pkey_okey_cp", "STRIKE_PRICE": "message_pkey_okey_xx", "EXPIRATION_DATE": "message_pkey_okey_dt", "BID_PRICE": "message_bidPrice", "BID_SIZE": "message_bidSize", "CUM_BID_SIZE": "message_cumBidSize", "BID_TIME": "message_bidTime", "BID_MKT_TYPE": "message_bidMktType", "ASK_PRICE": "message_askPrice", "ASK_SIZE": "message_askSize", "CUM_ASK_SIZE": "message_cumAskSize", "ASK_TIME": "message_askTime", "ASK_MKT_TYPE": "message_askMktType", "SR_STIM": "header_sTim", "SR_ENCTIME": "header_encT", "SR_SRCTIMESTAMP": "message_srcTimestamp", "SR_NETTIMESTAMP": "message_netTimestamp"}, "unique_key_path": "pkey.okey", "columns": {"TICKER": "ticker", "CALL_PUT": "call_put", "STRIKE_PRICE": "strike_price", "EXPIRATION_DATE": "expiration_date", "UPDATE_TYPE": "update_type", "BID_PRICE": "bid_price", "BID_SIZE": "bid_size", "CUM_BID_SIZE": "cum_bid_size", "BID_TIME": "bid_time", "BID_MKT_TYPE": "bid_mkt_type", "ASK_PRICE": "ask_price", "ASK_SIZE": "ask_size", "CUM_ASK_SIZE": "cum_ask_size", "ASK_TIME": "ask_time", "ASK_MKT_TYPE": "ask_mkt_type", "SR_SRCTIMESTAMP": "sr_srctimestamp", "SR_NETTIMESTAMP": "sr_nettimestamp", "JG_API_RECVD_TIMESTAMP": "jg_api_recvd_timestamp", "JG_API_REQ_TIMESTAMP": "jg_api_req_timestamp"}}, "TickerDefinition": {"params": {"apikey": "76334B43-38A4-4D92-BDA3-FC3977C67AFE", "cmd": "getmsgs", "msgtype": "TickerDefinition", "view": "securityID|symbolType|name|country|primaryCurrency|rateCurve|parValue|parValueCurrency|pointValue|pointCurrency|primaryExch|altID|mic|micSeg|symbol|issueClass|sharesOutstanding|cusip|indNum|subNum|grpNum|nbrNum|sic|cik|gics|lei|naics|cfi|cic|fisn|isin|figi|bbgCompositeTicker|bbgExchangeTicker|bbgCompositeGlobalID|bbgGlobalID|bbgCurrency|otcPrimaryMarket|otcTier|otcReportingStatus|otcDisclosureStatus|otcFlags|stkPriceInc|tkDefSource|statusFlag|tapeCode|stkVolume|futVolume|optVolume|exchString|hasOptions|numOptions|basisKey|reverseSkew|timeMetric|tradingPeriod|regionalCompositeTicker|timestamp", "where": "(symboltype:eq:ETF|symboltype:eq:Equity|symboltype:eq:CashIndex|symboltype:eq:ADR)&(ticker.ts:eq:NMS)", "limit": 250000}}, "RootDefinition": {"params": {"apikey": "76334B43-38A4-4D92-BDA3-FC3977C67AFE", "cmd": "getmsgs", "msgtype": "RootDefinition", "view": "ticker|osiRoot|ccode|uPrcDriverKey|uPrcDriverType|uPrcDriverKey2|uPrcDriverType2|uPrcBoundCCode|expirationMap|underlierMode|optionType|multihedge|exerciseTime|exerciseType|timeMetric|tradingPeriod|pricingModel|moneynessType|priceQuoteType|volumeTier|positionLimit|exchanges|tickValue|pointValue|pointCurrency|strikeScale|strikeRatio|cashOnExercise|underliersPerCn|premiumMult|symbolRatio|adjConvention|optPriceInc|priceFormat|minTickSize|tradeCurr|settleCurr|strikeCurr|defaultSurfaceRoot|ricRoot|bbgRoot|bbgGroup|regionalCompositeRoot|timestamp", "limit": 250000}}}}