from data_analyzer.amazon_review.amazon_review_search  import AmazonReviewSearch

import sys

def main():
    api_key = "***************************************************"
    input_datapath = "data/Reviews.csv"
    output_datapath = "data/fine_food_reviews_with_embeddings_1k"

    amazon_review_search = AmazonReviewSearch(api_key, input_datapath, output_datapath, debug=True)
    
    try:
        amazon_review_search.check_data()
        amazon_review_search.process_embeddings()
        amazon_review_search.search('delicious beans')
        amazon_review_search.visualize_embeddings()
        amazon_review_search.perform_regression()
        amazon_review_search.classify_and_evaluate()
        amazon_review_search.plot_precision_recall()
        amazon_review_search.find_and_visualize_clusters()
        amazon_review_search.summarize_clusters()
        amazon_review_search.start_server()
    except FileNotFoundError as e:
        print(e)

if __name__ == "__main__":
    main()
