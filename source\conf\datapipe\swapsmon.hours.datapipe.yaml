raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/swapsmon/1.0"  ## Location of Compress Raw Files
  s3_bucket: "jg-tech1dev-dp-snowflake-poc-data" ## S3 with Snowflake Acess
  s3_prefix: "swapsmon/hours" ## Internal S3path to files
  include_prefix: true
  structure: '{
      "hours/JAINTH*$DATE$*.TAR.GZ": [
          "*.CSV"
      ],
      "refdata/cities15000.zip":["*.txt"]
  }'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "SWAPSMON_HOURS"

  table_map:
    JAINTH:
      pattern: ".*$DATE$.*JAINTH.*" ## Need to me a regex format
      col_num: 19
      metadata_columns: ["filename","file_last_modified"]
      stage_path: "swapsmon/hours/" ##<stage name>/<stage path>
      file_format: "ff_swapsmon_hours"
    CITIES:
      pattern: ".*$DATE$.*CITIES.*" ## Need to me a regex format
      col_num: 6
      metadata_columns: ["filename","file_last_modified"]
      stage_path: "swapsmon/hours/" ##<stage name>/<stage path>
      file_format: "ff_swapsmon_hours"
    FUNC_INFO:
      pattern: ".*$DATE$.*S\\\\d{5}_FUNC_INFO.*" ## Need to me a regex format
      col_num: 2
      metadata_columns: ["filename","file_last_modified"]
      stage_path: "swapsmon/hours/" ##<stage name>/<stage path>
      file_format: "ff_swapsmon_hours"
    INFO:
      pattern: ".*$DATE$.*S\\\\d{5}_INFO.*" ## Need to me a regex format
      col_num: 15
      metadata_columns: ["filename","file_last_modified"]
      stage_path: "swapsmon/hours/" ##<stage name>/<stage path>
      file_format: "ff_swapsmon_hours"
    SETTLE_INFO:
      pattern: ".*$DATE$.*S\\\\d{5}_SETTLE_INFO.*" ## Need to me a regex format
      col_num: 17
      metadata_columns: ["filename","file_last_modified"]
      stage_path: "swapsmon/hours/" ##<stage name>/<stage path>
      file_format: "ff_swapsmon_hours"
    HOLCODEHISTORY:
      pattern: ".*$DATE$.*HOLCODEHISTORY.*" ## Need to me a regex format
      col_num: 5
      metadata_columns: ["filename","file_last_modified"]
      stage_path: "swapsmon/hours/" ##<stage name>/<stage path>
      file_format: "ff_swapsmon_hours"
    CODES:
      pattern: ".*$DATE$.*CODES.*" ## Need to me a regex format
      col_num: 2
      metadata_columns: ["filename","file_last_modified"]
      stage_path: "swapsmon/hours/" ##<stage name>/<stage path>
      file_format: "ff_swapsmon_hours"
    CITIES15000:
      pattern: ".*cities15000.*" ## Need to me a regex format
      col_num: 19
      metadata_columns: ["filename","file_last_modified"]
      stage_path: "swapsmon/hours/" ##<stage name>/<stage path>
      file_format: "ff_swapsmon_cities15000"