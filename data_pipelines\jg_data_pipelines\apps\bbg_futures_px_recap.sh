echo "Starting Futures Price Recap Process"

source $HOME/.bashrc

set -e

echo "Refreshing futures prices from Bbg DL"
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_futures_px.py >> /home/<USER>/logs/bbg_dl_futures_px.log 2>&1

echo "Sending Email Notification"
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/send_notification.py "Futures Price Recap process complete" "<EMAIL>,<EMAIL>"

echo "Completed Futures Price Recap Process"
