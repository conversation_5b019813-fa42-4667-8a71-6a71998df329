apiVersion: v1
kind: Secret
metadata:
  name: secret
  namespace: default
type: opaque
data:
  aws-access-key-secret: ${AWS_ACCESS_KEY_ID}
  aws-secret-access-key-secret: ${AWS_SECRET_ACCESS_KEY}
  aws-session-token-secret: ${AWS_SESSION_TOKEN}
  openmetadata-airflow-password: ${AIRFLOW_PASSWORD}
  openmetadata-elasticsearch-password: ${OPENSEARCH_PASSWORD}
  openmetadata-postgresql-password: ${RDS_PASSWORD}