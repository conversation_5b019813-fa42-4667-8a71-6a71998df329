from datetime import timedelta
from airflow import DAG

try:
    from airflow.operators.python import PythonVirtualenvOperator
except ModuleNotFoundError:
    from airflow.operators.python_operator import PythonVirtualenvOperator

from airflow.utils.dates import days_ago

default_args = {
    "owner":"airflow",
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 3,
    "retry_delay": timedelta(seconds=10),
    "execution_timeout": timedelta(minutes=60)
}

def metadata_ingestion_workflow():
    from metadata.workflow.metadata import MetadataWorkflow
    from metadata.workflow.workflow_output_handler import print_status
    import yaml
    config = """
      source:
        type: airflow
        serviceName: airflow_source
        serviceConnection:
          config:
            type: Airflow
            hostPort: http://a75dcc9bb29af4686a607e39041e91d1-1192139953.us-east-2.elb.amazonaws.com:8080
            numberOfStatus: 10
            connection:
              type: Postgres
              username: airflow
              password: airflow
              databaseSchema: airflow
              hostPort: postgresql+psycopg2://airflow:airflow@service-postgres:5432
        sourceConfig:
          config:
            type: PipelineMetadata
            markDeletedPipelines: True
            includeTags: True
            includeLineage: true
      sink:
        type: metadata-rest
        config: {}
      workflowConfig:
        loggerLevel: DEBUG  # DEBUG, INFO, WARNING or ERROR
        openMetadataServerConfig:
          hostPort: "http://aba125ad6b24949e3a67634c8fa5602a-646501548.us-east-2.elb.amazonaws.com:8585/api"
          authProvider: openmetadata
          securityConfig:
            jwtToken: #OPENMETADATA_JWT_TOKEN#
      """

    workflow_config = yaml.safe_load(config)
    workflow = MetadataWorkflow.create(workflow_config)
    workflow.execute()
    workflow.raise_from_status()
    print_status(workflow)
    workflow.stop()


with DAG(
    "ingestion_dag",
    default_args=default_args,
    description="example DAG which runs a OpenMetadata ingestion workflow ",
    start_date=days_ago(1),
    is_paused_upon_creation=True,
    catchup=False,
) as dag:
    ingest_task = PythonVirtualenvOperator(
        task_id="openmetadata_ingestion",
        system_site_packages=False,
        python_version="3.8",
        python_callable=metadata_ingestion_workflow
    )