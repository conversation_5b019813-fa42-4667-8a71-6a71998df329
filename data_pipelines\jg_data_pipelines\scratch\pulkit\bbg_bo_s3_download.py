import boto3
from botocore.exceptions import ClientError
import logging
from typing import List, Optional
import os

from botocore.exceptions import NoCredentialsError

# Replace the following placeholders with your own values
from botocore.config import Config
# Specify the path to your custom credentials file
# custom_credentials_path = '.credentials'

# os.environ['AWS_SHARED_CREDENTIALS_FILE'] = custom_credentials_path
# session = boto3.Session()
# credentials = session.get_credentials()
# current_credentials = credentials.get_frozen_credentials()

# def download_file(bucket, folder):
#     try:
#         s3 = boto3.resource('s3')
#         bucket_obj = s3.Bucket(bucket)
#         local_path = ""
#         for obj in bucket_obj.objects.filter(Prefix=folder):
#             local_file_path = os.path.join(local_path, obj.key)
#             print(local_file_path)
#             if not os.path.exists(os.path.dirname(local_file_path)):
#                 os.makedirs(os.path.dirname(local_file_path))
#             bucket_obj.download_file(obj.key, local_file_path)
#             print(f"Downloaded {obj.key} from {bucket} to {local_file_path}")
            
#     except NoCredentialsError:
#         print("Invalid AWS credentials")
#     except Exception as e:
#         print(f"Error downloading folder: {e}")

def list_files(bucket, prefix, date):
    try:
        extra_kwargs = {'RequestPayer': 'requester'}
        s3_client = boto3.client('s3')
        s3 = boto3.resource('s3')
        bucket_obj = s3.Bucket(bucket)
        # bucket_obj = s3.Bucket(bucket)
        # for obj in bucket_obj.objects.filter(Prefix=folder, **extra_kwargs):
        #     print(obj)
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(
        Bucket=bucket,
        Prefix=prefix,
        Delimiter='/',
        **extra_kwargs
        )
    
        for page in pages:
            # Common prefixes represent directories
            if 'CommonPrefixes' in page:
                for prefix in page['CommonPrefixes']:
                    prefix_path = prefix["Prefix"]
                    prefixes = prefix_path.split("/")
                    folder_name = prefixes[-2]
                    folder_name = f"{folder_name}/"
                    full_path = f"{prefix_path}snapshots/{date}/"
                    print(full_path)
                    print(folder_name)
                    local_file_path = os.path.join("/jfs/tech1_share/pulkit.vora/jsvc.datait/bbg_bo_options", date, folder_name)
                    print(local_file_path)
                    os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
                        
                    
                    for obj in bucket_obj.objects.filter(Prefix=full_path, **extra_kwargs):
                        if full_path == obj.key:
                            print(f"Skipping root {obj.key}")
                            continue
                        file_name = obj.key.split("/")[-1]
                        # print(local_file_path)
                        # print(file_name)
                        # print(obj.key)
                        local_file = f"{local_file_path}/{file_name}"
                        # print(local_file)
                        # local_file = os.path.join(local_file_path, file_name)
                        # print(local_file)
                        # bucket_obj.download_file(obj.key, local_file_path, ExtraArgs=extra_kwargs)
                        s3_client.download_file(Bucket=bucket, Key=obj.key, Filename=local_file, ExtraArgs=extra_kwargs)

    except NoCredentialsError:
        print("Invalid AWS credentials")
    except Exception as e:
        print(f"Error downloading folder: {e}")


def download_full_archives(bucket, prefix, target):
    try:
        extra_kwargs = {'RequestPayer': 'requester'}
        s3_client = boto3.client('s3')
        s3 = boto3.resource('s3')
        bucket_obj = s3.Bucket(bucket)
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(
        Bucket=bucket,
        Prefix=prefix,
        Delimiter='/',
        **extra_kwargs
        )
    
        for page in pages:
            # Common prefixes represent directories
            prefix_path = page["Prefix"]
            for obj in bucket_obj.objects.filter(Prefix=prefix_path, **extra_kwargs):
                # print(obj.key)
                file_name = obj.key.split("/")[-1]
                distr = obj.key.split("/")[-2]
                date = obj.key.split("/")[-3]
                # print(distr)
                # print(date)
                local_file = f"{target}/{date}/{distr}/{file_name}"
                print(f"Downloading {local_file}")
                # Check if the file exists, if it exists don't download.
                if not os.path.exists(local_file):
                    os.makedirs(os.path.dirname(local_file), exist_ok=True)
                    s3_client.download_file(Bucket=bucket, Key=obj.key, Filename=local_file, ExtraArgs=extra_kwargs)
                else:
                    print(f"Already exists, skipping {local_file}")

                
    except NoCredentialsError:
        print("Invalid AWS credentials")
    except Exception as e:
        print(f"Error downloading folder: {e}")

def download_dated_folders(bucket, prefix, target):
    try:
        extra_kwargs = {'RequestPayer': 'requester'}
        s3_client = boto3.client('s3')
        s3 = boto3.resource('s3')
        bucket_obj = s3.Bucket(bucket)
        # bucket_obj = s3.Bucket(bucket)
        # for obj in bucket_obj.objects.filter(Prefix=folder, **extra_kwargs):
        #     print(obj)
        paginator = s3_client.get_paginator('list_objects_v2')
        pages = paginator.paginate(
        Bucket=bucket,
        Prefix=prefix,
        Delimiter='/',
        **extra_kwargs
        )
    
        for page in pages:
            # Common prefixes represent directories
            if 'CommonPrefixes' in page:
                for prefix in page['CommonPrefixes']:
                    prefix_path = prefix["Prefix"]
                    prefixes = prefix_path.split("/")
                    folder_name = prefixes[-2]
                    folder_name = f"{folder_name}/"
                    full_path = f"{prefix_path}snapshots/{date}/distributions/"
                    print(full_path)
                    print(folder_name)
                    local_file_path = os.path.join("/jfs/tech1_share/pulkit.vora/jsvc.datait/bbg_bo_options", date, folder_name)
                    print(local_file_path)
                    os.makedirs(os.path.dirname(local_file_path), exist_ok=True)
                        
                    
                    for obj in bucket_obj.objects.filter(Prefix=full_path, **extra_kwargs):
                        if full_path == obj.key:
                            print(f"Skipping root {obj.key}")
                            continue
                        file_name = obj.key.split("/")[-1]
                        # print(local_file_path)
                        # print(file_name)
                        # print(obj.key)
                        local_file = f"{local_file_path}/{file_name}"
                        # print(local_file)
                        # local_file = os.path.join(local_file_path, file_name)
                        # print(local_file)
                        # bucket_obj.download_file(obj.key, local_file_path, ExtraArgs=extra_kwargs)
                        s3_client.download_file(Bucket=bucket, Key=obj.key, Filename=local_file, ExtraArgs=extra_kwargs)

    except NoCredentialsError:
        print("Invalid AWS credentials")
    except Exception as e:
        print(f"Error downloading folder: {e}")



if __name__ == "__main__":
    # date = "20250212"
    s3_access_point_arn = "arn:aws:s3:us-east-1:695011528394:accesspoint/dl-c0a4e143-ae06-4a08-b0ed-5483f78a53ec"
    
    # target = "/jfs/tech1/apps/rawdata/bloomberg/options/1.0/equityOptionsMorning1Namr2/archives"
    # download_full_archives(s3_access_point_arn, "GuXDxca3/catalogs/bbg/datasets/equityOptionsMorning1Namr2/archives/", target)

    # target = "/jfs/tech1/apps/rawdata/bloomberg/options/1.0/equityOptionsMorning1Namr2Deltas"
    # download_full_archives(s3_access_point_arn, "GuXDxca3/catalogs/bbg/datasets/equityOptionsMorning1Namr2Deltas/", target)

    # target = "/jfs/tech1/apps/rawdata/bloomberg/options/1.0/equityOptionNamr/archives"
    # download_full_archives(s3_access_point_arn, "GuXDxca3/catalogs/bbg/datasets/equityOptionNamr/archives/", target)

    # target = "/jfs/tech1/apps/rawdata/bloomberg/options/1.0/equityOptionNamrPricing/archives"
    # download_full_archives(s3_access_point_arn, "GuXDxca3/catalogs/bbg/datasets/equityOptionNamrPricing/archives/", target)

    target = "/jfs/tech1/apps/rawdata/bloomberg/options/1.0/equityOptionNamrDifferences/snapshots"
    download_full_archives(s3_access_point_arn, "GuXDxca3/catalogs/bbg/datasets/equityOptionNamrDifferences/snapshots/", target)
    
    # list_files(s3_access_point_arn, 'GuXDxca3/catalogs/bbg/datasets/equityOptions', date)
