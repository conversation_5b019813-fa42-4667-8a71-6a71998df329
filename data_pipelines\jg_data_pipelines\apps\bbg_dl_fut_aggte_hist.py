from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor

from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from bloomberg.per_security.parser import BloombergParser
import pandas as pd
import tempfile
import shutil
import gzip
import os

from utils.date_utils import get_now, get_n_bday_yyyymmdd

if __name__ == "__main__":

    securities = [
    'NOA Index', 'TPA Index', 'HIA Index', 'HCA Index', 'HCTA Index', 'XPA Index',
    'TWTA Index', 'FTA Index', 'JGSA Index', 'XUA Index', 'KMA Index', 'QZA Index',
    'VGA Index', 'GXA Index', 'Z A Index', 'CFA Index', 'STA Index', 'EOA Index',
    'SMA Index', 'ESA Index', 'NQA Index', 'RTYA Index', 'DMA Index'
    ]

    per_sec_req_type = PerSecurityRequestType.gethistory
    batch_name = "dp_futagg"

    from_date = get_n_bday_yyyymmdd(15)
    to_date = get_n_bday_yyyymmdd(0)

    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "date_range": f"{from_date}|{to_date}",
        "sec_id": "TICKER",
        "fields": [
            "FUT_AGGTE_OPEN_INT",
            "FUT_AGGTE_VOL"
        ],
        "securities": securities,
    }

    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_px_eod"
    risk_idx_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)

    risk_idx_file_name = os.path.basename(risk_idx_file_path)
    df_risk_idx = pd.DataFrame()
    with tempfile.TemporaryDirectory() as temp_dir:
        tmp_file_path = f"{temp_dir}/{risk_idx_file_name.replace('.gz', '')}"
        with gzip.open(risk_idx_file_path, "rb") as f_in:
            with open(tmp_file_path, "wb") as f_out:
                shutil.copyfileobj(f_in, f_out)

        parser = BloombergParser(
            tmp_file_path, sep="|", skipinitialspace=True, on_bad_lines="error"
        )
        df_risk_idx = parser.parse_data()

    if df_risk_idx.shape[0] == 0:
        raise ValueError("No data returned from Bloomberg")

    df_risk_idx.rename(
        columns={
            "Ticker": "BBG_TICKER",
            "Field": "FIELD",
            "Date": "DATE",
            "Value": "VALUE",
        },
        inplace=True,
    )
    df_risk_idx["DATE"] = pd.to_datetime(
        df_risk_idx["DATE"], format="%m/%d/%Y", errors="coerce"
    )
    df_risk_idx["VALUE"] = pd.to_numeric(df_risk_idx["VALUE"], errors="coerce")

    df_risk_idx["WHEN_UPDATED"] = get_now()

    adaptor = SnowparkAdaptor(
        database="BLOOMBERG",
        schema="BBGH_FUTURES",
        warehouse="BLOOMBERG_HUB_WH",
        role="DR_BBGH_OWNER",
    )

    ret_val = adaptor.upsert(
        df_risk_idx, "FUTURE_AGGTE_HIST", ["BBG_TICKER", "FIELD", "DATE"]
    )
    print(ret_val)
