raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/coppclark/hds/1.0"
  s3_prefix: "coppclark/hds"
  include_prefix: true
  structure: '[
    "Currencies_$DATE$.csv",
    "ExchangeSettlement_$DATE$.csv",
    "ExchangeTrading_$DATE$.csv",
    "FXSpotCalendar_LatAmNDFs_WithForwardValueDates_andFixingDates_$DATE$.csv",
    "FXSpotCalendar_NDFs_WithForwardValueDates_andFixingDates_$DATE$.csv",
    "FXSpotCalendar_WithForwardValueDates_$DATE$.csv",
    "FinancialCentres_$DATE$.csv",
    "WeekendDefinitions_$DATE$.csv",
    "WorkingWeekends_$DATE$.csv",
    "Special_$DATE$.csv",
    "History_Currencies_$DATE$.csv",
    "History_ExchangeSettlement_$DATE$.csv",
    "History_ExchangeTrading_$DATE$.csv",
    "History_Special_$DATE$.csv",
    "History_FinancialCentres_$DATE$.csv",
    "History_WorkingWeekends_$DATE$.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "COPPCLARK_HDS"

  table_map:
    CURRENCIES_RAW:
      pattern: "^Currencies_$DATE$.*" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/"
      file_format: "FF_COPPCLARK"

    EXCHANGE_SETTLEMENT_RAW:
      pattern: "^ExchangeSettlement_$DATE$.*" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    EXCHANGE_TRADING_RAW:
      pattern: "^ExchangeTrading_$DATE$.*" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    FXSPOT_CALENDAR_FORWARD_VALUES_RAW:
      pattern: "^.*FXSpotCalendar_WithForwardValueDates_$DATE$.*"
      col_num: 29
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"


    FXSPOT_CALENDAR_NDFS_FOWARD_AND_FIXING_DATES_RAW:
      pattern: "^.*FXSpotCalendar_NDFs_WithForwardValueDates_andFixingDates_$DATE$.*"
      col_num: 47
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    FXSPOT_CALENDAR_LATAM_NDFS_RAW:
      pattern: "^.*FXSpotCalendar_LatAmNDFs_WithForwardValueDates_andFixingDates_$DATE$.*"
      col_num: 47
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    SPECIAL_RAW:
      pattern: "^Special_$DATE$.*" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    WEEKEND_DEFINITIONS_RAW:
      pattern: "^.*WeekendDefinitions_$DATE$.*" 
      col_num: 6
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    WORKING_WEEKENDS_RAW:
      pattern: "^WorkingWeekends_$DATE$.*" 
      col_num: 7
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    FINANCIAL_CENTRES_RAW:
      pattern: "^FinancialCentres_$DATE$.*" 
      col_num: 11
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    CENTRE_CHANGE_REPORT_RAW:
      pattern: "^.*CentreChangeReport_$DATE$.*" 
      col_num: 9
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    CURRENCIES_HISTORY_RAW:
      pattern: "^.*History_Currencies_$DATE$.*" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/"  
      file_format: "FF_COPPCLARK"

    EXCHANGE_SETTLEMENT_HISTORY_RAW:
      pattern: "^.*History_ExchangeSettlement_$DATE$.*" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    EXCHANGE_TRADING_HISTORY_RAW:
      pattern: "^.*History_ExchangeTrading_$DATE$.*" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    SPECIAL_HISTORY_RAW:
      pattern: "^.*History_Special_$DATE$.*" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    FINANCIAL_CENTRES_HISTORY_RAW:
      pattern: "^.*History_FinancialCentres_$DATE$.*" 
      col_num: 11
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"

    WORKING_WEEKENDS_HISTORY_RAW:
      pattern: "^.*History_WorkingWeekends_$DATE$.*" 
      col_num: 7
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "coppclark/hds/" 
      file_format: "FF_COPPCLARK"



    