import os
import tarfile
import logging
import shutil
from uuid import uuid4
from pathlib import Path
from utils.compress_formats.format import Gzip
from utils.compress_formats.format import Tar
from utils.compress_formats.format import Uncompressed
from utils.compress_formats.format import Zip


magic_dict = {
    b"\x1f\x8b\x08": "gz",
    b"\x42\x5a\x68": "bz2",
    b"\x50\x4b\x03\x04": "zip"
    }

max_len = max(len(x) for x in magic_dict)


def test_tar(file):
    return tarfile.is_tarfile(file)
        

class Uncompressor:
    def __init__(self, tmp):
        self.tmp = tmp
        self.target = f'{self.tmp}/target'
        self.create_folder(self.target)

    def create_folder(self, path):
        Path(path).mkdir(parents=True, exist_ok=True)

    def get_file_type(self, file):
        if os.path.isdir(file):
            return 'uncompressed'
        try:
            with open(file, 'rb') as f:
                file_start = f.read(max_len)
            for magic, filetype in magic_dict.items():
                if file_start.startswith(magic):
                    return filetype
        except Exception as e:
            logging.exception(e)
        finally:
            if test_tar(file):
                return 'tar'
        raise ValueError(f'File format not compatible for file {file}')

    def uncompress(self, file, extract_pattern):
        file_type = self.get_file_type(file)
        stage = f'{self.tmp}/stage/{uuid4().hex}'
        self.create_folder(stage)
        if file_type == 'gz':
            Gzip.uncompress(file, stage)
        elif file_type == 'tar':
            Tar.uncompress(file, stage)
        elif file_type == 'zip':
            Zip.uncompress(file, extract_pattern, stage)
        elif file_type == 'uncompressed':
            Uncompressed.uncompress(file, extract_pattern, stage)
        return stage
        
    def finalize(self, file):
        filename = file.split('/')[-1]
        shutil.copyfile(file, f'{self.target}/{filename}')