from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor

from utils.date_utils import get_n_bday_yyyymmdd, get_n_bday, get_now
from bloomberg.bpipe import BpipeHandler
import pandas as pd
import datetime

if __name__ == "__main__":

    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    df_securities = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """select ref.id_bb_global As ID_BB_GLOBAL
            from VW_FUTURE_REF ref join 
            COMMON.PUBLIC.TIMESTAMP_DAILY td on td.date >= ref.fut_first_trade_dt and td.date <= ref.last_tradeable_dt left outer join 
            VW_FUTURE_PX px on ref.id_bb_global = px.id_bb_global and px.price_date = td.date
            WHERE FUTURE_ROOT = 'TU'  and 
            td.DAY_NAME not in ('SUNDAY', 'SATURDAY') and 
            px.px_settle is null and 
            td.date <= '2025-02-14'
            group by ref.bbg_full_ticker, ref.id_bb_global, ref.fut_contract_dt
            HAVING COUNT(1) > 15;
        """,
    )

    securities = df_securities["ID_BB_GLOBAL"].tolist()

    to_date = datetime.datetime(2025, 2, 14)
    from_date = datetime.datetime(2000, 1, 1)

    # securities = ["TUH19 Comdty"]

    bpipe_instance = ("Tech1ProdBPipe47537.jainglobal.net", 8194)
    bpipe_app = "JAIN:pmdashboard-bps"
    bpipe_handler = BpipeHandler(bpipe_instance, bpipe_app)

    df_fut_px_settle = bpipe_handler.fetch_hist(p_ticker=securities, p_field=[
                "PX_OPEN",
                "PX_HIGH",
                "PX_LOW",
                "PX_SETTLE",
                "PX_VOLUME",
                "OPEN_INT",
            ], p_start=from_date, p_end=to_date)
    
    df_fut_px_settle["date"] = pd.to_datetime(df_fut_px_settle["date"], format="%m/%d/%Y", errors="coerce")
    df_fut_px_settle["value"] = pd.to_numeric(df_fut_px_settle["value"], errors="coerce")

    df_fut_px_wide = df_fut_px_settle.pivot(index=['ticker', 'date'], columns='field', values='value').reset_index()
    df_fut_px_wide.rename(columns= {"ticker": "ID_BB_GLOBAL", "date": "PRICE_DATE"}, inplace=True)
    
    df_fut_px_wide["UPDATE_SOURCE"] = 3
    df_fut_px_wide["LAST_UPDATED"] = get_now()
    df_fut_px_wide["UPDATED_BY"] = "adhoc_backfill"

    print(df_fut_px_wide.head())
            
    
    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_FUTURES",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
    )

    ret_val = adaptor.upsert(df_fut_px_wide, "FUTURE_PX_DL_ADHOC_BACKFILL", ["ID_BB_GLOBAL", "PRICE_DATE"])
    print(ret_val)
