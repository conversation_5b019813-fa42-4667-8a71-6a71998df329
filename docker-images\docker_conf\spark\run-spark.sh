#!/bin/sh
/usr/bin/cwiqfs -f /jfs -o cache_size_mb=10000 -o allow_other -o force_allow_non_admin_allow_other -o cwiqfs_yaml=/etc/cwiq/cwiqfs/config.yaml -o log_file=/var/log/cwiq/cwiqfs/cwiqfs_v2.log -o tmpdir=/var/tmp/cache -o audit_log_file=/var/log/cwiq/cwiqfs/audit_cwiqfs_v2.log &

sleep 10

/opt/bitnami/spark/bin/spark-class org.apache.spark.deploy.master.Master &
/opt/bitnami/spark/bin/spark-class org.apache.spark.deploy.worker.Worker spark://$(hostname):7077 &

tail -f /dev/null