<!DOCTYPE html>
<html lang="en" dir="ltr">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login</title>
    <link rel="stylesheet" href="/static/css/login.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.2/css/all.min.css" />
    <script src="/static/ts/data_catalog_controller.js?v=20241003205700" defer></script>
    <script src="/static/ts/model_base.js?v=20241003205700" defer></script>
    <script src="/static/ts/web_socket_client.js?v=20241003205700" defer></script>
    <script src="/static/ts/logger.js?v=20241003205700" defer></script>
    <script src="/static/ts/config.js?v=20241003205700" defer></script>
    <script src="/static/ts/data_catalog_model.js?v=20241003205700" defer></script>
    <script src="/static/ts/data_table.js?v=20241003205700" defer></script>
    <script src="/static/ts/UserContext.ts"></script>
    <script src="/static/ts/login.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            const userLogin = new UserLogin();
        });
    </script>
</head>

<body>
    <div class="container">
        <div class="wrapper">
            <div class="title"><span>Login</span></div>
            <form action="#" id="login_form">
                <div class="row">
                    <i class="fas fa-user"></i>
                    <input type="text" id="login_email" placeholder="Email" required>
                </div>
                <div class="row">
                    <i class="fas fa-lock"></i>
                    <input type="password" id="login_password" placeholder="Password" required>
                </div>
                <div class="row button">
                    <input type="submit" id="login_submit" value="Login">
                </div>
                <div class="signup-link" id="register">Not a user? <a href="#">Register</a></div>
            </form>
        </div>
    </div>

</body>

</html>