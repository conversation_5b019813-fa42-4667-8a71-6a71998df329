raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/usda_agri"
  s3_bucket: "jg-data-dp-vendor-data"
  s3_prefix: "usda-agri"
  include_prefix: true

  structure: '[
  "PSD_api_regions_$DATE$.csv",
  "PSD_api_countries_$DATE$.csv",
  "PSD_api_uom_$DATE$.csv",
  "PSD_api_attributes_$DATE$.csv",
  "PSD_api_commodity_$DATE$.csv",
  "PSD_DATA_RELEASE_DATE_$DATE$.csv",
  "ESR_commodities_$DATE$.csv",
  "ESR_countries_$DATE$.csv",
  "ESR_datareleasedates_$DATE$.csv",
  "ESR_regions_$DATE$.csv",
  "ESR_unitsOfMeasure_$DATE$.csv",
  "GATS_CustomsDistricts_$DATE$.csv",
  "GATS_HS6Commodities_$DATE$.csv",
  "GATS_commodities_$DATE$.csv",
  "GATS_countries_$DATE$.csv",
  "GATS_regions_$DATE$.csv",
  "GATS_unitsOfMeasure_$DATE$.csv",
  "GATS_census_imports_datareleasedates_$DATE$.csv",
  "GATS_census_exports_datareleasedates_$DATE$.csv",
  "GATS_UNTrade_imports_datareleasedates_$DATE$.csv",
  "GATS_UNTrade_exports_datareleasedates_$DATE$.csv"
  ]'


snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "USDA_REFERENCE"

  table_map:
  
    ESR_REGION_RAW:
        pattern: "^ESR_regions_$DATE$.csv" 
        col_num: 2
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    ESR_COMMODITY_RAW:
        pattern: "^ESR_commodities_$DATE$.csv" 
        col_num: 3
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    ESR_Country_RAW:
        pattern: "^ESR_countries_$DATE$.csv" 
        col_num: 5
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI"   

    ESR_UOM_RAW:
        pattern: "^ESR_unitsOfMeasure_$DATE$.csv" 
        col_num: 2
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI"     
    
    ESR_DATA_RELEASE_DATE_RAW:
        pattern: "^ESR_datareleasedates_$DATE$.csv" 
        col_num: 5
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI"     
    

    GATS_REGION_RAW:
        pattern: "^GATS_regions_$DATE$.csv" 
        col_num: 2
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI"         
    
    GATS_COMMODITY_RAW:
        pattern: "^GATS_commodities_$DATE$.csv" 
        col_num: 11
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI"         
        
    GATS_HS6_COMMODITY_RAW:
        pattern: "^GATS_HS6Commodities_$DATE$.csv" 
        col_num: 7
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI"         
        
    GATS_COUNTRY_RAW:
        pattern: "^GATS_countries_$DATE$.csv" 
        col_num: 8
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    GATS_UOM_RAW:
        pattern: "^GATS_unitsOfMeasure_$DATE$.csv" 
        col_num: 4
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI"         
        
    GATS_CUSTOMS_DISTRICT_RAW:
        pattern: "^GATS_CustomsDistricts_$DATE$.csv" 
        col_num: 4
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 


    GATS_CENSUS_IMPORTS_DATA_RELEASE_DATE_RAW:
        pattern: "^GATS_census_imports_datareleasedates_$DATE$.csv" 
        col_num: 4
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    GATS_CENSUS_EXPORTS_DATA_RELEASE_DATE_RAW:
        pattern: "^GATS_census_exports_datareleasedates_$DATE$.csv" 
        col_num: 4
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    GATS_UNTRADE_IMPORTS_DATA_RELEASE_DATE_RAW:
        pattern: "^GATS_UNTrade_imports_datareleasedates_$DATE$.csv" 
        col_num: 4
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    GATS_UNTRADE_EXPORTS_DATA_RELEASE_DATE_RAW:
        pattern: "^GATS_UNTrade_exports_datareleasedates_$DATE$.csv" 
        col_num: 4
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 
    
    PSD_Country_RAW:
      pattern: "^PSD_api_countries_$DATE$.csv" 
      col_num: 4
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI"
    
      

    PSD_REGION_RAW:
      pattern: "^PSD_api_regions_$DATE$.csv" 
      col_num: 2
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI"  

    PSD_UOM_RAW:
      pattern: "^PSD_api_uom_$DATE$.csv" 
      col_num: 2
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI"

    PSD_ATTRIBUTE_RAW:
      pattern: "^PSD_api_attributes_$DATE$.csv" 
      col_num: 2
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI"
    
    PSD_COMMODITY_RAW:
      pattern: "^PSD_api_commodity_$DATE$.csv" 
      col_num: 2
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI"

    PSD_COMMOD_DATA_RELEASE_DATE_RAW:
      pattern: "^PSD_DATA_RELEASE_DATE_$DATE$.csv" 
      col_num: 5
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI"
