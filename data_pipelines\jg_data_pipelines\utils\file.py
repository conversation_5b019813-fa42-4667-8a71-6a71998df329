import os
import tempfile


def temp_folder(tmp_path = '/tmp/data'):
  os.makedirs(tmp_path,exist_ok=True)
  return tempfile.TemporaryDirectory(dir=tmp_path)

def read_n_to_last_line(filename, n = 1):
    """Returns the nth before last line of a file (n=1 gives last line)"""
    num_newlines = 0
    with open(filename, 'rb') as f:
        try:
            f.seek(-2, os.SEEK_END)    
            while num_newlines < n:
                f.seek(-2, os.SEEK_CUR)
                if f.read(1) == b'\n':
                    num_newlines += 1
        except OSError:
            f.seek(0)
        last_line = f.readline().decode().strip()
    return last_line