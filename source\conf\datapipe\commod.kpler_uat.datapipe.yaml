raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/kpler"
  s3_bucket: "jg-tech1dev-dp-snowflake-poc-data"
  s3_prefix: "usda-agri"
  include_prefix: true
  
  structure: '[
   "liquids_snap_**_DAILY_uat_$DATE$**.csv"
  ]'


snowflake:
  db_name: "COMMOD_uat"
  schema_name: "KPL<PERSON>"

  table_map:
    
    kpler_updates:
      pattern: "^liquids_snap_.*_DAILY_uat_$DATE$.*.csv" 
      col_num: 6
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 
