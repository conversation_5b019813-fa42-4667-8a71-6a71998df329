import os
import pandas as pd
import shutil
import gzip
from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType

from bloomberg.per_security.parser import BloombergParser
import tempfile
from utils.date_utils import get_today
from utils.file import temp_folder

if __name__ == "__main__":
    bo_path = "/jfs/tech1_share/pulkit.vora/bbg_bo_credit/"
    bo_file = "corp_pfd_asia_bb_comp.out.gz"
    bo_file_path = os.path.join(bo_path, bo_file)

    with tempfile.TemporaryDirectory() as temp_dir:
        tmp_file_path = f"{temp_dir}/{bo_file.replace('.gz', '')}"
        with gzip.open(bo_file_path, 'rb') as f_in:
            with open(tmp_file_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)
        
        parser = BloombergParser(tmp_file_path, sep='|', skipinitialspace=True, on_bad_lines='error') 
        df_credit = parser.parse_data()
        print(df_credit.head())
        
        
        
		

    