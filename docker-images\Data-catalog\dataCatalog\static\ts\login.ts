class UserLogin {
  private baseUrl =  `${window.location.protocol}//${window.location.host}`;

    constructor() {
        DataCatalogController.openTab(new Event('click'), 'MarketData');

        const login_form = document.getElementById('login_form');

        login_form?.addEventListener("submit", (event) => {
            event.preventDefault();
            this.handleLoginSubmit()
        })

        const register = document.getElementById('register');
        register?.addEventListener("click", (event) => {
            event.preventDefault();
            this.routeToRegister();
        })
    }


    submitLogin = async () => {
        const login_email_input = document.getElementById('login_email') as HTMLInputElement;
        const login_password_input = document.getElementById('login_password') as HTMLInputElement;
        const email = login_email_input.value
        const password = login_password_input.value
    
        const requestOptions = {
            method: "POST",
            headers: { "Content-Type": "application/x-www-form-urlencoded" },
            body: JSON.stringify(
              `grant_type=&username=${email}&password=${password}&scope=&client_id=&client_secret=`
            ),
        };
    
        const response = await fetch(`${this.baseUrl}/api/token`, requestOptions);
        const data = await response.json();
    
        if (!response.ok) {
            console.log(data.detail)
            alert("Login failed. Please check your credentials")
        } else {
            const baseUrl = `${window.location.protocol}//${window.location.host}`;
            console.log("localStorage.setItem(\"userToken\", data.access_token):",localStorage.setItem("userToken", data.access_token))
            localStorage.setItem("userToken", data.access_token)
            
            window.location.assign(baseUrl+"/static/index.html");
            const dataCatalogController = new DataCatalogController()
        }
      };
    
      handleLoginSubmit = () => {
        const login_password_input = document.getElementById('login_password') as HTMLInputElement;
        const password = login_password_input?.value
        this.submitLogin();
      };

      routeToRegister() {
        const baseUrl = `${window.location.protocol}//${window.location.host}`;
        window.location.assign(baseUrl+"/static/register.html");
      }
}
