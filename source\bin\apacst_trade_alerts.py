import psycopg2
import numpy as np
import pandas as pd
from datetime import date
from loguru import logger as log
from datetime import datetime, timedelta
import pytz
import os

from strunner import *
setupEnvironment()

import jglib.paths as pth
from stcommon.infra.rds.snowflake_operation import *
from stcommon.email_util_k8s import EmailUtility

RUN_LOG_FILE = f"/opt/data/rawdata/apacst_trade_alert_run_log.csv"

def get_last_run_time(trade_data_type):
    if os.path.exists(RUN_LOG_FILE):
        df_log = pd.read_csv(RUN_LOG_FILE, dtype={"Latest_Trade_Alert_Time": str})
        df_log = df_log[df_log["Type"] == trade_data_type]
        if not df_log.empty:
            last_run_time_str = str(df_log.iloc[-1]['Latest_Trade_Alert_Time'])
            return datetime.strptime(last_run_time_str, "%Y-%m-%d %H:%M:%S")
        return None
    
def log_run(type, max_trade_execution_timestamp):
    log_entry = {
        "Type": type,
        "Latest_Trade_Alert_Time": max_trade_execution_timestamp.strftime("%Y-%m-%d %H:%M:%S"),
    }
    
    log_df =pd.DataFrame([log_entry])
    if os.path.exists(RUN_LOG_FILE):
        # Read existing log file, append new log entry and keep only the last 20 rows/logs
        existing_df = pd.read_csv(RUN_LOG_FILE)
        updated_df = pd.concat([existing_df, log_df], ignore_index=True)
        
        # Keep latest entry for each type
        latest_per_type = updated_df.sort_values("Latest_Trade_Alert_Time").groupby("Type", as_index=False).tail(1)
        remaining = updated_df[~updated_df.index.isin(latest_per_type.index)]
        trimmed = pd.concat([remaining.tail(18), latest_per_type], ignore_index=True).drop_duplicates()

        trimmed.to_csv(RUN_LOG_FILE, mode='w', header=True, index=False)
    else:
        log_df.to_csv(RUN_LOG_FILE, mode='w', header=True, index=False)

def send_alert(view_name, type, last_run_time, additional_recipient):
    
    pg_query = f"""SELECT TRANSACTION_ID, BUNDLE_NAME, TRADE_EXECUTION_TIMESTAMP, REVISION, REVISION_NUMBER, SECURITY_DESNAME, QUANTITY, PRICE, SFS_TYPE, SECURITY_CURRENCY, EXECUTING_BROKER, TRANSACTION_DATE
                FROM SHARED.{view_name} VATY
                WHERE TRADE_EXECUTION_TIMESTAMP > '{last_run_time}'
                ORDER BY TRADE_EXECUTION_TIMESTAMP ASC;
                """  
    print(pg_query)
    
    df = pd.read_sql(pg_query, pg_conn)
    df.columns = [col.upper() for col in df.columns]
    
    if df is not None and not df.empty:
        email_util = EmailUtility()
        to_recipient = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
        to_recipient = to_recipient + additional_recipient
    
        body = (
            f"Hi team,<br><br>Below are the latest trades recorded.<br><br>"
        )
        
        body += email_util.format_dataframe_html(df, "Latest Trades Summary")

        email_util.send_email(
            to_recipient=to_recipient,
            subject=f"[ALERT] Latest Trades Summary",
            body=body,
            df=None
        )
        max_trade_execution_timestamp = df['TRADE_EXECUTION_TIMESTAMP'].max()
        log_run(type, max_trade_execution_timestamp)

try:
    utc_now = datetime.now(pytz.utc)
    objconfig = {}
    objconfig = fio.read_config_secrets()

    pg_conn = psycopg2.connect(
        host=objconfig['pg_host'],
        database="postgres",
        user=objconfig['pg_user'],
        password=objconfig['pg_password']
    )
    
    if not get_last_run_time('MACRO'):
        log_run('MACRO',(utc_now - timedelta(hours=1)))
 
    if not get_last_run_time('MACRO1'):
        log_run('MACRO1',(utc_now - timedelta(hours=1)))

    macro_last_run_time = get_last_run_time('MACRO')
    macro_last_run_time = macro_last_run_time.strftime("%Y-%m-%d %H:%M:%S")    
    
    macro1_last_run_time= get_last_run_time('MACRO1')
    macro1_last_run_time = macro1_last_run_time.strftime("%Y-%m-%d %H:%M:%S")
    
    send_alert('V_ARC_TRANSACTIONS_YZHANG0', 'MACRO', macro_last_run_time, ['<EMAIL>'])
    send_alert('V_ARC_TRANSACTIONS_YZHANG1', 'MACRO1', macro1_last_run_time, ['<EMAIL>'])
    
except Exception as e:
    log.error("Unexpected error on running the DQ check: {}".format(str(e)))
    raise e  