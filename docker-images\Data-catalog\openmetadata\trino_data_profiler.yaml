source:
  type: trino
  serviceName: trino
  sourceConfig:
    config:
      type: Profiler
      generateSampleData: true
      processPiiSensitive: false
processor:
  type: orm-profiler
  config: {}
sink:
  type: metadata-rest
  config: {}
workflowConfig:
  loggerLevel: DEBUG
  openMetadataServerConfig:
    hostPort: "http://$SERVICE_IP/api"
    authProvider: openmetadata
    securityConfig:
      jwtToken: $OPENMETADATA_JWT_TOKEN