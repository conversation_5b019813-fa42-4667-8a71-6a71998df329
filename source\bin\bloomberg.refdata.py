'''
cd /jfs/tech1/apps/datait/mahendra/JG-DATA-PLATFORM/source/bin
export JGDATA_PATH="/jfs/tech1/apps/datait/mahendra/JG-DATA-PLATFORM/source/"
export STCOMMON_PATH="/jfs/tech1/apps/datait/mahendra/JG-DATA-PLATFORM/source/stcommon"
export RAWSTORE_ROOT="/jfs/tech1/apps/rawdata/"
cd /jfs/tech1/apps/datait/mahendra/JG-DATA-PLATFORM/source/bin
export BUILDINPARALLEL=True
python
'''
import sys, os, glob, argparse
from strunner import *
setupEnvironment() # sets up environment
from jgdata import *
import stcommon
from stcommon.infra.python.module import function_from_path

dataset="bloomberg.refdata"
import jgdata.datasets.bloomberg.refdata
initDataset('bloomberg.refdata')

prefix = 'jgdata.datasets'
funct = function_from_path(f"{prefix}.{dataset}","buildDataset")

funct()





