create or replace view meteologica.api.vw_normal as
with consolidated as (
SELECT * FROM meteologica.api.normal
    WHERE issue_date < DATE('2025-01-01')
    UNION ALL 
    SELECT * FROM meteologica.ongoing.normal
    WHERE issue_date >= DATE('2025-01-01')
),
normal_data as(
    select content_id, content_name, issue_date, timezone, unit, installed_capacity, update_id, source_file,
        to_varchar(data:"From yyyy-mm-dd hh:mm") from_datetime,
        to_varchar(data:"To yyyy-mm-dd hh:mm") to_datetime,
        to_varchar(data:"UTC offset from (UTC+/-hhmm)") utc_offset_from,
        to_varchar(data:"UTC offset to (UTC+/-hhmm)") utc_offset_to,
        to_varchar(data:"normal") normal
        from consolidated
), normal_utc as (
    select content_id, content_name, 
    issue_date,
    unit, installed_capacity, update_id, source_file, normal, 
    to_timestamp_ntz(convert_timezone(
            'UTC', 
            TO_TIMESTAMP_TZ(FROM_DATETIME || ' ' || split(utc_offset_from, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
        )) FROM_DATETIME,
    to_timestamp_ntz(convert_timezone(
            'UTC', 
            TO_TIMESTAMP_TZ(TO_DATETIME || ' ' || split(utc_offset_to, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
        )) TO_DATETIME
        from normal_data
)
select * from normal_utc;

--35509667
select count(*) from vw_normal limit 10;
select * from vw_normal limit 10;