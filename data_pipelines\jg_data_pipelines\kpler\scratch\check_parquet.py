import pandas as pd
import requests

from utils.date_utils import get_current_hour

from pathlib import Path


if __name__ == "__main__":
    df = pd.read_parquet("/jfs/tech1/apps/rawdata/kpler_power/ongoing/avail_per_unit/processed/20250321100000.parquet")
    print(df.columns)
    print(df.head())
    print(df.dtypes)
    print(df.shape[0]/10_00_000.0)

    # url = "https://api.kpler.com/power/outages/v1/availability/series/fuel-types?provider=eex&start=2024-08-01&end=2024-08-06&granularity=daily&as_of=2024-02-01&country=AT&fuel_types=geothermal"

    # payload={}
    # headers = {
    # 'Authorization': "jain-global-dev:47fd296b-8930-42ff-a6a3-7935a28c99e6",
    # 'Accept': 'application/json'
    # }

    # response = requests.request("GET", url, headers=headers, data=payload)

    # print(response.text)

    # country_list = ['UK', 'DE', 'FR', 'NL', 'BE', 'AT']
    # params_list = [{"start": "start_date", "end": "end_date", "granularity": REQUEST_GRANULARITY, "timezone": REQUEST_TIMEZONE, "country": country, "provider": provider, "fuel_type": fuel_type} for provider, countries in DICT_POWER_AVAIL_PER_UNITS_PARAMS.items() for country in countries.keys() if country in country_list for fuel_type in countries[country]]
    # print(params_list)
    # print(len(params_list))

    # a = get_current_hour('UTC').strftime("%Y-%m-%d %H:%M:%S")
    # print(a)

    # url = "https://api.kpler.com/power/outages/v1/availability/series/units?start=2025-03-20&end=2027-03-20&granularity=hourly&timezone=UTC&country=DE&provider=entsoe&fuel_type=hydro+water+reservoir&as_of=2025-03-20+21:00:00"
    # url = "https://api.kpler.com/power/outages/v1/availability/series/units?start=2025-03-21&end=2027-03-21&granularity=hourly&timezone=UTC&country=NL&provider=eex&fuel_type=other&as_of=2025-03-21+04:00:00"

    # payload={}
    # headers = {
    # 'Authorization': "jain-global-dev:47fd296b-8930-42ff-a6a3-7935a28c99e6",
    # 'Accept': 'application/json'
    # }

    # response = requests.request("GET", url, headers=headers, data=payload)

    # print(response.status_code)

    # print(response.text)

    json_path = "jfs/tech1/apps/rawdata/kpler_power/ongoing/avail_per_unit/raw_json/20250321100000"
    pattern_to_match = "*.json"
    for json_path in Path(json_path).rglob(pattern_to_match):
        print(json_path)