# Default system properties included when running spark-submit.
# This is useful for setting default environmental settings.

# Example:
# spark.master                     spark://spark:7077
spark.eventLog.enabled             true
spark.eventLog.dir                 file:///home/<USER>/spark/logs 
# spark.serializer                 org.apache.spark.serializer.KryoSerializer
# spark.driver.memory              5g
# spark.executor.extraJavaOptions       -XX:+PrintGCDetails -Dkey=value -Dnumbers="one two three"
spark.serializer                        org.apache.spark.serializer.KryoSerializer
spark.shuffle.manager                   SORT
spark.shuffle.consolidateFiles          true
spark.shuffle.spill                     true
spark.shuffle.memoryFraction            0.75
spark.storage.memoryFraction            0.45
spark.shuffle.spill.compress            false
spark.shuffle.compress                  false
#spark.dynamicAllocation.enabled        true
spark.jars.packages                     org.apache.iceberg:iceberg-spark-runtime-3.2_2.12:1.4.2
spark.sql.extensions                    org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions
spark.sql.catalog.spark_catalog         org.apache.iceberg.spark.SparkSessionCatalog
spark.sql.catalog.spark_catalog.type    hive
spark.sql.catalog.local                 org.apache.iceberg.spark.SparkCatalog
spark.sql.catalog.local.type            hadoop
spark.sql.catalog.local.warehouse       $PWD/warehouse
spark.sql.defaultCatalog                local