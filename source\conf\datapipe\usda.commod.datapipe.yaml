raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/usda_agri"
  s3_bucket: "jg-data-dp-vendor-data"
  s3_prefix: "usda-agri"
  include_prefix: true

  structure: '[
   "psd_**_$DATE$.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "USDA_COMMOD"

  table_map:
  
    PSD_ALL_COMMOD_RAW:
      pattern: "^psd_.*_$DATE$.csv" 
      col_num: 12
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 




  

    
    




    