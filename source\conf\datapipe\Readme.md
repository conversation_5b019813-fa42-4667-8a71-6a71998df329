## datapipe configuration files

These files are used for the data consolidation routines in the dbt and snowflake pipeline.

This is the keys on config yaml files:

### Config files details

```yaml
raw_data: ## header used on raw_data uncompress and send to s3
  rawdata_location: "<path>"  ## Location of Compress Raw Files
  s3_bucket: "<s3-bucket-name>" ## S3 with Snowflake Acess
  s3_prefix: "<s3-internal-folder>"  ## Internal S3path to files
  include_prefix: <true|false> ## If the need use compress file name as prefix to files
  structure: "<dict|arrat>" ## A dict or array structure on quotes to be used to uncompress files (see detail session)

snowflake: # header used to run copy into files to raw tables
  db_name: "<db_name>" ## Database name on snowflake 
  schema_name: "<schema_name>" ## Schema name on snowflake 

  table_map: ## array of tables to be used to parse files oon copy
    <TABLENAME>: ## Snowflake table name
      pattern: "<regex>" ## A regex to be used to find the file on stage to be used on copy
      col_num: <integer> ## Number of the columns in the file to be use on select ($1,$2...$x)
      metadata_columns: ["filename"] ## Array metadata to be used on insert (aka METADATA$FILENAME).Put on end columns on array order
      stage_path: "@<stage db name>.<@stage schema name><stage name>/<internal path on stage>/" ## Stage name with database and schema name and path to folder on file are stored
      file_format: "<file format name>" ## File format named on database
```

### structure key

The `structure` key is used to uncompact 

- Will uncompact  the `FILE.TAR.GZ` and send only `.csv` files extracted

```yaml
structure = '{
     "FILE.TAR.GZ": ["*.csv"]
}'
```

- Will uncompact  the `FILE_RAW.TAR.GZ` and the internal `FILE.TAR.GZ` and send all files extracted

```yaml
structure = '{ 
            "FILE_RAW.TAR.GZ": {
                "FILE.TAR.GZ": ["*"]
            }
}'
```

- Can use `*` to find all files in the patter

```yaml
structure = '{ 
            "FILE_*RAW.TAR.GZ": {
                ".TAR.GZ": ["*"]
            }
}'
```

- To send direct flat, just need use a array

```yaml
structure = '["FILE_NAME_*_TO_SEND.csv"]'
```

> **All the `structure` data need to be quoted by single (') and internal keys by double (")**

### Running pipeline

To running pipeline are needed to use an airflow to call the consolidation scripts:

- [pipeline_rawdata](../..//bin/pipeline_rawdata.py): This script will run a uncompress routine and send files to a s3 bucket, all configuration are provide from `raw_data` key on configuration file. `--dataset` is a required argument if dataset name (<vendor>.<dataset>)

- [pipeline_sf_copy](../../bin/pipeline_sf_copy.py): This script will run trigger a copy into from snowflake s3 stage to raw tables,
all configuration are provide from `snowflake` key on configuration file. `--dataset` is a required argument if dataset name (<vendor>.<dataset>)

### Using --date and \$DATE$

Is possible send a `--date` argument to scripts, with a date in integer format (YYYYMMDD), and use this on `structure` key and `patter` key

On `structure` using `--date 20240101` this value

```yaml
structure = '{ 
            "FILE_$DATE$_RAW.TAR.GZ": {
                "FILE_$DATE$.TAR.GZ": ["*"]
            }
}'
```

will be translate to a dict:

```python
{ 
            "FILE_20240101_RAW.TAR.GZ": {
                "FILE_20240101.TAR.GZ": ["*"]
            }
}'
```

On `pattern` using `--date 20240101` this value

```yaml
pattern: ".*$DATE$.*CITIES.*"
```

will be translate to string:

```python
".20240101.*CITIES.*"
```

> remember `pattern` need follow **regex** systax

