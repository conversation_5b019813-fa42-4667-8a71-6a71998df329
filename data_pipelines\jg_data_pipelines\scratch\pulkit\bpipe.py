import json
from datetime import datetime
from datetime import timedelta, datetime
import atexit

import blpapi
import numpy as np
import pandas as pd
from pandas import DataFrame
from utils.date_utils import get_now


current_date = datetime.today()
EXCEPTIONS = blpapi.Name("exceptions")
FIELD_ID = blpapi.Name("fieldId")
REASON = blpapi.Name("reason")
CATEGORY = blpapi.Name("category")
DESCRIPTION = blpapi.Name("description")

SECURITIES = blpapi.Name("securities")
FIELDS = blpapi.Name("fields")
OVERRIDES = blpapi.Name("overrides")
FIELD_ID = blpapi.Name("fieldId")
VALUE = blpapi.Name("value")
SECURITY_DATA = blpapi.Name("securityData")

SessionConnectionDown = blpapi.Name("SessionConnectionDown")
SessionConnectionUp = blpapi.Name("SessionConnectionUp")
SessionTerminated = blpapi.Name("SessionTerminated")
ServiceDown = blpapi.Name("ServiceDown")
SlowConsumerWarning = blpapi.Name("SlowConsumerWarning")
SlowConsumerWarningCleared = blpapi.Name("SlowConsumerWarningCleared")
DataLoss = blpapi.Name("DataLoss")

ServiceName = blpapi.Name("serviceName")
# authorization
AUTHORIZATION_SUCCESS = blpapi.Name("AuthorizationSuccess")
AUTHORIZATION_FAILURE = blpapi.Name("AuthorizationFailure")
AUTHORIZATION_REVOKED = blpapi.Name("AuthorizationRevoked")
TOKEN_SUCCESS = blpapi.Name("TokenGenerationSuccess")
TOKEN_FAILURE = blpapi.Name("TokenGenerationFailure")
TOKEN = blpapi.Name("token")

g_session = None
g_sessionStarted = False
g_subscriptions = None
g_identity = None
g_authCorrelationId = None


class BpipeHandler:
    # use a Singleton to manage the session and query while running dashboard scripts
    _instance = None

    def __new__(cls, bpipe_instance, bpipe_app, service="//blp/refdata"):
        if cls._instance is None:
            cls._instance = super(BpipeHandler, cls).__new__(cls)
            cls._instance.bpipe_instance = bpipe_instance
            cls._instance.bpipe_app = bpipe_app
            cls._instance.service = service
            cls._instance.session = cls._instance._start_session_server()
            atexit.register(cls._instance._close_session)
        return cls._instance

    def _start_session_server(self):
        options = blpapi.SessionOptions()
        # Note: SessionOptions.SessionName require SDK version 3.23.x or later
        options.sessionName = "Example Session"

        options.setServerAddress(
            self._instance.bpipe_instance[0], self._instance.bpipe_instance[1], 0
        )
        authOptions = blpapi.AuthOptions.createWithApp(self._instance.bpipe_app)
        g_authCorrelationId = blpapi.CorrelationId("authCorrelation")
        options.setSessionIdentityOptions(authOptions, g_authCorrelationId)
        print("Session options: %s" % options)
        
        session = blpapi.Session(options)

        # Start a Session
        if not session.start():
            raise Exception("Failed to start session.")

        # service = "//blp/refdata"
        # service = "//blp/staticmktdata"
        if not session.openService(self._instance.service):
            print("Failed to open %s service" % self._instance.service)
            return
        return session

    def _close_session(self):
        if self.session:
            self.session.stop()
            print("Bloomberg session closed.")

    def _get_different_type(self, p_datatype, p_field, p_fieldData):
        if p_datatype == blpapi.DataType.STRING:
            ret = p_fieldData.getElementAsString(p_field)
        elif p_datatype == blpapi.DataType.FLOAT64:
            ret = p_fieldData.getElementAsFloat(p_field)
        elif p_datatype == blpapi.DataType.INT32:
            ret = p_fieldData.getElementAsInteger(p_field)
        elif p_datatype == blpapi.DataType.DATE:
            ret = p_fieldData.getElementAsDatetime(p_field)
        elif p_datatype == blpapi.DataType.DATETIME:
            ret = p_fieldData.getElementAsDatetime(p_field)
        else:
            ret = np.nan
        return ret

    def fetch_live(
        self, tickers, fields, p_overrides: dict = None, to_df: bool = True,
    ) -> pd.DataFrame | None:
        """
        Fetch live prices, similar to BDP

        :param tickers:
        :param fields:
        :param p_overrides: overrides for the request
        :param p_pd_datetime: convert index to pandas datetime
        :return:
        """
        refDataService = self.session.getService(self._instance.service)
        request = refDataService.createRequest("ReferenceDataRequest")

        if isinstance(tickers, str):
            tickers = [tickers]
        if isinstance(fields, str):
            fields = [fields]

        for ticker in tickers:
            request.getElement("securities").appendValue(ticker)
        for field in fields:
            request.getElement("fields").appendValue(field)
        if p_overrides:
            for field, value in p_overrides.items():
                override = request.getElement(OVERRIDES).appendElement()
                override.setElement(FIELD_ID, field)
                override.setElement(VALUE, value)

        self.session.sendRequest(request)
        data = {}
        while True:
            event = self.session.nextEvent(500)
            for msg in event:
                if msg.hasElement("securityData"):
                    securityDataArray = msg.getElement("securityData").values()
                    for securityData in securityDataArray:
                        ticker = securityData.getElementAsString("security")
                        fieldData = securityData.getElement("fieldData")
                        data[ticker] = dict()
                        for field in fields:
                            try:
                                if fieldData.hasElement(field):
                                    datatype = fieldData.getElement(field).datatype()
                                    data[ticker][field] = self._get_different_type(
                                        datatype, field, fieldData
                                    )
                            except:
                                data[ticker][field] = np.nan
                                continue
            if event.eventType() == blpapi.Event.RESPONSE:
                break
        if to_df:
            df_data = pd.DataFrame.from_dict(data, orient="index")
            df_data.reset_index(drop=False, inplace=True, names="IDENTIFIER")
            return df_data
        else:
            return data

    def fetch_hist(
        self,
        p_ticker,
        p_field,
        p_start: datetime = None,
        p_end: datetime = None,
        p_opt_args: dict = None,
        to_df: bool = True,
    ) -> DataFrame | None:
        """
        Fetch hist prices, similar to BDH

        :param p_ticker: str or list of tickers
        :param p_field: str or list of fields
        :param p_start: start date
        :param p_end: end date
        :param p_opt_args: other optional arguments. Similar to BDH()'s arguments after end date
        :return: DataFrame with requested data in long form.
        """
        # use HistoricalDataRequest
        ref_data_service = self.session.getService(self._instance.service)
        request = ref_data_service.createRequest("HistoricalDataRequest")
        if isinstance(p_ticker, str):
            p_ticker = [p_ticker]
        if isinstance(p_field, str):
            p_field = [p_field]
        for this_ticker in p_ticker:
            request.getElement("securities").appendValue(this_ticker)
        for this_field in p_field:
            request.getElement("fields").appendValue(this_field)
        request.set("startDate", p_start.strftime("%Y%m%d"))
        request.set("endDate", p_end.strftime("%Y%m%d"))
        if p_opt_args is not None:
            for k, v in p_opt_args.items():
                request.set(k, v)
        request.set("periodicitySelection", "DAILY")

        self.session.sendRequest(request)

        data = dict()
        while True:
            event = self.session.nextEvent()
            for msg in event:
                if msg.hasElement("securityData"):
                    security_data = msg.getElement("securityData")
                    ticker = security_data.getElementAsString("security")
                    field_data = security_data.getElement("fieldData")
                    if ticker not in data:
                        data[ticker] = []
                    for fields in field_data.values():
                        date = fields.getElementAsDatetime("date")
                        row = {"date": date}
                        for this_field in p_field:
                            if fields.hasElement(this_field):
                                datatype = fields.getElement(this_field).datatype()
                                row[this_field] = self._get_different_type(
                                    datatype, this_field, fields
                                )
                                # row[this_field] = fields.getElementAsFloat(this_field)
                            else:
                                row[this_field] = np.nan
                        data[ticker].append(row)
            if event.eventType() == blpapi.Event.RESPONSE:
                break
        
        if to_df:
            df_all_data = pd.DataFrame()
            for ticker, rows in data.items():
                df = pd.DataFrame(rows)
                if df.empty:
                    continue
                df_melted = pd.melt(df, id_vars=["date"], value_vars=p_field, var_name="field", value_name="value")
                df_melted["identifier"] = ticker
                df_all_data = pd.concat([df_all_data, df_melted], ignore_index=True)
            
            df_all_data.reset_index(drop=True, inplace=True)

            return df_all_data
        else:
            return data
    
    def bulk_data_request(
        self,
        securities: str | list[str],
        field: str,
        p_overrides: dict[str, any] = {},
        to_df: bool = True,
    ):
        service = self.session.getService(self._instance.service)
        request = service.createRequest("ReferenceDataRequest")

        securities = [securities] if isinstance(securities, str) else securities

        request_securities = request.getElement(SECURITIES)
        for security in securities:
            request_securities.appendValue(security)

        request_fields = request.getElement(FIELDS)
        request_fields.appendValue(field)

        if p_overrides:
            for field, value in p_overrides.items():
                override = request.getElement(OVERRIDES).appendElement()
                override.setElement(FIELD_ID, field)
                override.setElement(VALUE, value)

        eventQueue = blpapi.EventQueue()
        self.session.sendRequest(request, eventQueue=eventQueue)

        results = {}

        def process_event(event, session):
            for msg in event:
                if msg.hasElement(SECURITY_DATA, True):
                    msg_dict = msg.toPy()
                    data_list = msg_dict["securityData"]

                    for data_entry in data_list:
                        results[data_entry["security"]] = {
                            "fieldData": data_entry["fieldData"],
                            "fieldExceptions": data_entry["fieldExceptions"],
                            "eidData": data_entry["eidData"],
                        }
                        if "securityError" in data_entry:
                            results[data_entry["security"]]["securityError"] = data_entry["securityError"]

        done = False
        while not done:
            ev = eventQueue.tryNextEvent()
            if ev:
                eventType = ev.eventType()
                if eventType == blpapi.Event.RESPONSE:
                    process_event(ev, self.session)
                    done = True
                elif eventType == blpapi.Event.PARTIAL_RESPONSE:
                    process_event(ev, self.session)
                elif eventType == blpapi.Event.REQUEST_STATUS:
                    msg_list = []
                    for msg in ev:
                        if msg.messageType == blpapi.Names.REQUEST_FAILURE:
                            reason = msg.getElement(REASON)
                            msg_list.append("Request failed: " + reason + ", message: " + json.dumps(msg))
                    raise RuntimeError(json.dumps(msg_list))

        if to_df:
            df_all_data = pd.DataFrame()
            for identifier, rows in results.items():
                if "fieldData" in rows.keys():
                    fields_dict = rows["fieldData"]
                    for field, field_data in fields_dict.items():
                        df_inner = pd.DataFrame(field_data)
                        df_inner["field"] = field
                        df_inner["identifier"] = identifier
                        df_all_data = pd.concat([df_all_data, df_inner], ignore_index=True)
            df_all_data.reset_index(drop=True, inplace=True)
            return df_all_data
        else:
            return results


if __name__ == "__main__":
    bpipe_instance = ("Tech1ProdBPipe47537.jainglobal.net", 8194)
    # bpipe_app = "JAIN:BEACON_BPS"
    bpipe_app = "JAIN:pmdashboard-bps"
    # service = "//blp/staticmktdata"
    service = "//blp/refdata"
    # service = "//blp/mktdata"
    bpipe_handle = BpipeHandler(bpipe_instance, bpipe_app, service)
    
    # df_hist = bpipe_handle.fetch_hist(
    #     ["/isin/US797440CG74"],
    #     ["PX_LAST"],
    #     datetime(2025, 1, 1),
    #     datetime(2025, 4, 17),
    #     to_df=False,
    # )
    # print(df_hist)

    # df_ref = bpipe_handle.fetch_live(
    #     ["/isin/US797440CG74"],
    #     ["SETTLE_DT", "INT_ACC", "NXT_PAR_CALL_DT"],
    #     p_overrides = {"USER_LOCAL_TRADE_DATE": "20250426"},
    #     to_df=False,
    #     )
    # print(df_ref)

    # df_ref_loans = bpipe_handle.fetch_live(
    #     ["/isin/XAD7000LAC81"],
    #     ["SETTLE_DT", "INT_ACC", "NXT_CALL_DT"],
    #     p_overrides = {"USER_LOCAL_TRADE_DATE": "20250426"}
    #     )
    # print(df_ref_loans)

    # df_ref_loans_bulk = bpipe_handle.bulk_data_request(
    #     ["/isin/XAD7000LAC81"],
    #     "LOAN_CASH_FLOW_SCHEDULE",
    #     p_overrides = {"USER_LOCAL_TRADE_DATE": "20250426"}
    # )
    # print(df_ref_loans_bulk.shape)
    # print(df_ref_loans_bulk)

    # df_infl_linkers = bpipe_handle.fetch_live(
    #     ["/isin/DE0001030583"],
    #     ["SETTLE_DT", "INT_ACC", "IDX_RATIO"],
    #     p_overrides = {"USER_LOCAL_TRADE_DATE": "20250426"}
    #     )
    # print(df_infl_linkers)

    # df_ref = bpipe_handle.bulk_data_request(
    #     ["/isin/US797440CG74"],
    #     ["DES_CASH_FLOW"],
    #     p_overrides = {"USER_LOCAL_TRADE_DATE": "20251126"}
    #     )
    # print(df_ref)

    # df_ref = bpipe_handle.bulk_data_request(
    #     ["/isin/GB00BMBL1D50"],
    #     # ["/isin/US797440CG74"],
    #     "EX_DVD_SCHEDULE",
    #     p_overrides = {"USER_LOCAL_TRADE_DATE": "20251126"},
    #     to_df=False,
    #     )
    # print(df_ref)

    df_hist = pd.DataFrame()
    for i in range(2000, 2026):
        print(i)
        # print(datetime(i, 1, 1).strftime("%Y%m%d"), datetime(i, 12, 31).strftime("%Y%m%d"))
        overrides = {"START_DT": datetime(i, 1, 1).strftime("%Y%m%d"), "END_DT": datetime(i, 12, 31).strftime("%Y%m%d")}
        print(overrides)
        df_ref = bpipe_handle.bulk_data_request(
        ["FDTR Index", "INJCJC Index", "NFP TCH Index", "CPI YOY Index", "CPI CHNG Index", "NAPMPMI Index", "CONSSENT Index", "RSTAMOM Index", "FDIDFDMO Index", "ADP CHNG Index", "FEDMMINU Index", "PCE DEFM Index"],
        "ECO_FUTURE_RELEASE_DATE_LIST", # "ECO_RELEASE_DT_LIST",
        p_overrides = overrides,
        )
        # print(df_ref)
        df_hist = pd.concat([df_hist, df_ref], ignore_index=True)
    
    # print(df_hist.head(10))
    df_hist["Release Dates & Times"] = pd.to_datetime(df_hist["Release Dates & Times"])  
    df_hist["WHEN_UPDATED_UTC"] = pd.to_datetime(get_now("UTC"))
    print(df_hist.shape)
    df_hist.sort_values(by=["identifier", "Release Dates & Times"], inplace=True)
    df_hist.to_csv("events_hist.csv", index=False)


    # df_ref = bpipe_handle.bulk_data_request(
    #     ["FDTR Index", "INJCJC Index", "NFP TCH Index", "CPI YOY Index", "CPI CHNG Index", "NAPMPMI Index", "CONSSENT Index", "RSTAMOM Index", "FDIDFDMO Index", "ADP CHNG Index"],
    #     "ECO_FUTURE_RELEASE_DATE_LIST", # "ECO_RELEASE_DT_LIST",
    #     p_overrides = {"START_DT": "20000101", "END_DT": "20261231"},
    #     )
    # print(df_ref.shape)