class WebSocketClient {
    private firstConnection: boolean = true;
    private symbol: string;
    private subscriberId: string;
    private wsUrl: string;
    private connection: WebSocket;
    private isClosing: boolean = false;
    private onMessageCallback: (data: any) => void;

    constructor(symbol: string, subscriberId: string, onMessageCallback: (data: any) => void) {
        this.symbol = symbol;
        this.subscriberId = subscriberId;
        this.onMessageCallback = onMessageCallback;

        // Determine the correct protocol to use ('ws:' or 'wss:')
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        // Use window.location.host to get the hostname and port
        const wsHost = window.location.host;
        this.wsUrl = `${wsProtocol}//${wsHost}/ws/${this.symbol}/${this.subscriberId}`;

        this.connection  = this.initWebSocket();
    }

    private initWebSocket(): WebSocket {
        this.connection = new WebSocket(this.wsUrl);

        this.connection.onopen = () => {
            if (this.firstConnection) {
                Logger.log('WebSocket connection established');
                this.firstConnection = false;
            } else {
                Logger.log('WebSocket has successfully reconnected to ' + this.wsUrl);
                Logger.showStatus('WebSocket has successfully reconnected' + this.wsUrl, 'info', Config.get<number>('succesfullStatusMessageDuration') ) ;
            }
        };

        this.connection.onmessage = (event: MessageEvent) => {
            const data = JSON.parse(event.data);
            if (this.onMessageCallback && typeof this.onMessageCallback === 'function') {
                this.onMessageCallback(data);
            } else {
                Logger.error('onMessageCallback is not a function');
            }
        };

        this.connection.onerror = (error: Event) => {
            Logger.error('WebSocket error: ' + (error as ErrorEvent).message);
        };

        this.connection.onclose = (event: CloseEvent) => {
            Logger.log('WebSocket connection closed');
            if (!this.isClosing) {
                this.reconnect();
            }
        };

        window.onbeforeunload = () => {
            this.isClosing = true;
            this.connection.close();
        };

        return this.connection;
    }

    private reconnect(): void {
        setTimeout(() => {
            Logger.log('Attempting to reconnect WebSocket...');
            this.initWebSocket();
        }, 1000); // Reconnect after 1 second delay
    }
}
