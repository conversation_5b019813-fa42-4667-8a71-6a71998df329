[Unit]
Description=<PERSON><PERSON> Power Runner - downloads all the end points.
After=network.target

[Service]
Type=oneshot
EnvironmentFile=/jfs/tech1/apps/datait/jg-code/secure/prod/jg_data_pipelines.env
ExecStart=/bin/bash -c ' \
export MAMBA_EXE="/jfs/tools/conda/exec/micromamba"; \
export MAMBA_ROOT_PREFIX="/jfs/tech1/conda/"; \
__mamba_setup="$("$MAMBA_EXE" shell hook --shell bash --root-prefix "$MAMBA_ROOT_PREFIX" 2> /dev/null)"; \
eval "$__mamba_setup"; \
unset __mamba_setup; \
micromamba activate tech1-datait-analytics-light; \
python /jfs/tech1/apps/datait/jg-code/prod/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_processor.py actual_load  >> /opt/data/process_logs/kpler_power_actual_load_$(date +%%Y%%m%%d).log 2>&1; \
python /jfs/tech1/apps/datait/jg-code/prod/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_processor.py avail_per_unit  >> /opt/data/process_logs/kpler_power_avail_per_unit_$(date +%%Y%%m%%d).log 2>&1; \
python /jfs/tech1/apps/datait/jg-code/prod/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_processor.py avail_per_fuel_type  >> /opt/data/process_logs/kpler_power_avail_per_fuel_type_$(date +%%Y%%m%%d).log 2>&1;\
python /jfs/tech1/apps/datait/jg-code/prod/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_processor.py gen_per_fuel_type  >> /opt/data/process_logs/kpler_power_gen_per_fuel_type_$(date +%%Y%%m%%d).log 2>&1;\ 
python /jfs/tech1/apps/datait/jg-code/prod/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_processor.py gen_per_unit  >> /opt/data/process_logs/kpler_power_gen_per_unit_$(date +%%Y%%m%%d).log 2>&1;\ 
python /jfs/tech1/apps/datait/jg-code/prod/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_processor.py forecast_load  >> /opt/data/process_logs/kpler_power_forecast_load_$(date +%%Y%%m%%d).log 2>&1;\ 
python /jfs/tech1/apps/datait/jg-code/prod/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_processor.py consumption_per_fuel_type  >> /opt/data/process_logs/kpler_power_consumption_per_fuel_type_$(date +%%Y%%m%%d).log 2>&1;\ 
python /jfs/tech1/apps/datait/jg-code/prod/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_processor.py commercial_schedules  >> /opt/data/process_logs/kpler_power_commercial_schedules_$(date +%%Y%%m%%d).log 2>&1'
Restart=no

[Install]
WantedBy=multi-user.target