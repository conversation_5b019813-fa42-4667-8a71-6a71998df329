import pandas as pd
import re
import logging
import os, sys,json
from datetime import datetime, timezone
sys.path.append(os.getcwd())
import json
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '../../../source/bin')))
from strunner import *
setupEnvironment()
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)
from utils.snowflake.adaptor import SnowflakeAdaptor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def process_securities_valuations(file_path, latest_version=None):
    
    init_df = pd.read_excel(file_path, nrows=8)
    metadata = init_df.iloc[-1,0]
    metadata_dict = {key.strip().replace(" ", "_"): value for key, value in re.findall(r'([\w\s]+)\[([^\]]+)\]', metadata)}
    metadata_df = pd.DataFrame([metadata_dict])
    pricing_date_str = metadata_df['Pricing_Date'].iloc[0]
    pricing_date_obj = datetime.strptime(pricing_date_str, "%d-%b-%Y")
    metadata_df['Pricing_Date'] = pricing_date = pricing_date_obj.strftime("%Y-%m-%d")
    
    knowledge_date_str = metadata_df['Knowledge_Date'].iloc[0]
    knowledge_date_obj = datetime.strptime(knowledge_date_str, "%d-%b-%Y %H:%M:%S.%f")
    metadata_df['Knowledge_Date'] = knowledge_date = knowledge_date_obj.strftime("%Y-%m-%d %H:%M:%S.%f")

    securities_df = pd.read_excel(file_path, skiprows=11, skipfooter=8)
    
    if "ME" in file_path:
        securities_df.insert(0, 'Version', latest_version)
        metadata_df.insert(0, 'Version', latest_version)
    else:
        securities_df.insert(0, 'Knowledge_Date', knowledge_date)
        securities_df.insert(0, 'Pricing_Date', pricing_date)
    
    securities_df.columns = securities_df.columns.str.strip().str.replace("/ ", "/").str.replace("/", "_or_").str.replace(" ","_").str.replace("(", "").str.replace(")", "")
    securities_df.columns = [f"_{col}" if col[0].isdigit() else col for col in securities_df.columns]
    
    securities_df.rename(columns = {'Client.1':'Client_Id'}, inplace = True) 
    return metadata_df, securities_df    

def get_files_with_substring_and_date(rawdata_directory, substring, date):
        date_obj = datetime.strptime(date, '%Y-%m-%d')

        # Get the list of files in the current directory
        files = os.listdir(rawdata_directory)
        filtered_files = []
        
        for f in files:
            if substring in f :
                mod_time = datetime.fromtimestamp(os.path.getmtime(f'{rawdata_directory}/{f}'))
                if mod_time.date() == date_obj.date():
                    filtered_files.append(f'{rawdata_directory}/{f}')

        # Sort the filtered files by last modified time
        sorted_files = sorted(filtered_files, key=lambda x: os.path.getmtime(x))
        return sorted_files


if __name__ == "__main__":
    
    configPath = os.environ.get('CONFIG_PATH', os.getcwd())
    with open(f'{configPath}/config.json', 'r') as f:
        config = json.load(f)

    def jg_config_path():
        return config["JG_CONFIG_PATH"]
    
    def jg_rawdata_path():
        return config["JG_DATA_PATH"]

    def read_config_secrets():
        config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
        with open(config_secret_path, 'r') as f:
            config_secret = json.load(f)
        return config_secret
    objconfig = read_config_secrets()

    os.environ['SF_USERNAME'] = objconfig['sf_user']
    os.environ['SF_PASSWORD'] = objconfig['sf_password']
    os.environ['SF_DATABASE'] = objconfig['ssnc_database']
    os.environ['SF_WAREHOUSE'] = objconfig['ssnc_warehouse']
    os.environ['SF_ROLE'] = objconfig['ssnc_role']
    os.environ['SF_SCHEMA'] = schema = objconfig['ssnc_schema']
    
    jfs_rawdata_directory = f'{jg_rawdata_path()}/SSandC'
    sf_adaptor = SnowflakeAdaptor(database=objconfig['ssnc_database'], warehouse=objconfig['ssnc_warehouse'], role=objconfig['ssnc_role'])
    
    substring = 'Jainglobal Sec Report for client'
    date = datetime.now(timezone.utc).date().strftime('%Y-%m-%d')
    logger.info(f"Fetching files for date: '{date}'")
    files = get_files_with_substring_and_date(jfs_rawdata_directory, substring, date)
    
    
    logger.info(f"Files for processing: {files}")
    for file_path in files:  
        logger.info(f"\n\n\nProcessing file:: {file_path}")
        file_path = file_path.replace(f'{jfs_rawdata_directory}/', '')
        metadata_table = f'{schema}.SECURITIES_METADATA'
        metadata_stage = f'{schema}.STG_SEC_METADATA'

        if "ME" in file_path:
            stage_list = sf_adaptor.read_data(schema, f'LIST @{schema}.STG_SEC_METADATA;')
            loaded_files_list = stage_list['name'].to_list()
            for i in range(len(loaded_files_list)):
                loaded_files_list[i] = loaded_files_list[i].replace('stg_sec_metadata/', '').replace('.gz', '')
            
            if file_path.replace(' ', '_') in loaded_files_list:
                logger.info(f"Data is already loaded for file '{file_path}'")
                continue
           
            schema_name = objconfig['ssnc_schema']
            table_name = f'{schema}.ME_SECURITIES_VALUATION'
            staging_table_name = f'STAGING.ME_SECURITIES_VALUATION'
            stage_name = f'{schema}.STG_ME_SECURITIES_VALUATION'
            
            fetch_query = f"SELECT * FROM {table_name} WHERE VERSION = (SELECT MAX(VERSION) FROM {table_name});"

            existing_data_df = sf_adaptor.read_data(schema_name, fetch_query, objconfig['ssnc_role']) 

            latest_version = sf_adaptor.read_data(schema_name, f"SELECT MAX(VERSION) FROM {table_name}", objconfig['ssnc_role'])
            latest_version = 0 if latest_version.iloc[0,0] is None else latest_version.iloc[0,0]

            metadata_df, data_df = process_securities_valuations(f'{jfs_rawdata_directory}/{file_path}', latest_version)
            logger.info("Completed processing Securities ME Report data")

            # load staging data
            sf_adaptor.execute_query(schema_name, f'TRUNCATE {staging_table_name}')

            sf_adaptor.load_df(data_df, file_path.replace(' ', '_'), 'STAGING', staging_table_name, stage_name)
            staging_data_df = sf_adaptor.read_data(schema_name, f'SELECT * FROM {staging_table_name}', objconfig['ssnc_role'])

            if not existing_data_df.empty and existing_data_df.equals(staging_data_df):
                fetch_query = f"SELECT MAX(DATE(KNOWLEDGE_DATE)) FROM {metadata_table} WHERE VERSION = (SELECT MAX(VERSION) FROM {metadata_table});"
                KNOWLEDGE_DATE = sf_adaptor.read_data(schema_name, fetch_query, objconfig['ssnc_role'])
                if(str(KNOWLEDGE_DATE.iloc[0,0])==date):
                    logger.info("Same data as previous ME file, no new data to load")
                else:
                    logger.info("Updating METADATA table to point to previous version")
                    sf_adaptor.load_df(metadata_df, file_path.replace(' ', '_'), schema_name, metadata_table, metadata_stage)

            else:
                logger.info("Uploading Securities Valuation ME data...")
                metadata_df['Version'] = metadata_df['Version']+1 
                data_df['Version'] = data_df['Version']+1
                sf_adaptor.load_df(metadata_df, file_path.replace(' ', '_'), schema_name, metadata_table, metadata_stage)
                sf_adaptor.load_df(data_df, file_path.replace(' ', '_'), schema_name, table_name, stage_name, True)
        else:
            stage_list = sf_adaptor.read_data(schema, f'LIST @{schema}.STG_SECURITIES_VALUATION;')
            loaded_files_list = stage_list['name'].to_list()
            for i in range(len(loaded_files_list)):
                loaded_files_list[i] = loaded_files_list[i].replace('stg_securities_valuation/', '').replace('.gz', '')
        
            if file_path.replace(' ', '_') in loaded_files_list:
                logger.info(f"Data is already loaded for file '{file_path}'")
                continue
            
            schema_name = objconfig['ssnc_schema']
            table_name = f'{schema}.SECURITIES_VALUATION'
            stage_name = f'{schema}.STG_SECURITIES_VALUATION'        
            
            metadata_df, data_df = process_securities_valuations(f'{jfs_rawdata_directory}/{file_path}')
            logger.info("Completed processing data")

            sf_adaptor.execute_query(schema_name, f'TRUNCATE {table_name}')
            sf_adaptor.load_df(data_df, file_path.replace(' ', '_'), schema_name, table_name, stage_name)