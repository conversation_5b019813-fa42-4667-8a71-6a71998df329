import dotenv
import sys
from loguru import logger


# Configure log
logger.remove()
LOG_LEVEL = "INFO"  # Can be "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"

# Add handler with custom format
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
    level=LOG_LEVEL,
    colorize=True
)
 
# Change the values of the variables below to match your environment
BASE_DATA_DIR = "/jfs/tech1/apps/rawdata/metelogica/api"
 