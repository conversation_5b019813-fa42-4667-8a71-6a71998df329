select * from METEOLOGICA.API.OBSERVATION WHERE CONTENT_ID = 1515  and ISSUE_DATE = '2022-01-01 08:25:15.000';
select * from meteologica.api.observation
WHERE CONTENT_ID IN (1515,883, 881, 877, 879, 1517);

create or replace view meteologica.api.vw_observation as
with consolidated as (
    SELECT * FROM meteologica.api.observation
    WHERE issue_date < DATE('2025-01-01')
    UNION ALL 
    SELECT * FROM meteologica.ongoing.observation
    WHERE issue_date >= DATE('2025-01-01')
), observation as (
    select content_id, content_name, 
    issue_date,
    timezone, unit, installed_capacity, update_id, source_file,
        to_varchar(data:"From yyyy-mm-dd hh:mm") from_datetime,
        to_varchar(data:"To yyyy-mm-dd hh:mm") to_datetime,
        to_varchar(data:"UTC offset from (UTC+/-hhmm)") utc_offset_from,
        to_varchar(data:"UTC offset to (UTC+/-hhmm)") utc_offset_to,
        -- special cases
        to_varchar(data:"Date yyyy-mm-dd") plain_date,
        to_varchar(data:"UTC offset(HHmm)") plain_utc_offset,
        to_varchar(data:"observation") observation
        from consolidated
), observation_utc as (
    select content_id, content_name, issue_date, unit, installed_capacity, update_id, source_file, observation,
    CASE WHEN content_id IN (1515,883, 881, 877, 879, 1517) THEN
            to_timestamp_ntz(convert_timezone(
                'UTC', 
                TO_TIMESTAMP_TZ(plain_date, 'YYYY-MM-DD')
            ))
        ELSE
            to_timestamp_ntz(convert_timezone(
                'UTC', 
                TO_TIMESTAMP_TZ(FROM_DATETIME || ' ' || split(utc_offset_from, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
            )) 
        END AS FROM_DATETIME,
    CASE WHEN content_id IN (1515,883, 881, 877, 879, 1517) THEN
            TIMEADD(HOUR, 24, to_timestamp_ntz(convert_timezone(
                'UTC', 
                TO_TIMESTAMP_TZ(plain_date, 'YYYY-MM-DD')
            )))
        ELSE
            to_timestamp_ntz(convert_timezone(
                'UTC', 
                TO_TIMESTAMP_TZ(TO_DATETIME || ' ' || split(utc_offset_to, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
            )) 
        END AS TO_DATETIME
    from observation
)
select * from observation_utc;

--17248479
select count(*) from vw_observation limit 100;
select * from vw_observation 
WHERE CONTENT_ID IN (1515,883, 881, 877, 879, 1517)
limit 100;