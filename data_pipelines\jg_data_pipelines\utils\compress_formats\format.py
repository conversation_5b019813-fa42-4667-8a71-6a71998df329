import logging
from abc import ABC, abstractmethod
from utils.compress_formats.linux_tools import ExecuteCMD


class Format(ABC):
    @staticmethod
    @abstractmethod
    def uncompress(file, target):
        pass


class Gzip(Format):
    def uncompress(file, target):
        filename = file.split('/')[-1]
        target_name = filename.split('.gz')[0]
        cmd = f"gunzip '{file}' -c > {target}/'{target_name}'"
        ExecuteCMD.execute(cmd)


class Tar(Format):
    def uncompress(file, target):
        cmd = f'tar -xf {file} -C {target}'
        ExecuteCMD.execute(cmd)


class Uncompressed(Format):
    def uncompress(file, pattern, target):
        cmd = f"cp {file}/{pattern} '{target}'"
        ExecuteCMD.execute(cmd)


class Zip(Format):
    def uncompress(file, pattern, target):
        try:
            cmd = f"unzip -j '{file}' '{pattern}' -d '{target}'"
            ExecuteCMD.execute(cmd)
        except Exception as e:
            logging.warning(f'File {file}/{pattern} not found, trying as folder')
            logging.warning(e)
            cmd = f"unzip '{file}' '{pattern}/*' -d '{target}'"
            ExecuteCMD.execute(cmd)
