import os
import numpy as np
import pandas as pd
from datetime import datetime
import pytz

from utils.postgres.adaptor import PostgresAdaptor
import bloomberg.bpipe as bpipe_helper


if __name__ == "__main__":
    loader = PostgresAdaptor(
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df_securities = loader.execute_query(
        "select security_code_id, security_code, bbg_full_ticker from eqvol.sr_security_ref;"
    )
    dict_securities = dict(
        zip(df_securities["bbg_full_ticker"], df_securities["security_code_id"])
    )
    
    bpipe_instance = ("Tech1ProdBPipe47537.jainglobal.net", 8194)
    bpipe_app = "JAIN:pmdashboard-bps"
    bpipe_handle = bpipe_helper.BpipeHandler(bpipe_instance, bpipe_app)
    api_req_time = datetime.now(pytz.utc).replace(tzinfo=None)
    df_price = bpipe_handle.fetch_live(
        tickers = dict_securities.keys(),
        fields=["BID_ALL_SESSION", "ASK_ALL_SESSION", "PX_LAST", "BID_SIZE_ALL_SESSIONS_RT", "ASK_SIZE_ALL_SESSIONS_RT", "LAST_UPDATE_BID_RT", "LAST_UPDATE_ASK_RT"], 
    )
    df_price.reset_index(inplace=True, names="bbg_full_ticker")
    ref_date = api_req_time.date()
    df_price['BID_SIZE_ALL_SESSIONS_RT'] = df_price['BID_SIZE_ALL_SESSIONS_RT'].where(pd.notna(df_price['BID_SIZE_ALL_SESSIONS_RT']), None).astype('Int64')
    df_price['ASK_SIZE_ALL_SESSIONS_RT'] = df_price['ASK_SIZE_ALL_SESSIONS_RT'].where(pd.notna(df_price['ASK_SIZE_ALL_SESSIONS_RT']), None).astype('Int64')
    df_price["JG_API_REQ_TIMESTAMP"] = api_req_time
    df_price["JG_API_REQ_TIMESTAMP_DATE"] = ref_date
    df_price["security_code_id"] = df_price["bbg_full_ticker"].map(dict_securities)

    df_price.columns = df_price.columns.str.lower()

    # df_price.to_parquet(
    #     "/jfs/tech1_share/pulkit.vora/eqvol_stock_quotes_bbg/bbg_stock_quotes_" + api_req_time.strftime('%Y%m%d%H%M%S') + ".parquet",
    #     index=False        
    # )