name: Test and Coverage Report

on:
  workflow_dispatch:

jobs:
  pyTest-coverage:
    runs-on:
      group: core

    env:
      JGDATA_PATH: source
      JGLIB_PATH: source
      STCOMMON_PATH: source/stcommon
      BUILDINPARALLEL: false
      SPARK_DIST_CLASSPATH: source/jars/iceberg-spark-runtime.jar
      CONFIG_PATH: source
      JG_CONFIG_PATH: /tmp

    steps:
    - name: Checkout repo
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Conda Setup in Runner
      working-directory: source/dbt/dbt_data_platform
      run: |
        export MAMBA_EXE='/jfs/tools/conda/exec/micromamba'
        export MAMBA_ROOT_PREFIX='/jfs/tech1/conda/'
        eval "$($MAMBA_EXE shell hook --shell bash --root-prefix "$MAMBA_ROOT_PREFIX")"
        micromamba activate tech1-datait

    - name: Write GitHub secrets to /tmp
      run: |
        cat <<EOF > /tmp/config_secret.json
        ${{ secrets.CONFIG_SECRET_JSON }}
        EOF

    - name: <PERSON> JG_CONFIG_PATH in source/config.json to /tmp
      run: |
        jq '.JG_CONFIG_PATH = "/tmp"' source/config.json > source/config_tmp.json
        mv source/config_tmp.json source/config.json

    - name: Run tests with coverage
      run: |
        export MAMBA_EXE='/jfs/tools/conda/exec/micromamba'
        export MAMBA_ROOT_PREFIX='/jfs/tech1/conda/'
        eval "$($MAMBA_EXE shell hook --shell bash --root-prefix "$MAMBA_ROOT_PREFIX")"
        micromamba activate tech1-datait
        pytest source/tests --cov=source --cov-report=html --cov-report=term

    - name: Upload HTML coverage report
      uses: actions/upload-artifact@v4
      with:
        name: htmlcov
        path: htmlcov
