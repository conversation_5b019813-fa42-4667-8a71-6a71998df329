# Meteologica Data Pipeline

## Overview
The `MeteologicaProcessor` class manages a data pipeline that:

1. Fetches weather data from Meteologica API
2. Processes JSON responses
3. Converts to Parquet format
4. Loads into Snowflake

## Required Environment Variables

```bash
# Required - Meteologica API Authentication 
export METEOLOGICA_DATABASE="METEOLOGICA_DATABASE"
export METEOLOGICA_SCHEMA="METEOLOGICA_SCHEMA"
export METEOLO<PERSON><PERSON>_STAGE="your_stage"
export SF_USERNAME="your_user"
export SF_PASSWORD="your_pwd"
export SF_WAREHOUSE="your_warehouse"
export SF_ROLE="your_role"

# Optional - Logging Configuration
export LOG_LEVEL="INFO"  # Options: DEBUG, INFO, WARNING, ERROR
```

## Directory Structure
```
BASE_DATA_DIR/
├── latest/
│   ├── raw_json/
│   │   └── {timestamp}/  # Raw JSON files
│   └── processed/        # Parquet files
```

## Running the Pipeline

### From Command Line
```bash
# From project root
python jg_data_pipelines/meteologica/meteologica_processor.py
```

### From VS Code
Create `.vscode/launch.json`:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Meteologica Pipeline",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/jg_data_pipelines/meteologica/meteologica_processor.py",
            "console": "integratedTerminal",
            "env": {
                "METEOLOGICA_DATABASE":"METEOLOGICA_DATABASE",
                "METEOLOGICA_SCHEMA":"your_password",
                "METEOLOGICA_STAGE":"your_stage",
                "SF_USERNAME":"your_user",
                "SF_PASSWORD":"your_pwd",
                "SF_WAREHOUSE":"your_warehouse",
                "SF_ROLE":"your_role"
            }
        }
    ]
}
```

## Data Types Processed

- NORMAL: Standard observations
- OBSERVATION: Current weather data
- REANALYSIS: Historical reanalysis
- FORECAST types:
  - DETERMINISTIC
  - ENSEMBLE
  - EXTENDED

## Output Files
- Raw JSON: `{BASE_DATA_DIR}/latest/raw_json/{timestamp}/{content_id}_{update_id}.json`
- Processed Parquet: `{BASE_DATA_DIR}/latest/processed/{timestamp}_{data_type}.parquet`

## Process Flow
1. Fetch latest API contents (last 500 seconds)
2. Save raw JSON responses
3. Validate JSON structure
4. Convert to Parquet format
5. Load to Snowflake (optional)

## Error Handling
- Invalid JSON structure: Logged and skipped
- API errors: Full error details logged
- Missing data: Noted in logs and continues processing


## Steps to run `metodolia_parser`

- run `jg_data_pipelines/meteologica/meteologica_parser.py` with the launch json left at the bottom
- It's got hardcoded target db, schema, stage, etc. for now at the top of the file
- Result parquet files will be written to _PROCESSED variable path
  - Keep in mind that if the file is already processed in the destination, it'll be skipped in future runs
  - Once the process finishes, tables should be populates
- SQL code under `jg_data_pipelines/meteologica/SQL`
  - Tables will be under snowflake poc_db.meteologica (Already created)
  - Views will be under snowflake poc_db.meteologica (Already created)

- **Pending**
  - Full loads for 2 source folders:
    - _RAW = `/jfs/tech1_share/samuel.lasker/meteo_results/`
    - _RAW = `/jfs/tech1_share/samuel.lasker/meteo_results_2/`
  - Need to check if performance is good enough. If not, try running many zipfiles in parallel


launch.json:
```json
{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Python Debugger: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "args": [
                ],
            "env": {
                "SF_USERNAME": "***",
                "SF_PK_FILE": "./.secrets/your_key.p8",
                "SF_PK_PASSWORD": "keypass",
            }
        }
    ]
}
```