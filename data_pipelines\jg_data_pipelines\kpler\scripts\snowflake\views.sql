USE DATABASE KPLER;
USE SCHEMA KPLER_POWER;

create or replace view KPLER.KPLER_POWER.VW_ACTUAL_LOAD 
AS

select TIMESTAMP, 
PROVIDER, 
COUNTRY, 
SUBCOUNTRY, 
DEMAND,
CAST(NULL AS VARCHAR) AS JSON_FILE
FROM KPLER.KPLER_POWER.ACTUAL_LOAD 
WHERE TIMESTAMP <= '2025-01-27 23:00:00.000'

union all 

SELECT TIMESTAMP, 
PROVIDER, 
COUNTRY, 
SUBCOUNTRY, 
DEMAND, 
JSON_FILE
FROM KPLER.ONGOING.ACTUAL_LOAD 
WHERE TIMESTAMP > '2025-01-27 23:00:00.000';


create or replace view KPLER.KPLER_POWER.vw_availability_by_fueltype
AS
select TIMESTAMP, 
A<PERSON><PERSON>ABILITY_AMOUNT, 
AS_OF, 
PROVIDER, 
COUNTRY, 
TIMEZONE, 
<PERSON>E<PERSON><PERSON>, 
FUEL_TYPE, 
<PERSON><PERSON><PERSON>_FILE
FROM KPLER.KPLER_POWER.AVAILABILITY_BY_FUELTYPE
WHERE AS_OF <= '2025-02-25 10:00:00.000'

UNION ALL 

select TIMESTAMP, 
AVAILABILITY_AMOUNT, 
AS_OF, 
PROVIDER, 
COUNTRY, 
TIMEZONE, 
LEVEL, 
FUEL_TYPE, 
JSON_FILE
FROM KPLER.ONGOING.AVAILABILITY_BY_FUELTYPE
WHERE AS_OF > '2025-02-25 10:00:00.000';

create or replace view KPLER.KPLER_POWER.vw_availability_per_units
AS
select TIMESTAMP, 
AVAILABILITY_AMOUNT, 
TYPE, 
AS_OF, 
PROVIDER, 
COUNTRY, 
UNIT, 
UNIT_NAME, 
GENERATION_CODE, 
PRODUCTION_CODE, 
ASSET_TYPE, 
FUEL_TYPE, 
TIMEZONE, 
JSON_FILE
FROM KPLER.KPLER_POWER.AVAILABILITY_PER_UNITS_FULL
WHERE AS_OF <= '2025-02-16 10:00:00.000'

UNION ALL 

select TIMESTAMP, 
AVAILABILITY_AMOUNT, 
TYPE, 
AS_OF, 
PROVIDER, 
COUNTRY, 
UNIT, 
UNIT_NAME, 
GENERATION_CODE, 
PRODUCTION_CODE, 
ASSET_TYPE, 
FUEL_TYPE, 
TIMEZONE, 
JSON_FILE
FROM KPLER.ONGOING.AVAILABILITY_PER_UNITS
WHERE AS_OF > '2025-02-16 10:00:00.000';

create or replace view KPLER.KPLER_POWER.vw_COMMERCIAL_SCHEDULES
AS
select TIMESTAMP, 
PROVIDER, 
AREA_TYPE, 
MARKET_TYPE, 
SOURCE_COUNTRY, 
TARGET_COUNTRY, 
VALUE,
CAST(NULL AS VARCHAR) AS JSON_FILE
from KPLER.KPLER_POWER.COMMERCIAL_SCHEDULES
WHERE TIMESTAMP <= '2025-01-27 23:00:00.000'

union all 

select TIMESTAMP, 
PROVIDER, 
AREA_TYPE, 
MARKET_TYPE, 
SOURCE_COUNTRY, 
TARGET_COUNTRY, 
VALUE, 
JSON_FILE
from KPLER.ONGOING.COMMERCIAL_SCHEDULES
WHERE TIMESTAMP > '2025-01-27 23:00:00.000';

CREATE OR REPLACE VIEW KPLER.KPLER_POWER.VW_GENERATION_BY_FUEL_TYPE
AS
select TIMESTAMP, 
PROVIDER, 
COUNTRY, 
FUEL_TYPE, 
GENERATION, 
CAST(NULL AS VARCHAR) AS JSON_FILE
FROM KPLER.KPLER_POWER.GENERATION_BY_FUELTYPE_AGGREGATED
where TIMESTAMP <= '2025-01-27 23:00:00.000'
union all
select TIMESTAMP, 
PROVIDER, 
COUNTRY, 
FUEL_TYPE, 
GENERATION, 
JSON_FILE
FROM KPLER.ONGOING.GENERATION_BY_FUELTYPE
where TIMESTAMP > '2025-01-27 23:00:00.000';

CREATE OR REPLACE VIEW KPLER.KPLER_POWER.VW_GENERATION_BY_UNIT
AS
select TIMESTAMP, 
PROVIDER, 
COUNTRY, 
ASSET_ID, 
GENERATION, 
UNIT_NAME, 
ASSET_TYPE, 
FUEL_TYPE,
CAST(NULL AS VARCHAR) AS JSON_FILE
from KPLER.KPLER_POWER.GENERATION_BY_FUELTYPE_UNIT 
where TIMESTAMP <= '2025-01-27 23:00:00.000'

union all 

select TIMESTAMP, 
PROVIDER, 
COUNTRY, 
ASSET_ID, 
GENERATION, 
UNIT_NAME, 
ASSET_TYPE, 
FUEL_TYPE, 
JSON_FILE
from KPLER.ONGOING.GENERATION_BY_UNIT
where TIMESTAMP >= '2025-01-27 23:00:00.000';

CREATE OR REPLACE VIEW KPLER.KPLER_POWER.VW_CONSUMPTION_PER_FUELTYPE
AS
select TIMESTAMP, 
PROVIDER, 
COUNTRY, 
SUBCOUNTRY, 
FUEL_TYPE, 
CONSUMPTION,
CAST(NULL AS VARCHAR) AS JSON_FILE
from KPLER.KPLER_POWER.CONSUMPTION_FUELTYPE
where TIMESTAMP <= '2025-01-23 23:00:00.000'

union all 

select TIMESTAMP, 
PROVIDER, 
COUNTRY, 
SUBCOUNTRY, 
FUEL_TYPE, 
CONSUMPTION, 
JSON_FILE
from KPLER.ONGOING.CONSUMPTION_PER_FUELTYPE
where TIMESTAMP > '2025-01-23 23:00:00.000';

CREATE OR REPLACE VIEW KPLER.KPLER_POWER.VW_FORECAST_LOAD
AS
select TIMESTAMP, 
RUNDATE, 
KIND, 
MODEL, 
COUNTRY, 
PROVIDER, 
RUN_00Z, 
RUN_06Z, 
RUN_12Z, 
RUN_18Z,
CAST(NULL AS VARCHAR) AS JSON_FILE
from KPLER.KPLER_POWER.LOAD_FORECASTS
WHERE RUNDATE <= '2025-01-14'

UNION ALL 

select TIMESTAMP, 
RUNDATE, 
KIND, 
MODEL, 
COUNTRY, 
PROVIDER, 
RUN_00Z, 
RUN_06Z, 
RUN_12Z, 
RUN_18Z, 
JSON_FILE
FROM KPLER.ONGOING.FORECAST_LOAD
WHERE RUNDATE > '2025-01-14';