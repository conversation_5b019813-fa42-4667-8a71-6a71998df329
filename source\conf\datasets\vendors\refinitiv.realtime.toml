[queries.ddl]
create_table_if_not_exists = """
CREATE TABLE IF NOT EXISTS refdata.refinitiv_real_time (
    time_stamp TIMESTAMP,
    ric VARCHAR(255),
    service VARCHAR(255),
    cusip_cd VARCHAR(255),
    ask NUMERIC,
    bid NUMERIC,
    value_dt VARCHAR(255),
    value_ts VARCHAR(255),
    PRIMARY KEY (time_stamp, ric)
)
"""

[queries.dml]
insert_data_query = """
INSERT INTO refdata.refinitiv_real_time (time_stamp, ric, service, cusip_cd, ask, bid, value_dt, value_ts)
VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
"""
