import os
import pandas as pd
from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.postgres.adaptor import PostgresAdaptor

from bloomberg.per_security.parser import BloombergParser
from utils.snowflake.adaptor import SnowflakeAdaptor

if __name__ == "__main__":
    pg_adaptor = PostgresAdaptor(
    host='tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com',
    database='postgres',
    schema='mktdata_eqvol',
    user= os.environ['RDS_SVC_MKTDATA_EQVOL_USER'],
    password=os.environ['RDS_PROD_SVC_MKTDATA_EQVOL_PASSWORD'],
    )

    df_etfs = pg_adaptor.execute_query(
        """select bbg_full_ticker from mktdata_eqvol.security_ref where security_type  IN ('ETF', 'STOCK')""",
    )
    securities = df_etfs["bbg_full_ticker"].tolist()
    per_sec_req_type = PerSecurityRequestType.getdata
    batch_name = "dp_eqvol3"
    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "sec_id": "TICKER",
        "output_format": "bulklist",
        "fields": [
            "EQY_DVD_HIST_SPLITS"
        ],
        "securities": securities,
    }
    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/eqvol/splits_hist/"
    eq_hist_splits_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)

    parser = BloombergParser(eq_hist_splits_file_path, sep='|', skipinitialspace=True, on_bad_lines='error') 
    df_data = parser.parse_data()
    df_data["BC_EQY_DVD_HIST_SPLITS_ANN_DT"] = pd.to_datetime(df_data["BC_EQY_DVD_HIST_SPLITS_ANN_DT"], format="%m/%d/%Y", errors="coerce")
    df_data["BC_EQY_DVD_HIST_SPLITS_EX_DT"] = pd.to_datetime(df_data["BC_EQY_DVD_HIST_SPLITS_EX_DT"], format="%m/%d/%Y", errors="coerce")
    df_data["BC_EQY_DVD_HIST_SPLITS_REC_DT"] = pd.to_datetime(df_data["BC_EQY_DVD_HIST_SPLITS_REC_DT"], format="%m/%d/%Y", errors="coerce")
    df_data["BC_EQY_DVD_HIST_SPLITS_PAY_DT"] = pd.to_datetime(df_data["BC_EQY_DVD_HIST_SPLITS_PAY_DT"], format="%m/%d/%Y", errors="coerce")
    df_data["BC_EQY_DVD_HIST_SPLITS_AMT"] = pd.to_numeric(df_data["BC_EQY_DVD_HIST_SPLITS_AMT"], errors="coerce")
    print(df_data)
    print(df_data.dtypes)
