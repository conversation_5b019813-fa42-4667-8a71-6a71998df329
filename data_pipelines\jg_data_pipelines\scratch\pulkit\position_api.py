import json
import requests
import time
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s:%(name)s:%(message)s', datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger(__name__)

while True:
    url = "https://dataservice.jainglobal.net/bpipe/get-position-intraday"
    payload = {}
    start_time = time.time()
    response = requests.post(url, json=payload, headers={"api_key": "4iJT9k7kY23DkoQBNMEZX7aepy7KJchs"})
    elapsed_time = time.time() - start_time

    if response.status_code == 200:
        res = response.json()
        df = pd.DataFrame.from_dict(res["positions"], orient="index")
        nos_pos = df.shape[0]
        # print("Response : " + json.dumps(response.json()))
        logger.info(f"No Connectivity Issues. Nos of Positions are {nos_pos}, Time taken is {elapsed_time:.2f} seconds")
    else:
        logger.error(f"Connectivity Issues : " + json.dumps(response.json()))

    time.sleep(5)
    

# url = " http://***********:8006/bpipe/get-position-intraday"
# api_key = ""

 
# payload = {}
# response = requests.post(url, json=payload, headers={"api_key": api_key})
# print(response.json())