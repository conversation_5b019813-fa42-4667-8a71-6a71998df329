import pytest
import pandas as pd
import numpy as np
from kpler.kpler_parser import flatten_json
import kpler.kpler_fueltype_parser as kft
from pandas.testing import assert_frame_equal

def generate_test_data(json_file_name: str):
    df_final = flatten_json(f"{json_file_name}.json")
    df_final.write_parquet(f"{json_file_name}.parquet")

def generate_fueltype_test_data(json_file_name: str):
    df_final = kft.flatten_json(f"{json_file_name}.json")
    df_final.write_parquet(f"{json_file_name}.parquet")

def empty_string_to_na(value):
    return None if value == '' else value


def test_flatten_json():
    idx = ['timestamp', 'type', 'as_of', 'provider', 'country', 'unit',
       'unit_name', 'production_code', 'asset_type', 'fuel_type',
       'availability_amount', 'timezone', 'json_file']
    df_frozen = pd.read_parquet(
        "2025-01-15T10:00:00+00:00_DE_fossil gas_2025-01.parquet"
        )
    df_flattened = flatten_json(
        "2025-01-15T10:00:00+00:00_DE_fossil gas_2025-01.json"

        ).to_pandas()
    df_frozen = df_frozen[idx]
    df_flattened = df_flattened[idx]
    assert_frame_equal(df_flattened, df_frozen)


def test_fueltype_flatten_json():
    idx = ['timestamp', 'availability_amount', 'as_of', 'provider', 'country', 'timezone', 'level', 'fuel_type', 'json_file']
    df_frozen = pd.read_parquet(
        "2022-01-01 10:00:00_eex_DE__2021-12.parquet"
        )
    df_flattened = kft.flatten_json(
        "2022-01-01 10:00:00_eex_DE__2021-12.json"
        ).to_pandas()
    df_frozen = df_frozen[idx]
    df_flattened = df_flattened[idx]
    assert_frame_equal(df_flattened, df_frozen)

if __name__ == "__main__":
    #generate_test_data("2025-01-15T10:00:00+00:00_DE_fossil gas_2025-01")
    # test_flatten_json()
    # generate_fueltype_test_data("2022-01-01 10:00:00_eex_DE__2021-12")
    test_fueltype_flatten_json()


    
