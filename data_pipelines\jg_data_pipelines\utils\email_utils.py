from confluent_kafka import Producer, KafkaException
import time
import json

_conf = {
    'bootstrap.servers': 'kafka1.jainglobal.net:9092,kafka2.jainglobal.net:9092',
}
_topic = 'fo_tools_alerts'

def delivery_report(err, msg):    
    if err is not None:
        print(f'Message delivery failed: {err}')
    else:
        print(f'Message delivered to {msg.topic()} [{msg.partition()}] at offset {msg.offset()}')

def send_email(to_recipient, subject, record_key_prefix, body = None, cc_recipient = [], from_ = None, attachment=None):
    producer = Producer(_conf)
    email_param = {
        "to": to_recipient,
        "cc": cc_recipient,
        "subject": subject,
        "from_": "<EMAIL>" if from_ is None else from_,
        "body": "" if body is None else body,
    }
    timestamp = time.strftime('%Y%m%d%H%M%S')
    record_key = f'{record_key_prefix}_{timestamp}'
    producer.produce(_topic, key=record_key, value=json.dumps(email_param), callback=delivery_report)
    producer.flush()

if __name__ == "__main__":
    send_email(["<EMAIL>"], "Email Test Subject", "Test body. Please ignore. <b>Test body in bold</b>")

