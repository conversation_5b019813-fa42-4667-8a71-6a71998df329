import pandas as pd
from sqlalchemy import create_engine, text
import psycopg2 
import logging
from typing import Optional, Union
import time
from urllib.parse import quote_plus

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class PostgresAdaptor:
    def __init__(
        self,
        host: str,
        database: str,
        schema: str,
        user: str,
        password: str,
        port: int = 5432,
        chunksize: int = 10000
    ):
        """
        Initialize the loader with database connection parameters.
        
        Args:
            host: Database host
            database: Database name
            user: Database username
            password: Database password
            port: Database port (default: 5432)
            chunksize: Number of rows to insert in each batch (default: 10000)
        """
        self.schema = schema
        self.chunksize = chunksize
        self.engine = self._create_engine(host, database, user, password, port)
        
    def _create_engine(
        self,
        host: str,
        database: str,
        user: str,
        password: str,
        port: int,
    ) -> create_engine:
        """Create SQLAlchemy engine with connection pooling configuration."""
        encoded_password = quote_plus(password)
        
        connection_string = (
            f'postgresql://{user}:{encoded_password}@{host}:{port}/{database}'
        )
        
        return create_engine(
            connection_string,
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=3600
        )
    
    def load_dataframe(
        self,
        df: pd.DataFrame,
        table_name: str,
        if_exists: str = 'append',
    ) -> bool:
        """
        Load DataFrame into PostgreSQL table with batch processing.
        
        Args:
            df: Input pandas DataFrame
            table_name: Target table name
            if_exists: How to behave if table exists ('fail', 'replace', 'append')
            dtype: Optional dictionary of column dtypes
            index: Write DataFrame index as a column
            index_label: Column label(s) for index column(s)
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            start_time = time.time()
            total_rows = len(df)
            logger.info(f"Starting to load {total_rows} rows into {table_name}")

            df.to_sql(
                table_name,
                self.engine,
                schema=self.schema,
                if_exists=if_exists,
                index=False,
                chunksize=self.chunksize,
            )            
            
            elapsed_time = time.time() - start_time
            logger.info(
                f"Successfully loaded {total_rows} rows "
                f"in {elapsed_time:.2f} seconds"
            )
            return True
            
        except Exception as e:
            logger.error(f"Error loading data: {str(e)}")
            return False
        
    def execute_query(self, query: str) -> Optional[pd.DataFrame]:
        """
        Execute a SQL query and return results as DataFrame.
        
        Args:
            query: SQL query string
            
        Returns:
            Optional[pd.DataFrame]: Query results as DataFrame or None if error
        """
        try:
            return pd.read_sql_query(query, self.engine)
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            return None
        
    def execute_query_no_ret(self, query: str):
        """
        Execute a SQL query and return results as DataFrame.
        
        Args:
            query: SQL query string
            
        Returns:
            Optional[pd.DataFrame]: Query results as DataFrame or None if error
        """
        try:
            with self.engine.connect() as conn:
                conn.execute(text(query))
                conn.commit()
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            return None
    
    def execute_statement(self, statement):
        try:
            with self.engine.connect() as conn:
                result = conn.execute(statement)
                return_ids = result.fetchall()
                conn.commit()
                return return_ids
        except Exception as e:
            logger.error(f"Error executing statement: {str(e)}")

    def close(self):
        """Close database connection."""
        self.engine.dispose()