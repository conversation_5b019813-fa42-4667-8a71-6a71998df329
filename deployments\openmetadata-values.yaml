# Default values for OpenMetadata.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1

# Overrides the openmetadata config file with the help of Environment Variables
# Below are defaults as per openmetadata-dependencies Helm Chart Values
openmetadata:
  config:
    upgradeMigrationConfigs:
      debug: false
      # You can pass the additional argument flags to the openmetadata-ops.sh migrate command
      # Example if you want to force migration runs, use additionalArgs: "--force"
      additionalArgs: ""
    # Values can be OFF, ERROR, WARN, INFO, DEBUG, TRACE, or ALL
    logLevel: INFO
    clusterName: jg-openmetadata-cluster
    openmetadata:
      host: openmetadata
      # URI to use with OpenMetadata Alerts Integrations
      uri: "http://openmetadata:8585"
      port: 8585
      adminPort: 8586
    elasticsearch:
      enabled: true
      host: ${OM_OS_ENDPOINT}
      searchType: opensearch
      port: 443
      scheme: https
      connectionTimeoutSecs: 5
      socketTimeoutSecs: 60
      batchSize: 10
      searchIndexMappingLanguage: "EN"
      keepAliveTimeoutSecs: 600
      trustStore:
        enabled: false
        path: ""
        password:
          secretRef: "elasticsearch-truststore-secrets"
          secretKey: "openmetadata-elasticsearch-truststore-password"
      auth:
        enabled: true
        username: ${OPENSEARCH_USERNAME}
        password:
          secretRef: secret
          secretKey: openmetadata-elasticsearch-password
    database:
      enabled: true
      host: ${OM_RDS_ENDPOINT}
      port: 5432
      driverClass: org.postgresql.Driver
      dbScheme: postgresql
      databaseName: ${OM_RDS_DBNAME}
      auth:
        username: ${RDS_USERNAME}
        password:
          secretRef: secret
          secretKey: openmetadata-postgresql-password
      dbParams: "allowPublicKeyRetrieval=true&useSSL=false&serverTimezone=UTC"
    pipelineServiceClientConfig:
      enabled: false
      className: "org.openmetadata.service.clients.pipeline.airflow.AirflowRESTClient"
      # endpoint url for airflow
      apiEndpoint: http://openmetadata-dependencies-web:8080
      # this will be the api endpoint url of OpenMetadata Server
      metadataApiEndpoint: http://openmetadata:8585/api
      # possible values are "no-ssl", "ignore", "validate"
      verifySsl: "no-ssl"
      hostIp: ""
      ingestionIpInfoEnabled: false
      # healthCheckInterval in seconds
      healthCheckInterval: 300
      # local path in Airflow Pod
      sslCertificatePath: "/no/path"
      auth:
        enabled: true
        username: airflow
        password:
          secretRef: secret
          secretKey: openmetadata-airflow-password
        trustStorePath: ""
        trustStorePassword:
          secretRef: ""
          secretKey: ""
    authorizer:
      enabled: true
      className: "org.openmetadata.service.security.DefaultAuthorizer"
      containerRequestFilter: "org.openmetadata.service.security.JwtFilter"
      initialAdmins:
      - "admin"
      allowedEmailRegistrationDomains:
      - "all"
      principalDomain: "open-metadata.org"
      enforcePrincipalDomain: false
      enableSecureSocketConnection: false
    authentication:
      enabled: true
      provider: "basic"
      publicKeys:
      - "http://${SERVICE_IP}/api/v1/system/config/jwks"
      authority: "https://accounts.google.com"
      clientId: ""
      callbackUrl: ""
      responseType: id_token
      jwtPrincipalClaims:
      - "email"
      - "preferred_username"
      - "sub"
      enableSelfSignup: true
      ldapConfiguration:
        host: localhost
        port: 10636
        dnAdminPrincipal: "cn=admin,dc=example,dc=com"
        dnAdminPassword:
          secretRef: ldap-admin-secret
          secretKey: openmetadata-ldap-secret
        userBaseDN: "ou=people,dc=example,dc=com"
        mailAttributeName: email
        maxPoolSize: 3
        sslEnabled: false
        # Possible values are CustomTrustStore, HostName, JVMDefault, TrustAll
        truststoreConfigType: TrustAll
        trustStoreConfig:
          customTrustManagerConfig:
            trustStoreFilePath: ""
            trustStoreFilePassword:
              secretRef: ""
              secretKey: ""
            trustStoreFileFormat: ""
            verifyHostname: true
            examineValidityDates: true
          hostNameConfig:
            allowWildCards: false
            acceptableHostNames: []
          jvmDefaultConfig:
            verifyHostname: true
          trustAllConfig:
            examineValidityDates: true
      saml:
        debugMode: false
        idp:
          entityId: ""
          ssoLoginUrl: ""
          idpX509Certificate:
            secretRef: ""
            secretKey: ""
          authorityUrl: "http://openmetadata:8585/api/v1/saml/login"
          nameId: "urn:oasis:names:tc:SAML:2.0:nameid-format:emailAddress"
        sp:
          entityId: "http://openmetadata:8585/api/v1/saml/metadata"
          acs: "http://openmetadata:8585/api/v1/saml/acs"
          spX509Certificate:
            secretRef: ""
            secretKey: ""
          callback: "http://openmetadata:8585/saml/callback"
        security:
          strictMode: false
          tokenValidity: 3600
          sendEncryptedNameId: false
          sendSignedAuthRequest: false
          signSpMetadata: false
          wantMessagesSigned: false
          wantAssertionsSigned: false
          wantAssertionEncrypted: false
          wantNameIdEncrypted: false
          keyStoreFilePath: ""
          keyStoreAlias:
            secretRef: ""
            secretKey: ""
          keyStorePassword:
            secretRef: ""
            secretKey: ""

    jwtTokenConfiguration:
      enabled: true
      # File Path on Airflow Container
      rsapublicKeyFilePath: "/opt/openmetadata/conf/public_key.der"
      # File Path on Airflow Container
      rsaprivateKeyFilePath: "/opt/openmetadata/conf/private_key.der"
      jwtissuer: "open-metadata.org"
      keyId: "Gb389a-9f76-gdjs-a92j-0242bk94356"
    fernetkey:
      value: "jJ/9sz0g0OHxsfxOoSfdFdmk3ysNmPRnH3TUAbz3IHA="
      secretRef: ""
      secretKey: ""
    eventMonitor:
      enabled: true
      # Possible values are prometheus and cloudwatch
      type: prometheus
      batchSize: 10
      pathPattern:
      - "/api/v1/tables/*"
      - "/api/v1/health-check"
      # For value p99=0.99, p90=0.90, p50=0.50 etc.
      latency: []
      # - "p99=0.99"
      # - "p90=0.90"
      # - "p50=0.50"
    smtpConfig:
      enableSmtpServer: false
      emailingEntity: "OpenMetadata"
      supportUrl: "https://slack.open-metadata.org"
      transportationStrategy: "SMTP_TLS"
      openMetadataUrl: ""
      serverEndpoint: ""
      serverPort: ""
      senderMail: ""
      username: ""
      password:
        secretRef: ""
        secretKey: ""
    secretsManager:
      enabled: true
      # Possible values are noop, aws, aws-ssm, managed-aws, managed-aws-ssm, in-memory
      provider: aws
      additionalParameters:
        enabled: false
        region: "us-east-2"
        accessKeyId:
          secretRef: secret
          secretKey: aws-access-key-secret
        secretAccessKey:
          secretRef: secret
          secretKey: aws-secret-access-key-secret
      # You can create Kubernetes secrets from AWS Credentials with the below command
      # kubectl create secret generic aws-key-secret \
      # --from-literal=aws-access-key-secret=<access_key_id_value> \
      # --from-literal=aws-secret-access-key-secret=<access_key_secret_value>
    web:
      enabled: true
      uriPath: "/api"
      hsts:
        enabled: false
        maxAge: "365 days"
        includeSubDomains: "true"
        preload: "true"
      frameOptions:
        enabled: false
        option: "SAMEORIGIN"
        origin: ""
      contentTypeOptions:
        enabled: false
      xssProtection:
        enabled: false
        onXss: true
        block: true
      csp:
        enabled: false
        policy: "default-src 'self'"
        reportOnlyPolicy: ""
      referrerPolicy:
        enabled: false
        option: "SAME_ORIGIN"
      permissionPolicy:
        enabled: false
        option: ""

networkPolicy:
  # If networkPolicy is true, following values can be set
  # for ingress on port 8585 and 8586
  enabled: true

  # Example Google SSO Auth Config
  # authorizer:
  #   className: "org.openmetadata.service.security.DefaultAuthorizer"
  #   containerRequestFilter: "org.openmetadata.service.security.JwtFilter"
  #   initialAdmins:
  #   - "suresh"
  #   principalDomain: "open-metadata.org"
  # authentication:
  #   provider: "google"
  #   publicKeys:
  #   - "https://www.googleapis.com/oauth2/v3/certs"
  #   authority: "https://accounts.google.com"
  #   clientId: "<client_id>"
  #   callbackUrl: "<callback_url>"

image:
  repository: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/openmetadata-server
  # Overrides the image tag whose default is the chart appVersion.
  tag: "1.3.1"
  pullPolicy: "Always"

sidecars: []
# - name: "busybox"
#   image: "busybox:1.34.1"
#   imagePullPolicy: "Always"
#   command: ["ls"]
#   args: ["-latr", "/usr/share"]
#   env:
#   - name: DEMO
#     value: "DEMO"
#   volumeMounts:
#   - name: extras
#     mountPath: /usr/share/extras
#     readOnly: true

imagePullSecrets: []
nameOverride: ""
fullnameOverride: "openmetadata"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""
automountServiceAccountToken: true
podSecurityContext: {}
  # fsGroup: 2000
securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 100

service:
  type: NodePort
  port: 8585
  adminPort: 8586
  annotations: {}

# Service monitor for Prometheus metrics
serviceMonitor:
  enabled: false
  interval: 30s
  annotations: {}
  labels: {}

ingress:
  enabled: false
  className: "alb"
  annotations: {}
    # kubernetes.io/tls-acme: "true"
  hosts:
    - paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: "openmetadata"
              port:
                number: 8585
        - path: /admin
          pathType: Prefix
          backend:
            service:
              name: "openmetadata"
              port:
                number: 8586
  tls: []
    # - secretName: tls-open-metadata.local
    #   hosts:
    #     - open-metadata.local

extraEnvs: []
# - name: MY_ENVIRONMENT_VAR
#   value: the_value_goes_here

envFrom: []
# - secretRef:
#     name: secret_containing_config

extraVolumes: []
# - name: extras
#   emptyDir: {}

extraVolumeMounts: []
# - name: extras
#   mountPath: /usr/share/extras
#   readOnly: true

# Provision for InitContainers to be running after the `run-db-migration` InitContainer
extraInitContainers: []

# Provision for InitContainers to be running before the `run-db-migration` InitContainer
preMigrateInitContainers: []

resources: {}
# We usually recommend not to specify default resources and to leave this as a conscious
# choice for the user. This also increases chances charts run on environments with little
# resources, such as Minikube. If you do want to specify resources, uncomment the following
# lines, adjust them as necessary, and remove the curly braces after 'resources:'.
# limits:
#   cpu: 1
#   memory: 2048Mi
# requests:
#   cpu: 500m
#   memory: 256Mi

nodeSelector: {}

tolerations: []

affinity: {}

livenessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 5
  httpGet:
    path: /healthcheck
    port: http-admin
readinessProbe:
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 5
  httpGet:
    path: /
    port: http
startupProbe:
  periodSeconds: 60
  failureThreshold: 5
  successThreshold: 1
  httpGet:
    path: /healthcheck
    port: http-admin

podDisruptionBudget:
  enabled: false
  config:
    maxUnavailable: "1"
    minAvailable: "1"

commonLabels: {}
podAnnotations: {}
