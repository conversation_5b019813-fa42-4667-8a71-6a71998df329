"use strict";
class Config {
    // Use a generic method with a type assertion
    static get(key) {
        return this.config[key];
    }
    static getUI(key) {
        return this.config.uiText[key];
    }
}
Config.config = {
    msgPerSecInterval: 10000, // assuming it's in milliseconds
    succesfullStatusMessageDuration: 5000, // assuming it's in milliseconds
    errorStatusMessageDuration: 20000, // assuming it's in milliseconds
    uiText: {
        streamPriceButtonStart: "Start streaming prices",
        streamPriceButtonStop: "Stop streaming prices",
        // Other UI texts can be added here
    },
};
