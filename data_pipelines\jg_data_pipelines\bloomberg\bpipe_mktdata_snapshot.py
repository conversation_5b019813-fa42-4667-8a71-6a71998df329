# SnapshotRequestTemplateExample.py

import datetime as dt
from threading import Condition, Lock
import logging
import time
import pandas as pd
from pprint import pformat

import blpapi
from blpapi import Names

_log = logging.getLogger(__name__)

logger = logging.getLogger()
logging.basicConfig(level=logging.INFO)

SUBSCRIPTION_FIELDS = [
        "LAST_PRICE",
        "ASK",
        "BID",
        "EID",
        "MKTDATA_EVENT_TYPE",
        "MKTDATA_EVENT_SUBTYPE",
        "IS_DELAYED_STREAM",
        "LAST_TRADE_PRICE_TIME_TODAY_RT",
        "BID_UPDATE_STAMP_RT",
        "ASK_UPDATE_STAMP_RT",
        "EVT_TRADE_TIME_RT",
    ]

SUBSCRIPTION_OPTIONS = []

MKT_DATA_SVC = "//blp/mktdata"
API_AUTH_SVC = "//blp/apiauth"

EXCEPTIONS = blpapi.Name("exceptions")
FIELD_ID = blpapi.Name("fieldId")
REASON = blpapi.Name("reason")
CATEGORY = blpapi.Name("category")
DESCRIPTION = blpapi.Name("description")

ServiceName = blpapi.Name("serviceName")
# authorization
AUTHORIZATION_SUCCESS = blpapi.Name("AuthorizationSuccess")
AUTHORIZATION_FAILURE = blpapi.Name("AuthorizationFailure")
AUTHORIZATION_REVOKED = blpapi.Name("AuthorizationRevoked")
TOKEN_SUCCESS = blpapi.Name("TokenGenerationSuccess")
TOKEN_FAILURE = blpapi.Name("TokenGenerationFailure")
TOKEN = blpapi.Name("token")

DEFAULT_QUEUE_SIZE = 10000

class CorrelationInfo:
    def __init__(self, topic):
        self.topic = topic

    def getTopic(self):
        return self.topic
    
    def __str__(self):
        return self.topic

def onMessage(threadId, traceLevel, dateTime, loggerName, message):
    logger.info(
        "%s %s [%s] Thread ID = %s %s"
        % (dateTime, loggerName, traceLevel, threadId, message)
    )

class SnapshotRequest:
    def __init__(self, topics, fields,
                 interval = None, callback = None,
                 template_batch_size = 50):
        self._topics = topics
        self._fields = fields
        self._interval = interval
        self._callback = callback
        self._template_batch_size = template_batch_size

        self._snapshots = {}

        # Lock and conditions are used to synchronize requests and responses,
        # i.e., the next requests are only sent after all the responses
        # of the current requests have been received.
        self._lock = Lock()
        self._responseCount = 0
        self._responseCondition = Condition(self._lock)

        self._templateCount = 0
        self.d_templateBatchingCondition = Condition(self._lock)

        self._running = False

        self._subscriptions = None
        self._subscriptionStrings = {}

        self._data = []

    def _createSubscriptionStrings(self):

        # Use SubscriptionList to help construct the subscription string
        self._subscriptions = blpapi.SubscriptionList()
        for i, userTopic in enumerate(self._topics):
            self._subscriptions.add(userTopic, self._fields, SUBSCRIPTION_OPTIONS)
            self._subscriptionStrings[userTopic] = self._subscriptions.topicStringAt(i)

    def _createTemplates(self, session):
        # NOTE: resources used by a snapshot request template are released
        # only when a 'RequestTemplateTerminated' message is received or when
        # the session is destroyed. In order to release resources when a
        # request template is not needed anymore, the user should call
        # 'Session.cancel' and pass the correlation id used when creating the
        # request template, or call 'RequestTemplate.close'. If 'Session.cancel'
        # is used, all outstanding requests are canceled and the underlying
        # subscription is closed immediately. If the handle is closed with
        # 'RequestTemplate.close', the underlying subscription is closed
        # only when all outstanding requests are served.
        self._createSubscriptionStrings()

        with self._lock:
            self._responseCount = len(self._subscriptionStrings)
            for userTopic, subscriptionString in self._subscriptionStrings.items():
                self.d_templateBatchingCondition.wait_for(
                    lambda: not self._running
                    or self._templateCount < self._template_batch_size
                )

                if not self._running:
                    break

                # Create the template
                _log.debug('Creating snapshot request template for %s', userTopic)

                statusCid = blpapi.CorrelationId(CorrelationInfo(userTopic))
                requestTemplate = session.createSnapshotRequestTemplate(
                    subscriptionString, statusCid
                )
                self._snapshots[statusCid] = requestTemplate

            # Wait until all the request templates have finished, either
            # success or failure.
            self._responseCondition.wait_for(
                lambda: not self._running or self._responseCount == 0
            )


    def _sendRequests(self, session):
        while True:
            with self._lock:
                if not self._running or not self._snapshots:
                    break

                _log.debug('Sending requests using the request templates')

                self._data.clear()
                self._responseCount = len(self._snapshots)
                for cid, template in self._snapshots.items():
                    userTopic = cid.value().topic
                    _log.debug('Sending request for %s', userTopic)
                    session.sendRequestTemplate(
                        template,
                        blpapi.CorrelationId(CorrelationInfo(userTopic)),
                    )

                self._responseCondition.wait_for(
                    lambda: not self._running or self._responseCount == 0
                )

            if self._callback:
                self._callback(self.get_data())

            if self._interval is None:
                break

            _log.debug('Received all the responses, will send next request in %d seconds', self._interval)
            time.sleep(self._interval)

    def run(self):
        """main entry point"""
        # set BLPAPI log level
        blpapi.logging.Logger.registerCallback(
            onMessage, blpapi.logging.Logger.SEVERITY_OFF
        )

        # Fill SessionOptions
        options = blpapi.SessionOptions()
        options.sessionName = "mds_snapshot_session"

        # Server address setup
        options.setServerAddress("Tech1ProdBPipe47537.jainglobal.net", 8194, 0)
        authOptions = blpapi.AuthOptions.createWithApp("JAIN:pmdashboard")
        authCorrelationId = blpapi.CorrelationId("authCorrelation")
        options.setSessionIdentityOptions(authOptions, authCorrelationId)

        logger.info("Session options: %s" % options)

        session = blpapi.Session(options, self._processEvent)
        try:
            if not session.start():
                raise RuntimeError("Failed to start session.")
            else:
                self._running = True
            
            logger.info("Connected successfully")

            if not session.openService(MKT_DATA_SVC):
                logger.error("Failed to open service %s", MKT_DATA_SVC)
                return
            
            if not session.openService(API_AUTH_SVC):
                raise RuntimeError("Failed to open service %s", API_AUTH_SVC)
                
            self._createTemplates(session)

            if self._snapshots:
                self._sendRequests(session)
            else:
                logger.error("Failed to create any request templates")
        finally:
            if self._running:
                session.stop()
            
    def get_data(self):
        return self._data

    def _processSnapshotResponse(self, msg):
        """Process snapshot response event"""
        data = msg.toPy()
        data['timestamp'] = dt.datetime.now(dt.timezone.utc)  # snapshots do not include timeReceived even when enabled in session options
        data['topic'] = msg.correlationId().value().topic
        self._data.append(data)

    def _processEvent(self, event, _):
        """Process session event"""

        eventType = event.eventType()

        for msg in event:
            messageType = msg.messageType()
            logger.debug('Event type: %s, message type: %s', eventType, messageType)
            cid = msg.correlationId()
            myCorrelation = cid.value()
            if messageType == Names.REQUEST_TEMPLATE_AVAILABLE:
                logger.debug('Request template is successfully created for topic %s', myCorrelation)

                with self._lock:
                    # Decrease template count
                    self._templateCount -= 1
                    self.d_templateBatchingCondition.notify_all()

                    # Decrease response count
                    self._responseCount -= 1
                    self._responseCondition.notify_all()
            elif messageType == Names.REQUEST_TEMPLATE_TERMINATED:
                # Will also receive a 'RequestFailure' message preceding
                # 'RequestTemplateTerminated' for every pending request.
                logger.warning('Request template terminated for topic %s:\n%s', myCorrelation, msg.toPy())

                with self._lock:
                    # Remove the template
                    # blpapi example incorrectly tries to delete myCorrelation (cid.value())
                    del self._snapshots[cid]

                    # Decrease template count
                    self._templateCount -= 1
                    self.d_templateBatchingCondition.notify_all()

                    # Decrease response count
                    self._responseCount -= 1
                    self._responseCondition.notify_all()
            elif eventType == blpapi.Event.PARTIAL_RESPONSE:
                logger.debug('Received partial for topic %s', myCorrelation)
                self._processSnapshotResponse(msg)
            elif eventType == blpapi.Event.RESPONSE:
                logger.debug('Received response for topic %s', myCorrelation)
                self._processSnapshotResponse(msg)

                with self._lock:
                    self._responseCount -= 1
                    self._responseCondition.notify_all()
            elif messageType == Names.SESSION_TERMINATED:
                with self._lock:
                    self._running = False
                    self.d_templateBatchingCondition.notify_all()
                    self._responseCondition.notify_all()
            else:
                logger.debug('Message of type %s in event of type %s left unhandled:\n%s',
                           messageType, eventType, pformat(msg.toPy(), compact=True))

def print_data(data):
    """Print the data received from the snapshot request"""
    for item in data:
        print(item)

def process_snapshot_request(securities, fields, batch_size=50):
    snap_request = SnapshotRequest(topics=securities, fields=fields, template_batch_size=batch_size)
    snap_request.run()
    df = pd.DataFrame(snap_request.get_data())
    df.rename(columns={"topic": "bbg_ticker", "timestamp": "api_recvd_timestamp"}, inplace=True)
    df["api_recvd_timestamp"] = df["api_recvd_timestamp"].dt.strftime('%Y-%m-%d %H:%M:%S.%f')
    df.columns = df.columns.str.lower()
    return df

if __name__ == "__main__":
    snap_request = SnapshotRequest(topics=["WSM US Equity"], fields=SUBSCRIPTION_FIELDS, template_batch_size=50, callback=print_data, interval=10)
    snap_request.run()    