import pandas
import json
import boto3
import io

from metadata.generated.schema.api.data.createCustomProperty import (
    CreateCustomPropertyRequest,
)
from metadata.generated.schema.entity.data.databaseSchema import DatabaseSchema
from metadata.generated.schema.entity.data.table import Table

from metadata.generated.schema.entity.services.connections.metadata.openMetadataConnection import (
    OpenMetadataConnection,
)

from metadata.generated.schema.security.client.openMetadataJWTClientConfig import (
    OpenMetadataJWTClientConfig,
)
from metadata.generated.schema.type.basic import EntityExtension
from metadata.ingestion.models.custom_properties import (
    CustomPropertyDataTypes,
    OMetaCustomProperties,
)
from metadata.ingestion.ometa.ometa_api import OpenMetadata


class OMetaCustomAttribute():
    server_config = OpenMetadataConnection(
        hostPort="http://internal-k8s-default-openmeta-e85dbd1e9b-316426303.us-east-2.elb.amazonaws.com/api",
        authProvider="openmetadata",
        securityConfig=OpenMetadataJWTClientConfig(
            jwtToken="eyJraWQiOiJHYjM4OWEtOWY3Ni1nZGpzLWE5MmotMDI0MmJrOTQzNTYiLCJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************.fi8lEVoARjhyihFZkFdXdMtfzE1FDsD80zRDuAfez3ZybdLsdbycQhKyuoCJueyGnESFJTzt4UxF-_MzPLcWfJchtsStJ8ju8v99GpkcLXejdo9MUNnVTmF5yDrdNi8ik4vesM3wKOtEO3US9Sv7Sh1AiehFpOkmzOlXDDZ6Qba-2Zl5SRsj_y-6Smg_0LFQW5kye0i1uuiz6mXmYhCNW8CEs3-fJfUSwyrBNFhzpCaJqelLmJpLXEjr9NqBqdk1ueZJ0DJWoM010MxdzJgyMZWtCy8MJOQZzxeXf2o9CPi5G38vqRm61TAi-Zhgw_fDpZGbihsgIsjVvOLYwWqIMw"
        ),
    )
    metadata = OpenMetadata(server_config)

    assert metadata.health_check()

    def add_custom_property(self, sheetName: str, entity):
        # Read excel document
        aws_id = '********************'
        aws_secret = 'TfjYbi8MxrAscB5NgqlzeXYFxFQagTTFFCjqtLHu'
        bucket_name = 'jg-s3-datacatalogfeed-uat'
        object_key = 'ETLFeeds-Catalog.xlsx'

        s3 = boto3.client('s3', aws_access_key_id=aws_id, aws_secret_access_key=aws_secret)
        obj = s3.get_object(Bucket=bucket_name, Key=object_key)
        data = obj['Body'].read()
        df = pandas.read_excel(io.BytesIO(data), sheet_name='Final Sheet')
        df.fillna("")

        # Convert excel to string (define orientation of document in this case from up to down)
        jsonString = df.to_json(orient='records')
        jsonDict = json.loads(jsonString)
        # print("\njsonString\n",jsonString)
        
        # converting all the json values to string
        for dicts in jsonDict:
            for keys in dicts:
                dicts[keys]=str(dicts[keys])
        # print("\n\nDICT\n",jsonDict)
        
        # creating and updating custom properties in openmetadata     
        for item in jsonDict:
            print("ITEM:::::::::::::::")
            print(item)
            print("DATASETNAME:::::::::::::::::::")
            print(item['Dataset_Name'])
            for customProperty in item.keys():
                
                # Create the property for the Entity
                ometa_custom_property_request = OMetaCustomProperties(
                    entity_type=entity,
                    createCustomPropertyRequest=CreateCustomPropertyRequest(
                        name=customProperty,
                        description=customProperty,
                        propertyType=self.metadata.get_property_type_ref(
                            CustomPropertyDataTypes.STRING
                        ),
                    ),
                )
                self.metadata.create_or_update_custom_property(
                    ometa_custom_property=ometa_custom_property_request
                )
                if customProperty == "Table":
                    table = item[customProperty]
            
            extensions = item
            if entity == Table:
                item = self.metadata.get_by_name(entity=entity, fqn="trino.iceberg."+item['Dataset_Name']+"."+table.lower(), fields=["*"])
            else:
                item = self.metadata.get_by_name(entity=entity, fqn="trino.iceberg."+item['Dataset_Name'])
            updated_item = item.copy(deep=True)
            updated_item.extension = EntityExtension(__root__=extensions)
            self.metadata.patch(entity=entity, source=item, destination=updated_item)

if __name__=="__main__":
    create = OMetaCustomAttribute()
    
    create.add_custom_property("Schema", DatabaseSchema)
    # create.add_custom_property("Table", Table, "snp_data")