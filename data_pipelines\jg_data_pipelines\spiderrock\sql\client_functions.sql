CREATE OR REPLACE FUNCTION eqvol.get_opt_eq_quotes_timestamps()
 RETURNS TABLE(snap_timestamp timestamp without time zone)
 LANGUAGE plpgsql
AS $function$
begin
	return query
	select soqet.snap_timestamp 
	from eqvol.sr_option_quote_exp_timestamps soqet  
	where snap_date >= current_date - interval '2 days'
	order by soqet.snap_timestamp desc;
end;
$function$
;

-- Permissions

ALTER FUNCTION eqvol.get_opt_eq_quotes_timestamps() OWNER TO external_write;
GRANT ALL ON FUNCTION eqvol.get_opt_eq_quotes_timestamps() TO external_write;
GRANT ALL ON FUNCTION eqvol.get_opt_eq_quotes_timestamps() TO fe_risk;
GRANT ALL ON FUNCTION eqvol.get_opt_eq_quotes_timestamps() TO fe_risk_ro;

CREATE OR REPLACE FUNCTION eqvol.get_opt_quotes_for_security(sr_ticker character varying, quote_timestamp timestamp without time zone DEFAULT NULL::timestamp without time zone, lookback_tolerance_seconds integer DEFAULT 300)
 RETURNS TABLE(ticker character varying, call_put character varying, strike_price bigint, expiration_date date, update_type character varying, bid_price real, bid_size integer, cum_bid_size integer, bid_time integer, bid_mkt_type character varying, ask_price real, ask_size integer, cum_ask_size integer, ask_time integer, ask_mkt_type character varying, sr_srctimestamp timestamp without time zone, sr_nettimestamp timestamp without time zone, jg_api_req_timestamp timestamp without time zone, jg_api_recvd_timestamp timestamp without time zone)
 LANGUAGE plpgsql
AS $function$
DECLARE
	quote_timestamp_to_use timestamp;
BEGIN
	
	IF quote_timestamp IS NULL THEN
		SELECT MAX(snap_timestamp) INTO quote_timestamp_to_use
		FROM eqvol.sr_option_quote_exp_timestamps;
	ELSE
		SELECT MAX(snap_timestamp) INTO quote_timestamp_to_use
		FROM eqvol.sr_option_quote_exp_timestamps
		WHERE snap_timestamp <= quote_timestamp
		AND snap_timestamp >= quote_timestamp - (lookback_tolerance_seconds * INTERVAL '1 second');
	END IF;

	-- Validate timestamp was resolved
	IF quote_timestamp_to_use IS NULL THEN
		RAISE EXCEPTION 'Could not resolve a valid quote timestamp using input timestamp %, with lookback tolerance % seconds.', quote_timestamp, lookback_tolerance_seconds
			USING ERRCODE = '22000';
	END IF;

    RETURN query
    select soql.ticker as ticker,
	soql.call_put as call_put,
	soql.strike_price,
	soql.expiration_date,
	soql.update_type,
	soql.bid_price,
	soql.bid_size,
	soql.cum_bid_size,
	soql.bid_time,
	soql.bid_mkt_type,
	soql.ask_price,
	soql.ask_size,
	soql.cum_ask_size,
	soql.ask_time,
	soql.ask_mkt_type,
	soql.sr_srctimestamp,
	soql.sr_nettimestamp,
	soql.jg_api_req_timestamp,
	soql.jg_api_recvd_timestamp
	from eqvol.sr_option_quotes_exp soql
	where soql.jg_api_req_timestamp = quote_timestamp_to_use and 
	soql.ticker = sr_ticker;
    
END;
$function$
;

GRANT EXECUTE ON FUNCTION eqvol.get_opt_quotes_for_security(varchar, timestamp, int4) TO fe_risk;
GRANT EXECUTE ON FUNCTION eqvol.get_opt_quotes_for_security(varchar, timestamp, int4) TO fe_risk_ro;



-- DROP FUNCTION eqvol.get_opt_quotes_for_opt_attrs(jsonb, timestamp, int4);

CREATE OR REPLACE FUNCTION eqvol.get_opt_quotes_for_opt_attrs(opt_attrs_json jsonb, quote_timestamp timestamp without time zone DEFAULT NULL::timestamp without time zone, lookback_tolerance_seconds integer DEFAULT 300)
 RETURNS TABLE(ticker character varying, call_put character varying, strike_price bigint, expiration_date date, update_type character varying, bid_price real, bid_size integer, cum_bid_size integer, bid_time integer, bid_mkt_type character varying, ask_price real, ask_size integer, cum_ask_size integer, ask_time integer, ask_mkt_type character varying, sr_srctimestamp timestamp without time zone, sr_nettimestamp timestamp without time zone, jg_api_req_timestamp timestamp without time zone, jg_api_recvd_timestamp timestamp without time zone)
 LANGUAGE plpgsql
AS $function$
DECLARE
	quote_timestamp_to_use timestamp;
BEGIN
	
	IF quote_timestamp IS NULL THEN
		SELECT MAX(snap_timestamp) INTO quote_timestamp_to_use
		FROM eqvol.sr_option_quote_exp_timestamps;
	ELSE
		SELECT MAX(snap_timestamp) INTO quote_timestamp_to_use
		FROM eqvol.sr_option_quote_exp_timestamps
		WHERE snap_timestamp <= quote_timestamp
		AND snap_timestamp >= quote_timestamp - (lookback_tolerance_seconds * INTERVAL '1 second');
	END IF;

	-- Validate timestamp was resolved
	IF quote_timestamp_to_use IS NULL THEN
		RAISE EXCEPTION 'Could not resolve a valid quote timestamp using input timestamp %, with lookback tolerance % seconds.', quote_timestamp, lookback_tolerance_seconds
			USING ERRCODE = '22000';
	END IF;

    RETURN query
	WITH input_options AS (
		SELECT 
			opt_attrs.ticker,
			opt_attrs.strike_price,
			opt_attrs.expiration_date,
			opt_attrs.call_put
		FROM jsonb_to_recordset(opt_attrs_json) AS opt_attrs(
			ticker varchar,
			strike_price int8,
			expiration_date date,
			call_put varchar
	))
    select COALESCE(inp_opts.ticker, soql.ticker) as ticker,
	COALESCE(inp_opts.call_put, soql.call_put) as call_put,
	COALESCE(inp_opts.strike_price, soql.strike_price) as strike_price,
	COALESCE(inp_opts.expiration_date, 	soql.expiration_date) as expiration_date,
	soql.update_type,
	soql.bid_price,
	soql.bid_size,
	soql.cum_bid_size,
	soql.bid_time,
	soql.bid_mkt_type,
	soql.ask_price,
	soql.ask_size,
	soql.cum_ask_size,
	soql.ask_time,
	soql.ask_mkt_type,
	soql.sr_srctimestamp,
	soql.sr_nettimestamp,
	soql.jg_api_req_timestamp,
	soql.jg_api_recvd_timestamp
	from input_options inp_opts left outer join
	eqvol.sr_option_quotes_exp soql on (inp_opts.ticker = soql.ticker and inp_opts.expiration_date = soql.expiration_date and inp_opts.strike_price = soql.strike_price and inp_opts.call_put = soql.call_put and soql.jg_api_req_timestamp = quote_timestamp_to_use) 
	where COALESCE(soql.jg_api_req_timestamp, quote_timestamp_to_use) = quote_timestamp_to_use;
    
END;
$function$
;



GRANT EXECUTE ON FUNCTION eqvol.get_opt_quotes_for_opt_attrs(jsonb, timestamp, int4) TO fe_risk;
GRANT EXECUTE ON FUNCTION eqvol.get_opt_quotes_for_opt_attrs(jsonb, timestamp, int4) TO fe_risk_ro;

GRANT EXECUTE ON FUNCTION eqvol.get_opt_eq_quotes_timestamps() TO eqvol_read;
GRANT EXECUTE ON FUNCTION eqvol.get_opt_eq_quotes_timestamps() TO mds_read;
GRANT EXECUTE ON FUNCTION eqvol.get_opt_eq_quotes_timestamps() TO tech_fe_risk_ro;


GRANT EXECUTE ON FUNCTION eqvol.get_opt_quotes_for_security(varchar, timestamp, int4) TO tech_fe_risk_ro;
GRANT EXECUTE ON FUNCTION eqvol.get_opt_quotes_for_security(varchar, timestamp, int4) TO mds_read;
GRANT EXECUTE ON FUNCTION eqvol.get_opt_quotes_for_security(varchar, timestamp, int4) TO eqvol_read;
