import os
import logging
import time
import pysftp


logging.getLogger().setLevel(logging.INFO)

def _conn(host, username, password=None, private_key=None, private_key_pass=None, port=22, disable_hostkey=False):
    if disable_hostkey:
        p = pysftp.CnOpts()
        p.hostkeys = None
    else:
        p = None
    return pysftp.Connection(host,username=username,password=password,private_key=private_key, private_key_pass=private_key_pass,port=port,cnopts=p)

def _file(conn, remotefile, localfile):
  os.makedirs(os.path.dirname(localfile),exist_ok=True)
  try:
    conn.get(remotefile,localfile,preserve_mtime=True)
  except Exception as e:
    logging.exception(e)
    raise ValueError(f"File missing: {remotefile}")

class SftpManager:
  def __init__(self,
               host: str,
               username: str,
               password: str = None,
               private_key: str = None,
               private_key_pass: str = None,
               port: int = 22,
               disable_hostkey: bool = False):
    self.host = host
    self.username = username
    self.password = password
    self.port = port
    self.disable_hostkey = disable_hostkey
    self.private_key = private_key
    self.private_key_pass = private_key_pass
    self.conn = None
    self.connect()

  def connect(self):
    logging.info(f"Connecting to: {self.username}@{self.host}")
    self.conn = _conn(
      self.host,
      username=self.username,
      password=self.password,
      private_key=self.private_key,
      private_key_pass=self.private_key_pass,
      port=int(self.port),
      disable_hostkey=bool(self.disable_hostkey))    

  def put(self, request, target='.'):
    logging.info(f"Submitting request:")
    logging.info(request)
    filename = request.split('/')[-1]
    self.conn.put(request, remotepath=f'{target}/{filename}')

  def check_response(self, lookup):
    logging.info(f'Looking for file {lookup}')
    if self.conn.exists(lookup):
      logging.info(f'File {lookup} found')
      return lookup
    return None

  def long_poll(self, lookup, timeout_seconds=300, check_interval_seconds=60, cold_start=60):
    logging.info(f"Waiting {cold_start} seconds before starting lookups")
    time.sleep(cold_start)
    n = 0  
    while n < timeout_seconds:
      response = self.check_response(lookup)
      if response:
        return response
      else:
        time.sleep(check_interval_seconds)
        n += check_interval_seconds
    logging.info(f"Long poll timed out after {timeout_seconds} seconds")

  def fetch(self, filename, source, target):
    if not filename:
      raise ValueError('File response was never received')
    folder = source if source.endswith('/') else f'{source}/'
    logging.info(f'Fetching file {folder}{filename}')
    _file(self.conn, f'{folder}{filename}', f'{target}/{filename}')
    return f'{target}/{filename}'

  def get_directory_structure(self, path, with_attributes=False):
    retries=3
    for attempt in range(retries):
      try:
        if with_attributes:
          return self.conn.listdir_attr(path)
        else:
          return self.conn.listdir(path)
      except OSError as e:
        if attempt < retries - 1:
          time.sleep(5)
          logging.info("ERROR: RETRYING")
        else:
          logging.error(f"Error accessing {path}: {e}")
          raise e  

  def close(self):
    self.conn.close()
