raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/eex/spot/1.0/"  ## Location of Raw Files
  s3_prefix: "eex/spot" ## Internal S3path to files

  structure: '[
    "austria/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_austria_$DATE$.csv",
    "denmark 2/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_denmark 2_$DATE$.csv",
    "germany/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_germany_luxembourg_$DATE$.csv",
    "norway 1/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_norway 1_$DATE$.csv",
    "norway 4/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_norway 4_$DATE$.csv",
    "sweden 1/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_sweden 1_$DATE$.csv",
    "sweden 4/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_sweden 4_$DATE$.csv",
    "belgium/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_belgium_$DATE$.csv",
    "finland/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_finland_$DATE$.csv",
    "great-britain/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_great-britain_$DATE$.csv",
    "great-britain/Day-Ahead Auction/Half-hourly/Current/Prices_Volumes/hh_auction_spot_volumes_great-britain_$DATE$.csv",
    "norway 2/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_norway 2_$DATE$.csv",
    "norway 5/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_norway 5_$DATE$.csv",
    "sweden 2/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_sweden 2_$DATE$.csv",
    "switzerland/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_switzerland_$DATE$.csv",
    "denmark 1/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_denmark 1_$DATE$.csv",
    "france/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_france_$DATE$.csv",
    "netherlands/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_netherlands_$DATE$.csv",
    "norway 3/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_norway 3_$DATE$.csv",
    "poland/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_poland_$DATE$.csv",
    "sweden 3/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_volumes_sweden 3_$DATE$.csv",
    "austria/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_austria_$DATE$.csv",
    "denmark 2/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_denmark 2_$DATE$.csv",
    "germany/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_germany_luxembourg_$DATE$.csv",
    "norway 1/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_norway 1_$DATE$.csv",
    "norway 4/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_norway 4_$DATE$.csv",
    "sweden 1/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_sweden 1_$DATE$.csv",
    "sweden 4/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_sweden 4_$DATE$.csv",
    "belgium/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_belgium_$DATE$.csv",
    "finland/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_finland_$DATE$.csv",
    "great-britain/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_great-britain_$DATE$.csv",
    "great-britain/Day-Ahead Auction/Half-hourly/Current/Prices_Volumes/hh_auction_spot_prices_great-britain_$DATE$.csv",
    "norway 2/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_norway 2_$DATE$.csv",
    "norway 5/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_norway 5_$DATE$.csv",
    "sweden 2/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_sweden 2_$DATE$.csv",
    "switzerland/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_switzerland_$DATE$.csv",
    "denmark 1/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_denmark 1_$DATE$.csv",
    "france/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_france_$DATE$.csv",
    "netherlands/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_netherlands_$DATE$.csv",
    "norway 3/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_norway 3_$DATE$.csv",
    "poland/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_poland_$DATE$.csv",
    "sweden 3/Day-Ahead Auction/Hourly/Current/Prices_Volumes/auction_spot_prices_sweden 3_$DATE$.csv"
  ]'
  
snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "EEX_SPOT"

  table_map:
    VOLUMES_RAW:
      pattern: "auction_spot_volumes_.*_$DATE$.csv" ## Need to be a regex format
      col_num: 27
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/spot/" ##<stage name>/<stage path>
      file_format: "FF_EEX_SPOT"

    PRICES_RAW:
      pattern: "auction_spot_prices_.*_$DATE$.csv" ## Need to be a regex format
      col_num: 45
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/spot/" ##<stage name>/<stage path>
      file_format: "FF_EEX_SPOT"

    HH_VOLUMES_RAW:
      pattern: "hh_auction_spot_volumes_.*_$DATE$.csv" ## Need to be a regex format
      col_num: 52
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/spot/" ##<stage name>/<stage path>
      file_format: "FF_EEX_SPOT"

    HH_PRICES_RAW:
      pattern: "hh_auction_spot_prices_.*_$DATE$.csv" ## Need to be a regex format
      col_num: 56
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/spot/" ##<stage name>/<stage path>
      file_format: "FF_EEX_SPOT"