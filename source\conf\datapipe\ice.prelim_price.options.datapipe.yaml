raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/ice/prelim_price/ICE_FLASH"  ## Location of Compress Raw Files
  s3_bucket: "jg-data-dp-vendor-data" ## S3 with Snowflake Acess
  s3_prefix: "ice/prelim_price/futures" ## Internal S3path to files
  include_prefix: false
  structure: '{
      "**/icecleared_gasoptions_$DATE$.zip": [
          "*.dat" 
      ]
  }'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "ICE_PRELIM_PRICE"

  table_map:
    OPTIONS_RAW:
      pattern: ".*icecleared_gasoptions_.*dat" ## Need to me a regex format
      col_num: 13
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/prelim_price/options/" ##<stage name>/<stage path>
      file_format: "FF_OPTIONS"