import os
import pandas as pd
import numpy as np
from ast import literal_eval
from data_analyzer.data_analyzer import DataAnalyzer

class AmazonReviewDataAnalyzer(DataAnalyzer):
    def check_data_availability(self):
        if not os.path.exists(self.input_datapath):
            print("\033[91mData file not found. Please download the Amazon Fine Food Reviews dataset from Kaggle.\033[0m")
            print("\033[91mDownload link: https://www.kaggle.com/datasets/snap/amazon-fine-food-reviews\033[0m")
            print("\033[91mOnce downloaded, please save it in './data/Reviews.csv' and restart the application.\033[0m")
            return False
        return True

    def load_or_create_embeddings(self, top_n=1000):
        if not self.check_data_availability():
            return

        if not os.path.exists(self.output_datapath + ".csv"):
            print("Creating embeddings...")
            df = pd.read_csv(self.input_datapath, index_col=0)
            df = df[["Time", "ProductId", "UserId", "Score", "Summary", "Text"]]
            df = df.dropna()
            df["combined"] = "Title: " + df.Summary.str.strip() + "; Content: " + df.Text.str.strip()
            df = df.sort_values("Time").tail(top_n * 2)
            df.drop("Time", axis=1, inplace=True)
            df["embedding"] = df.combined.apply(lambda x: self.get_embedding(x))
            df.to_csv(self.output_datapath + ".csv")
        else:
            print("Loading embeddings...")
            df = pd.read_csv(self.output_datapath + ".csv", index_col=0)
            df['embedding'] = df['embedding'].apply(lambda x: np.array(literal_eval(x)))
        self.df = df
        return df