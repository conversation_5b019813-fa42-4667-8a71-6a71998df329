import os
import re
import sys
import json
import uuid
import asyncio
import aiohttp
import shutil
import logging
import pandas as pd
import numpy as np
import polars as pl
from datetime import datetime
from pathlib import Path
from abc import ABC, abstractmethod
import kpler.kpler_config as kpler_config
from utils.date_utils import get_current_hour, resolve_relative_date, get_now

from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type
from utils.snowflake.bulk_loader import SnowflakeBulkLoader
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from utils.snowflake.adaptor import SnowflakeAdaptor

import time
import functools

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def log_time(func):
    """Decorator to log time taken by a function."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"{func.__qualname__} took {end_time - start_time:.4f} secs")
        return result
    return wrapper


@retry(
    stop=stop_after_attempt(kpler_config.API_MAX_RETRIES),
    wait=wait_fixed(10),
    retry=retry_if_exception_type(Exception),
)
async def fetch_url(session, url, headers, params, semaphore):
    """Fetch data from a given URL asynchronously with rate limiting, timeout, and retries."""
    async with semaphore:
        try:
            async with session.get(url, headers=headers, params=params, timeout=kpler_config.API_REQUEST_TIMEOUT) as response:
                response.raise_for_status()
                return await response.json()
        except Exception as e:
            # logger.error(f"Unexpected error for {url}: {e}, {params}")
            logger.warning(f"Unexpected error for {url}: {e}, {params}")
            raise


async def fetch_all(url, params, semaphore):
    """Fetch multiple URLs in parallel with rate limiting and retries."""
    headers = {'Authorization': "jain-global-dev:47fd296b-8930-42ff-a6a3-7935a28c99e6"}
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_url(session, url, headers, param, semaphore) for param in params]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        return results


def _cleaned_numeric_list(lst):
    return [np.nan if x == "null" else x for x in lst]

class KplerProcessor(ABC):

    def __init__(self, kpler_data_type: kpler_config.KplerDataType, is_historical_backfill:bool = False, **kwargs):   
        """
        Initialize the KplerProcessor with the specified Kpler data type.

        Args:
            kpler_data_type (KplerDataType): The type of Kpler data to process.
        """
        self.kpler_data_type = kpler_data_type
        self.api_url = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value]['api_url']
        self.countries = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('countries', [])
        self.forecast_models = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('forecast_models', [])
        self.demand_kind = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('demand_kind', [])
        self.area_types = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('area_types', [])
        self.market_types = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('market_types', [])

        if not is_historical_backfill:
            as_of_expr = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('as_of', None)
            if as_of_expr:
                if as_of_expr == 'current_hour':
                    self.as_of = get_current_hour('UTC')
                else:
                    raise ValueError(f"Unsupported as_of value: {as_of_expr} for {self.kpler_data_type.value}")
            else:
                self.as_of = None

            run_date_expr = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('run_date', None)
            if run_date_expr:
                self.run_date = resolve_relative_date(run_date_expr, tz_str='UTC')
            else:
                self.run_date = None
            
            start_date_expr = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('start', None)
            if start_date_expr:
                self.start_date = resolve_relative_date(start_date_expr, tz_str='UTC')
            else:
                self.start_date = None

            end_date_expr = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('end', None)
            if end_date_expr:
                self.end_date = resolve_relative_date(end_date_expr, tz_str='UTC')
            else:
                self.end_date = None
        else:
            if "hist_as_of" in kwargs:
                self.as_of = datetime.strptime(kwargs["hist_as_of"], "%Y-%m-%d %H:%M:%S")
                self.start_date = resolve_relative_date("T+0D", reference_date=self.as_of, tz_str='UTC')
                self.end_date = resolve_relative_date("T+2Y", reference_date=self.as_of, tz_str='UTC')
                self.run_date = None
                logger.info(f"As Of date: {self.as_of}, Start date: {self.start_date}, End date: {self.end_date}")
            elif "hist_run_date" in kwargs:
                self.run_date = datetime.strptime(kwargs["hist_run_date"], "%Y-%m-%d")
                self.as_of = None
                logger.info(f"Run date: {self.run_date}")
            elif "hist_start_date" in kwargs and "hist_end_date" in kwargs:
                self.start_date = datetime.strptime(kwargs["hist_start_date"], "%Y-%m-%d")
                self.end_date = datetime.strptime(kwargs["hist_end_date"], "%Y-%m-%d")
                self.as_of = None
                self.run_date = None
                logger.info(f"Start date: {self.start_date}, End date: {self.end_date}")
            else:
                raise ValueError("For historical backfill, please provide either hist_as_of or hist_run_date or hist_start_date and hist_end_date.")
        
        self.api_req_concurrency = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value].get('concurrent_api_requests', 10)
        self.now_str = get_now(tz_str='UTC').strftime("%Y%m%d%H%M%S")

    @abstractmethod
    def _build_requests(self):
        """
        Build the requests to fetch Kpler data.

        Returns:
            list: A list of requests to be sent to the Kpler API.
        """
        pass
    
    @log_time
    def _download_json_from_api(self, requests=[]):
        if not requests:
            raise ValueError("No requests provided to download JSON data.")
        url = f"{kpler_config.KPLER_BASE_URL}{self.api_url}"
        semaphore = asyncio.Semaphore(self.api_req_concurrency)
        results = asyncio.run(fetch_all(url, requests, semaphore))

        return results
    
    @abstractmethod
    def _get_json_file_name(self, param):
        """
        Generate the JSON file name based on the request parameters.

        Args:
            param (dict): The request parameters.

        Returns:
            str: The generated JSON file name.
        """
        pass
    
    @log_time
    def _save_json_to_file(self, json_data, requests):
        if self.as_of:
            raw_json_path = os.path.join(kpler_config.BASE_DATA_DIR, self.kpler_data_type.value, "raw_json", self.as_of.strftime("%Y%m%d%H%M%S"))
        elif self.run_date:
            raw_json_path = os.path.join(kpler_config.BASE_DATA_DIR, self.kpler_data_type.value, "raw_json", self.run_date.strftime("%Y%m%d"))
        else:
            raw_json_path = os.path.join(kpler_config.BASE_DATA_DIR, self.kpler_data_type.value, "raw_json", self.now_str)
        
        if os.path.exists(raw_json_path):
            shutil.rmtree(raw_json_path)
        
        os.makedirs(raw_json_path)

        for res, param in zip(json_data, requests):
            if isinstance(res, Exception):
                logger.error(f"Error fetching data: {res}, {param}")
                # country = requests.get("country", "unknown")
                # fuel_type = requests.get("fuel_type", "unknown")
                # logger.error(f"{country}, {fuel_type}, {res}")
            else:
                json_file_name = self._get_json_file_name(param)
                json_full_path = os.path.join(raw_json_path, json_file_name)
                with open(json_full_path, 'w') as json_file:
                    json.dump(res, json_file, default=str)
        
        return str(raw_json_path)        

    @abstractmethod
    def _examine_json(self, data: dict):
        """
        Examine the JSON response for expected keys and data types.

        Returns:
            tuple: A tuple containing lists of missing keys, extra keys, mismatched data types, and a flag indicating if no data was found.
        """
        pass

    @abstractmethod
    def _flatten_json(self, file_path):
        """
        Flatten the JSON response into a DataFrame.

        Returns:
            DataFrame: A Polars DataFrame containing the flattened data.
        """
        pass

    def _process_json_folder(self, json_path: str, country: str = None, fueltype: str = None):
        df_list = []
        parquet_full_file_path = None

        if country and fueltype:
            pattern_to_match = f"{country}_{fueltype}.json"
        else:
            pattern_to_match = "*.json"

        for json_path in Path(json_path).rglob(pattern_to_match):
            logger.info(f"Processing JSON file: {json_path}")
            flattened_json = self._flatten_json(json_path)

            if flattened_json is not None:
                logger.debug(f"Flattened JSON Data for {json_path}")
                df_list.append(flattened_json)
            else:
                logger.warning(f"No data found in {json_path}")
            
        if df_list:
            parquet_file_path = os.path.join(kpler_config.BASE_DATA_DIR, self.kpler_data_type.value, "processed")
            
            if self.as_of:
                as_of_date_str = self.as_of.strftime("%Y%m%d%H%M%S")
                parquet_full_file_path = os.path.join(parquet_file_path, f"{as_of_date_str}.parquet")
            elif self.run_date:
                run_date_str = self.run_date.strftime("%Y%m%d")
                parquet_full_file_path = os.path.join(parquet_file_path, f"{run_date_str}.parquet")
            else:
                parquet_full_file_path = os.path.join(parquet_file_path, f"{self.now_str}.parquet")

            if country and fueltype:
                parquet_full_file_path = parquet_full_file_path.replace(".parquet", f"_{country}_{fueltype}.parquet")

            if not os.path.exists(parquet_file_path):
                os.makedirs(parquet_file_path)

            if os.path.exists(parquet_full_file_path):
                os.remove(parquet_full_file_path)
            
            data_result = pl.concat(df_list)
            data_result.write_parquet(parquet_full_file_path)
        
        return parquet_full_file_path
        
    @log_time
    def _generate_parquet_files(self, json_path: str):
        all_parquet_files = []

        parquet_file_path = self._process_json_folder(json_path)
        
        if parquet_file_path:
            all_parquet_files.append(parquet_file_path)

        return all_parquet_files
    
    @log_time
    def _load_parquet_files_to_sf(self, parquet_files: list):

        if not parquet_files:
            logger.warning(f"No parquet files to load into Snowflake for {self.kpler_data_type.value}")
            return

        if self.as_of:
            delete_statement = f"DELETE FROM {kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value]['sf_table_name']} WHERE AS_OF = '{self.as_of.strftime('%Y-%m-%d %H:%M:%S')}'"
        elif self.run_date:
            delete_statement = f"DELETE FROM {kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value]['sf_table_name']} WHERE RUNDATE = '{self.run_date.strftime('%Y-%m-%d')}'"
        elif self.start_date and self.end_date:
            start_date_lbound = datetime(self.start_date.year, self.start_date.month, self.start_date.day, 0, 0, 0)
            end_date_ubound = datetime(self.end_date.year, self.end_date.month, self.end_date.day, 23, 0, 0)
            delete_statement = f"DELETE FROM {kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value]['sf_table_name']} WHERE TIMESTAMP BETWEEN '{start_date_lbound.strftime('%Y-%m-%d %H:%M:%S')}' AND '{end_date_ubound.strftime('%Y-%m-%d %H:%M:%S')}'"
        else:
            raise ValueError("Unhandled scenario when saving the parquet file. Please check the configuration.")

        logger.info(f"Delete statement: {delete_statement}")

        sf_adaptor = SnowflakeAdaptor(
            database="KPLER", 
            warehouse="TEST", 
            role="FR_DATA_PLATFORM")

        sf_adaptor.execute_query("ONGOING", delete_statement, "FR_DATA_PLATFORM")
        sf_adaptor.close()

        sf_adaptor = SnowflakeBulkLoader(
            database="KPLER", 
            schema="ONGOING",
            warehouse="TEST", 
            role="FR_DATA_PLATFORM")

        sf_stage_name = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value]["sf_stage_name"]
        sf_table_name = kpler_config.DICT_KPLER_PARAMS[self.kpler_data_type.value]["sf_table_name"]

        stage_path = f"{uuid.uuid4().hex}"

        for parquet_file in parquet_files:
            logger.info(f"Putting parquet file: {parquet_file} to Snowflake stage {sf_stage_name}\{stage_path}")
            sf_adaptor.put_file(parquet_file, sf_stage_name, stage_path=stage_path)
        
        sf_adaptor.load_generic(
                    sf_table_name, 
                    sf_stage_name,
                    stage_path,
                    "(TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE'"
                )
        sf_adaptor.cleanup_stg(sf_stage_name, stage_path)
        sf_adaptor.cleanup()

    @log_time
    def process_kpler_data(self):
        api_requests = self._build_requests()
        raw_api_response = self._download_json_from_api(api_requests)
        raw_json_path = self._save_json_to_file(raw_api_response, api_requests)
        parquet_files_path = self._generate_parquet_files(raw_json_path)
        self._load_parquet_files_to_sf(parquet_files_path)
        logger.info(f"Processed Kpler data for type: {self.kpler_data_type.value}")


class KplerActualLoadProcessor(KplerProcessor):
    """
    Processor for Kpler actual load data.
    """

    def __init__(self, is_historical_backfill: bool = False, **kwargs):
        super().__init__(kpler_config.KplerDataType.ActualLoad, is_historical_backfill, **kwargs)

    @log_time
    def _build_requests(self):
        params_list = [{"start": self.start_date.strftime("%Y-%m-%d"), "end": self.end_date.strftime("%Y-%m-%d"), "granularity": kpler_config.REQUEST_GRANULARITY, "timezone": kpler_config.REQUEST_TIMEZONE, "country": country, "provider": "elexon" if country.upper() in ["UK"] else "entsoe"} for country in self.countries]
        return params_list
    
    def _examine_json(self, data: dict):
        json_keys = data.keys()

        expected_elements = {
            "provider": str,
            "timezone": str,
            "data": dict,
            "index": list,
        }

        missing_keys = list(set(expected_elements.keys()) - set(json_keys))
        extra_keys = list(set(json_keys) - set(expected_elements.keys()))

        mismatched_data_types = []

        if "index" in json_keys:
            timestamps = data["index"]
            if type(timestamps) == list and not timestamps:
                return [], [], [], True

        for key, expected_type in expected_elements.items():
            if key in json_keys:
                if type(data[key]) != expected_type:
                    mismatched_data_types.append(key)
        
        return missing_keys, extra_keys, mismatched_data_types, False

    def _flatten_json(self, file_path):
        with open(file_path, 'r') as f:
            full_dict = json.load(f)

        full_df = []

        (missing_keys, extra_keys, mismatched_types, no_data) = self._examine_json(full_dict)
        if len(extra_keys) > 0:
            logger.warning(f"Unexpected file format: {file_path}. Extra keys identified: {extra_keys}")
        
        if len(missing_keys) > 0 or len(mismatched_types) > 0:
            logger.warning(f"Unexpected file format: {file_path}. Missing Keys: {missing_keys}, Mismatched Types: {mismatched_types}")
            return None
        elif no_data:
            logger.warning(f"No data:{file_path}")
            return None
        else:
            logger.debug(f"Expected format: {file_path}")
        
        provider = full_dict['provider']
        timezone = full_dict['timezone']
        country = os.path.basename(file_path).replace(".json", "")

        timestamp = full_dict["index"]

        for sub_country, data_val in full_dict["data"].items():
            if len(data_val) > 0:
                assert len(data_val) == len(timestamp), f"Length of index dates and production data are not equal for {sub_country} in {file_path}"
                df = pl.DataFrame({
                    "timestamp": timestamp,
                    "demand": data_val
                })
                df = df.with_columns(
                    timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                    provider = pl.lit(provider),
                    subcountry = pl.lit(sub_country),
                    country = pl.lit(country),
                    json_file = pl.lit(str(file_path)),
                )
                df = df.with_columns(
                    timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                )
                full_df.append(df)
            else:
                logger.warning(f"No data found for {sub_country} in {file_path}")

        if full_df:
            return pl.concat(full_df, how="vertical_relaxed")
        else:
            return None
        
    def _get_json_file_name(self, param):
        return param.get("country", "unknown") + ".json"


class KplerAvailabilityPerUnitProcessor(KplerProcessor):

    def __init__(self, is_historical_backfill: bool = False, **kwargs):
        super().__init__(kpler_config.KplerDataType.AvailPerUnit, is_historical_backfill, **kwargs)

    @log_time
    def _build_requests(self):
        params_list = [{"start": self.start_date.strftime("%Y-%m-%d"), "end": self.end_date.strftime("%Y-%m-%d"), "granularity": kpler_config.REQUEST_GRANULARITY, "timezone": kpler_config.REQUEST_TIMEZONE, "country": country, "fuel_type": fuel_type, "as_of": self.as_of.strftime("%Y-%m-%d %H:%M:%S")} for country, fuel_types in kpler_config.DICT_COUNTRY_FUELTYPE_PARAMS.items() for fuel_type in fuel_types if country in self.countries]
        return params_list
    
    def _examine_json(self, data: dict):
        json_keys = data.keys()

        if "detail" in json_keys:
            if type(data["detail"]) == str:
                detail_value = data["detail"].lower()
                if "no production units for" in detail_value:
                    return [], [], [], True

        expected_elements = {
            "provider": str,
            "location": str,
            "timezone": str,
            "metadata": dict,
            "data": dict,
            "index": list,
        }

        missing_keys = set(expected_elements.keys()) - set(json_keys)
        extra_keys = set(json_keys) - set(expected_elements.keys())

        mismatched_data_types = []
        
        for key, expected_type in expected_elements.items():
            if key in json_keys:
                if type(data[key]) != expected_type:
                    mismatched_data_types.append(key)
                if key == "data":
                    data_val = data[key]
                    data_val_keys = data_val.keys()
                    lst_data_val_keys = list(data_val_keys)
                    if len(lst_data_val_keys) == 1 and lst_data_val_keys[0] == "productions":
                        data_val_productions = data_val["productions"]
                        
                        if type(data_val_productions) != dict:
                            mismatched_data_types.append(f"{key}.productions")
                            continue
                        
                        for plant, plant_value in data_val_productions.items():
                            plant_value_keys = plant_value.keys()
                            if set(plant_value_keys) - set(["data", "generations"]):
                                mismatched_data_types.append(f"{key}.productions.{plant}.values")
                    else:
                        mismatched_data_types.append(f"{key}.productions")
                
        return list(missing_keys), list(extra_keys), mismatched_data_types, False
    
    def _flatten_json(self, file_path):
        with open(file_path, 'r') as f:
            full_dict = json.load(f)

        full_df = []

        (missing_keys, extra_keys, mismatched_types, no_production_units) = self._examine_json(full_dict)
        if len(missing_keys) > 0 or len(extra_keys) > 0 or len(mismatched_types) > 0:
            logger.warning(f"Unexpected file format: {file_path} - Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_types}")
            return None
        
        elif no_production_units:
            logger.info(f"No production units for {file_path}")
            return None

        file_name = str(file_path).replace(kpler_config.BASE_DATA_DIR, "")

        provider = full_dict['provider']
        location = full_dict['location']
        timezone = full_dict['timezone']

        metadata = full_dict['metadata']
        data = full_dict["data"]["productions"]
        index_dates = full_dict["index"]
        for key, value in metadata.items():
            plant_id = key
            plant_metadata = value
            if plant_id in data.keys():
                logger.debug(f"Productions Found for {plant_id}")
                plant_data = data[plant_id]
                n = len(index_dates)

                if "data" in plant_data.keys():
                    logger.debug(f"Data Found for {plant_id}")
                    production_data = plant_data["data"]
                    assert len(production_data) == len(index_dates), f"Length of index dates and production data are not equal for {plant_id} in {file_name}"

                    df = pl.DataFrame({
                        "timestamp": index_dates,
                        "availability_amount": production_data
                    })
                    df = df.with_columns(
                        timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                        type = pl.lit('production'),
                        as_of = pl.lit(self.as_of).dt.cast_time_unit('ms'),
                        provider = pl.lit(provider),
                        country = pl.lit(location),
                        unit = pl.lit(plant_id),
                        unit_name = pl.lit(plant_metadata.get('name', '')),
                        generation_code = pl.lit(''),
                        production_code = pl.lit(plant_metadata.get('production_code', plant_id)),
                        asset_type = pl.lit(plant_metadata.get('asset_type', '')),
                        fuel_type = pl.lit(plant_metadata.get('fuel_type', '')),
                        timezone = pl.lit(timezone),
                        json_file = pl.lit(file_name),
                    )
                    
                    df = df.with_columns(
                        timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                        as_of = pl.col('as_of').dt.replace_time_zone("UTC")
                    )
                    df = df.with_columns(pl.col('production_code').fill_null(pl.lit('')))
                    full_df.append(df)
                    

                if "generations" in plant_data.keys():
                    logger.debug(f"Generations Found for {plant_id}")
                    generations_data_dict = plant_data["generations"]
                    for power_gen_id in generations_data_dict.keys():
                        power_generation_data = generations_data_dict[power_gen_id]
                        power_gen_metadata = metadata[power_gen_id]
                        assert len(power_generation_data) == len(index_dates), f"Length of index dates and generation data are not equal for {plant_id} in {file_name}"
                        
                        df = pl.DataFrame({
                            "timestamp": index_dates,
                            "availability_amount": power_generation_data
                        })
                        df = df.with_columns(
                            timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                            type = pl.lit('generation'),
                            as_of = pl.lit(self.as_of).dt.cast_time_unit('ms'),
                            provider = pl.lit(provider),
                            country = pl.lit(location),
                            unit = pl.lit(power_gen_id),
                            unit_name = pl.lit(power_gen_metadata.get('name', '')),
                            generation_code = pl.lit(power_gen_metadata.get('production_code', '')),
                            production_code = pl.lit(plant_metadata.get('production_code', plant_id)),
                            asset_type = pl.lit(power_gen_metadata.get('asset_type', '')),
                            fuel_type = pl.lit(power_gen_metadata.get('fuel_type', '')),
                            timezone = pl.lit(timezone),
                            json_file = pl.lit(file_name),
                        )
                        
                        df = df.with_columns(
                        timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                        as_of = pl.col('as_of').dt.replace_time_zone("UTC")
                        )
                        df = df.with_columns(pl.col('production_code').fill_null(pl.lit('')))
                        full_df.append(df)
            else:
                logger.debug(f"No data section for plant_id: {plant_id}")
        
        if full_df:
            return pl.concat(full_df, how="vertical_relaxed")
        else:
            return None

    @log_time
    def _generate_parquet_files(self, json_path: str):
        all_parquet_files = []

        for country in self.countries:
            fuel_types = kpler_config.DICT_COUNTRY_FUELTYPE_PARAMS[country]
            for fuel_type in fuel_types:
                fuel_type = fuel_type.replace("/", " or ")
                parquet_file_path = self._process_json_folder(json_path, country, fuel_type)
                if parquet_file_path:
                    all_parquet_files.append(parquet_file_path)

        return all_parquet_files

    def _get_json_file_name(self, param):
        country = param.get("country", "unknown")
        fuel_type = param.get("fuel_type", "unknown").replace("/", " or ")
        return f"{country}_{fuel_type}.json"
    
class KplerAvailabilityByFuelTypeProcessor(KplerProcessor):

    def __init__(self, is_historical_backfill: bool = False, **kwargs):
        super().__init__(kpler_config.KplerDataType.AvailPerFuelType, is_historical_backfill, **kwargs)

    @log_time
    def _build_requests(self):
        params_list = [{"start": self.start_date.strftime("%Y-%m-%d"), "end": self.end_date.strftime("%Y-%m-%d"), "granularity": kpler_config.REQUEST_GRANULARITY, "timezone": kpler_config.REQUEST_TIMEZONE, "country": country, "fuel_types": fuel_type, "as_of": self.as_of.strftime("%Y-%m-%d %H:%M:%S")} for country, fuel_types in kpler_config.DICT_COUNTRY_FUELTYPE_PARAMS.items() for fuel_type in fuel_types if country in self.countries]
        return params_list
    
    def _examine_json(self, data: dict):
        json_keys = data.keys()

        expected_elements = {
            "provider": str,
            "location": str,
            "timezone": str,
            "data": dict,
            "index": list,
        }

        expected_data_keys = ["low", "central", "high"]

        missing_keys = list(set(expected_elements.keys()) - set(json_keys))
        extra_keys = list(set(json_keys) - set(expected_elements.keys()))

        mismatched_data_types = []

        if "index" in json_keys:
            timestamps = data["index"]
            if type(timestamps) == list and not timestamps:
                return [], [], [], True
        
        for key, expected_type in expected_elements.items():
            if key in json_keys:
                if type(data[key]) != expected_type:
                    mismatched_data_types.append(key)
                if key == "data":
                    data_val = data[key]
                    data_val_keys = data_val.keys()
                    lst_data_val_keys = list(data_val_keys)
                    missing_data_keys = set(expected_data_keys) - set(lst_data_val_keys)
                    
                    if len(missing_data_keys) > 0:
                        for missing_key in missing_data_keys:
                            missing_keys.append(f"{key}.{missing_key}")
                    extra_data_keys = set(lst_data_val_keys) - set(expected_data_keys)

                    if len(extra_data_keys) > 0:
                        for extra_key in extra_data_keys:
                            extra_keys.append(f"{key}.{extra_key}")
                
        return list(missing_keys), list(extra_keys), mismatched_data_types, False
    
    def _flatten_json(self, file_path):
        with open(file_path, 'r') as f:
            full_dict = json.load(f)

        full_df = []

        (missing_keys, extra_keys, mismatched_types, no_data) = self._examine_json(full_dict)
        if len(missing_keys) > 0 or len(extra_keys) > 0 or len(mismatched_types) > 0:
            logger.warning(f"Unexpected file format: {file_path}. Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_types}")
            return None
        elif no_data:
            logger.warning(f"No data:{file_path}")
            return None
        else:
            logger.debug(f"Expected format: {file_path}")
        
        provider = full_dict['provider']
        location = full_dict['location']
        timezone = full_dict['timezone']

        timestamp = full_dict["index"]

        file_path_to_save = str(file_path).replace(kpler_config.BASE_DATA_DIR, "")

        for level, prod_data in full_dict["data"].items():
            if len(prod_data) > 0:
                for fuel_types, avail_amt in prod_data.items():
                    assert len(avail_amt) == len(timestamp), f"Length of index dates and production data are not equal for {level} in {file_path}"
                    df = pl.DataFrame({
                        "timestamp": timestamp,
                        "availability_amount": avail_amt
                    })
                    df = df.with_columns(
                        timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                        as_of = pl.lit(self.as_of).dt.cast_time_unit('ms'),
                        provider = pl.lit(provider),
                        country = pl.lit(location),
                        timezone = pl.lit(timezone),
                        level = pl.lit(level),
                        fuel_type = pl.lit(fuel_types),
                        json_file = pl.lit(file_path_to_save),
                    )
                    df = df.with_columns(
                        timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                        as_of = pl.col('as_of').dt.replace_time_zone("UTC")
                    )
                    full_df.append(df)
            else:
                logger.warning(f"No data found for {level} in {file_path}")

        if full_df:
            return pl.concat(full_df, how="vertical_relaxed")
        else:
            return None

    @log_time
    def _generate_parquet_files(self, json_path: str):
        all_parquet_files = []

        for country in self.countries:
            fuel_types = kpler_config.DICT_COUNTRY_FUELTYPE_PARAMS[country]
            for fuel_type in fuel_types:
                fuel_type = fuel_type.replace("/", " or ")
                parquet_file_path = self._process_json_folder(json_path, country, fuel_type)
                if parquet_file_path:
                    all_parquet_files.append(parquet_file_path)

        return all_parquet_files

    def _get_json_file_name(self, param):
        country = param.get("country", "unknown")
        fuel_type = param.get("fuel_types", "unknown").replace("/", " or ")
        return f"{country}_{fuel_type}.json"


class KplerGenerationPerFuelTypeProcessor(KplerProcessor):

    def __init__(self, is_historical_backfill: bool = False, **kwargs):
        super().__init__(kpler_config.KplerDataType.GenPerFuelType, is_historical_backfill, **kwargs)

    @log_time
    def _build_requests(self):
        params_list = [{"start": self.start_date.strftime("%Y-%m-%d"), "end": self.end_date.strftime("%Y-%m-%d"), "granularity": kpler_config.REQUEST_GRANULARITY, "timezone": kpler_config.REQUEST_TIMEZONE, "country": country, "fuel_types": fuel_type} for country, fuel_types in kpler_config.DICT_GEN_COUNTRY_FUELTYPE_PARAMS.items() for fuel_type in fuel_types if country in self.countries]
        return params_list
    
    def _examine_json(self, data: dict):
        json_keys = data.keys()

        expected_elements = {
            "provider": str,
            "timezone": str,
            "data": dict,
            "index": list,
            "country": str,
        }

        missing_keys = set(expected_elements.keys()) - set(json_keys)
        extra_keys = set(json_keys) - set(expected_elements.keys())

        mismatched_data_types = []
        
        for key, expected_type in expected_elements.items():
            if key in json_keys:
                if type(data[key]) != expected_type:
                    mismatched_data_types.append(key)
                
        return list(missing_keys), list(extra_keys), mismatched_data_types, False
    
    def _flatten_json(self, file_path):
        with open(file_path, 'r') as f:
            full_dict = json.load(f)

        full_df = []

        (missing_keys, extra_keys, mismatched_types, no_production_units) = self._examine_json(full_dict)
        if len(missing_keys) > 0 or len(mismatched_types) > 0:
            logger.error(f"Unexpected file format: {file_path} - Missing Keys: {missing_keys}, Mismatched Types: {mismatched_types}")
            return None
        
        if len(extra_keys) > 0:
            logger.warning(f"Unexpected file format: {file_path} - Extra Keys: {extra_keys}")
            
        
        # elif no_production_units:
        #     logger.info(f"No production units for {file_path}")
        #     return None

        # base_file_name = os.path.basename(file_path)
        file_name = str(file_path).replace(kpler_config.BASE_DATA_DIR, "")

        # match = re.match(r"^(.{2})_", base_file_name)
        # if not match:
        #     raise ValueError(f"Invalid file name format for {file_path}. Expected format: <country>_<fuel_type>.json")
        
        # country = match.group(1)
        # file_name_comps = base_file_name.split("___", 1)
        # country = file_name_comps[0]
        
        provider = full_dict['provider']
        data = full_dict["data"]
        index_dates = full_dict["index"]
        country = full_dict["country"]
        for fuel_type, gen_data in data.items():
            assert len(gen_data) == len(index_dates), f"Length of index dates and gen data are not equal for {fuel_type} in {file_name}"
            df = pl.DataFrame({
                "timestamp": index_dates,
                "generation": gen_data
            })
            df = df.with_columns(
                timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                provider = pl.lit(provider),
                country = pl.lit(country),
                fuel_type = pl.lit(fuel_type),
                json_file = pl.lit(file_name),
            )
            
            df = df.with_columns(
                timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
            )
            full_df.append(df)
        
        if full_df:
            return pl.concat(full_df, how="vertical_relaxed")
        else:
            return None

    def _get_json_file_name(self, param):
        country = param.get("country", "unknown")
        fuel_type = param.get("fuel_types", "unknown").replace("/", " or ")
        return f"{country}___{fuel_type}.json"


class KplerGenerationPerUnitProcessor(KplerProcessor):

    def __init__(self, is_historical_backfill: bool = False, **kwargs):
        super().__init__(kpler_config.KplerDataType.GenPerUnit, is_historical_backfill, **kwargs)

    @log_time
    def _build_requests(self):
        params_list = [{"start": self.start_date.strftime("%Y-%m-%d"), "end": self.end_date.strftime("%Y-%m-%d"), "granularity": kpler_config.REQUEST_GRANULARITY, "timezone": kpler_config.REQUEST_TIMEZONE, "country": country, "fuel_type": fuel_type} for country, fuel_types in kpler_config.DICT_COUNTRY_FUELTYPE_PARAMS.items() for fuel_type in fuel_types if country in self.countries]
        return params_list

    def _examine_json(self, data: dict):
        json_keys = data.keys()

        expected_elements = {
            "provider": str,
            "timezone": str,
            "metadata": dict,
            "data": dict,
            "index": list,
        }

        missing_keys = set(expected_elements.keys()) - set(json_keys)
        extra_keys = set(json_keys) - set(expected_elements.keys())

        mismatched_data_types = []
        
        for key, expected_type in expected_elements.items():
            if key in json_keys:
                if type(data[key]) != expected_type:
                    mismatched_data_types.append(key)
                
        return list(missing_keys), list(extra_keys), mismatched_data_types, False
    
    def _flatten_json(self, file_path):
        with open(file_path, 'r') as f:
            full_dict = json.load(f)

        full_df = []

        (missing_keys, extra_keys, mismatched_types, no_production_units) = self._examine_json(full_dict)
        if len(missing_keys) > 0 or len(extra_keys) > 0 or len(mismatched_types) > 0:
            logger.warning(f"Unexpected file format: {file_path} - Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_types}")
            return None
        
        # elif no_production_units:
        #     logger.info(f"No production units for {file_path}")
        #     return None

        base_file_name = os.path.basename(file_path)
        file_name = str(file_path).replace(kpler_config.BASE_DATA_DIR, "")

        match = re.match(r"^(.{2})_", base_file_name)
        if not match:
            raise ValueError(f"Invalid file name format for {file_path}. Expected format: <country>_<fuel_type>.json")
        
        country = match.group(1)
        
        provider = full_dict['provider']
        timezone = full_dict['timezone']
        metadata = full_dict['metadata']
        data = full_dict["data"]
        index_dates = full_dict["index"]
        for key, value in metadata.items():
            plant_id = key
            plant_metadata = value
            if plant_id in data.keys():
                logger.debug(f"Generations Found for {plant_id}")
                generation_data = data[plant_id]
                assert len(generation_data) == len(index_dates), f"Length of index dates and production data are not equal for {plant_id} in {file_name}"

                df = pl.DataFrame({
                    "timestamp": index_dates,
                    "generation": generation_data
                })
                df = df.with_columns(
                    timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                    provider = pl.lit(provider),
                    country = pl.lit(country),
                    asset_id = pl.lit(plant_id),
                    unit_name = pl.lit(plant_metadata.get('name', '')),
                    asset_type = pl.lit(plant_metadata.get('asset_type', '')),
                    fuel_type = pl.lit(plant_metadata.get('fuel_type', '')),
                    json_file = pl.lit(file_name),
                )
                
                df = df.with_columns(
                    timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                )
                full_df.append(df)
                    
            else:
                logger.debug(f"No data section for plant_id: {plant_id}")
        
        if full_df:
            return pl.concat(full_df, how="vertical_relaxed")
        else:
            return None

    def _get_json_file_name(self, param):
        country = param.get("country", "unknown")
        fuel_type = param.get("fuel_type", "unknown").replace("/", " or ")
        return f"{country}_{fuel_type}.json"


class KplerForecastLoadProcessor(KplerProcessor):

    def __init__(self, is_historical_backfill: bool = False, **kwargs):
        super().__init__(kpler_config.KplerDataType.ForecastLoad, is_historical_backfill, **kwargs)

    @log_time
    def _build_requests(self):
        params_list = [{"run_date": self.run_date.strftime("%Y-%m-%d"), "kind": kind, "model": model, "granularity": kpler_config.REQUEST_GRANULARITY, "timezone": kpler_config.REQUEST_TIMEZONE, "country": country} for country in self.countries for model in self.forecast_models for kind in self.demand_kind]
        return params_list

    def _examine_json(self, data: dict):
        json_keys = data.keys()

        expected_elements = {
            "provider": str,
            "timezone": str,
            "run_date": str,
            "data": dict,
            "index": list,
        }

        missing_keys = set(expected_elements.keys()) - set(json_keys)
        extra_keys = set(json_keys) - set(expected_elements.keys())

        mismatched_data_types = []
        
        for key, expected_type in expected_elements.items():
            if key in json_keys:
                if type(data[key]) != expected_type:
                    mismatched_data_types.append(key)
                
        return list(missing_keys), list(extra_keys), mismatched_data_types, False
    
    def _flatten_json(self, file_path):
        with open(file_path, 'r') as f:
            full_dict = json.load(f)

        full_df = []

        (missing_keys, extra_keys, mismatched_types, no_production_units) = self._examine_json(full_dict)
        if len(missing_keys) > 0 or len(extra_keys) > 0 or len(mismatched_types) > 0:
            logger.warning(f"Unexpected file format: {file_path} - Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_types}")
            return None
        
        # elif no_production_units:
        #     logger.info(f"No production units for {file_path}")
        #     return None

        file_name = str(file_path).replace(kpler_config.BASE_DATA_DIR, "")
        kind = None
        
        if file_name.endswith("_residual_demand.json"):
            kind = "residual_demand"
        elif file_name.endswith("_demand.json"):
            kind = "demand"
        else:
            raise ValueError(f"Unable to determine kind from file name: {file_name}. Expected to end with one of {self.demand_kind}.")
            
        
        provider = full_dict['provider']
        timezone = full_dict['timezone']
        run_date = full_dict['run_date']
        forecast_data = full_dict["data"]
        index_dates = full_dict["index"]

        if not index_dates:
            return None

        for country, models_dict in forecast_data.items():
            for model, model_output in models_dict.items():
                dict_model_values = {}
                for model_param, model_param_val in model_output.items():
                    assert(len(model_param_val) == len(index_dates)), f"Length of index dates and forecast data are not equal for {model}.{model_param} in {file_name}"
                    dict_model_values[model_param] = [None if v == "null" else v for v in model_param_val]
                
                dict_model_values["timestamp"] = index_dates

                df = pl.DataFrame(dict_model_values).with_columns([pl.col(col).cast(pl.Float64).alias(col) for col in dict_model_values.keys() if col != "timestamp"])
                df = df.with_columns(
                    timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                    rundate = pl.lit(run_date),
                    kind = pl.lit(kind),
                    model = pl.lit(model),
                    country = pl.lit(country),
                    provider = pl.lit(provider),
                    json_file = pl.lit(file_name),
                )
                
                df = df.with_columns(
                    timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                )
                full_df.append(df)

        if full_df:
            return pl.concat(full_df, how="vertical_relaxed")
        else:
            return None

    def _get_json_file_name(self, param):
        country = param["country"]
        kind = param["kind"]
        model = param["model"]
        return f"{country}_{model}_{kind}.json"


class KplerConsumptionPerFuelTypeProcessor(KplerProcessor):

    def __init__(self, is_historical_backfill: bool = False, **kwargs):
        super().__init__(kpler_config.KplerDataType.ConsumptionPerFuelType, is_historical_backfill, **kwargs)

    @log_time
    def _build_requests(self):
        params_list = [{"start": self.start_date.strftime("%Y-%m-%d"), "end": self.end_date.strftime("%Y-%m-%d"), "granularity": kpler_config.REQUEST_GRANULARITY, "timezone": kpler_config.REQUEST_TIMEZONE, "country": country, "fuel_type": "hydro pumped storage"} for country in self.countries]
        return params_list

    def _examine_json(self, data: dict):
        json_keys = data.keys()

        expected_elements = {
            "provider": str,
            "timezone": str,
            "data": dict,
            "index": list,
        }

        missing_keys = set(expected_elements.keys()) - set(json_keys)
        extra_keys = set(json_keys) - set(expected_elements.keys())

        mismatched_data_types = []
        
        for key, expected_type in expected_elements.items():
            if key in json_keys:
                if type(data[key]) != expected_type:
                    mismatched_data_types.append(key)
                
        return list(missing_keys), list(extra_keys), mismatched_data_types, False
    
    def _flatten_json(self, file_path):
        with open(file_path, 'r') as f:
            full_dict = json.load(f)

        full_df = []

        (missing_keys, extra_keys, mismatched_types, no_production_units) = self._examine_json(full_dict)
        if len(missing_keys) > 0 or len(extra_keys) > 0 or len(mismatched_types) > 0:
            logger.warning(f"Unexpected file format: {file_path} - Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_types}")
            return None
        
        # elif no_production_units:
        #     logger.info(f"No production units for {file_path}")
        #     return None

        file_name = str(file_path).replace(kpler_config.BASE_DATA_DIR, "")
        base_file_name = os.path.basename(file_path)
        
        provider = full_dict['provider']
        timezone = full_dict['timezone']
        consumption_data = full_dict["data"]
        index_dates = full_dict["index"]

        match = re.match(r"^(.{2})_", base_file_name)
        if not match:
            raise ValueError(f"Invalid file name format for {file_path}. Expected format: <country>_<fuel_type>.json")
        
        country = match.group(1)

        if base_file_name.endswith("hydro pumped storage.json"):
            fuel_type = "hydro pumped storage"
        else:
            raise ValueError(f"Unable to determine fuel type from file name: {file_name}. Expected to end with hydro pumped storage.")

        for sub_country, consumption_val in consumption_data.items():
            assert len(consumption_val) == len(index_dates), f"Length of index dates and consumption data are not equal for {sub_country} in {file_name}"
            df = pl.DataFrame({
                "timestamp": index_dates,
                "consumption": consumption_val
            })
            df = df.with_columns(
                timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                provider = pl.lit(provider),
                country = pl.lit(country),
                subcountry = pl.lit(sub_country),
                fuel_type = pl.lit(fuel_type),
                json_file = pl.lit(file_name),
            )
            
            df = df.with_columns(
                timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
            )
            full_df.append(df)
        
        if full_df:
            return pl.concat(full_df, how="vertical_relaxed")
        else:
            return None

    def _get_json_file_name(self, param):
        country = param["country"]
        fuel_type = param["fuel_type"].replace("/", " or ")
        return f"{country}_{fuel_type}.json"


class KplerCommercialSchedulesProcessor(KplerProcessor):

    def __init__(self, is_historical_backfill: bool = False, **kwargs):
        super().__init__(kpler_config.KplerDataType.CommercialSchedules, is_historical_backfill, **kwargs)

    @log_time
    def _build_requests(self):
        params_list = [{"start": self.start_date.strftime("%Y-%m-%d"), "end": self.end_date.strftime("%Y-%m-%d"), "granularity": kpler_config.REQUEST_GRANULARITY, "timezone": kpler_config.REQUEST_TIMEZONE, "country": country, "market_type": market_type, "area_type": area_type} for country in self.countries for area_type in self.area_types for market_type in self.market_types]
        return params_list

    def _examine_json(self, data: dict):
        json_keys = data.keys()

        expected_elements = {
            "provider": str,
            "timezone": str,
            "data": dict,
            "index": list,
        }

        missing_keys = set(expected_elements.keys()) - set(json_keys)
        extra_keys = set(json_keys) - set(expected_elements.keys())

        mismatched_data_types = []
        
        for key, expected_type in expected_elements.items():
            if key in json_keys:
                if type(data[key]) != expected_type:
                    mismatched_data_types.append(key)
                
        return list(missing_keys), list(extra_keys), mismatched_data_types, False
    
    def _flatten_json(self, file_path):
        with open(file_path, 'r') as f:
            full_dict = json.load(f)

        full_df = []

        (missing_keys, extra_keys, mismatched_types, no_production_units) = self._examine_json(full_dict)
        if len(missing_keys) > 0 or len(extra_keys) > 0 or len(mismatched_types) > 0:
            logger.warning(f"Unexpected file format: {file_path} - Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_types}")
            return None
        
        # elif no_production_units:
        #     logger.info(f"No production units for {file_path}")
        #     return None

        file_name = str(file_path).replace(kpler_config.BASE_DATA_DIR, "")

        base_file_name = os.path.basename(file_path)
        current_area_type = None
        current_market_type = None
        for area_type in self.area_types:
            if area_type in base_file_name:
                current_area_type = area_type
                break
        for market_type in self.market_types:
            if market_type in base_file_name:
                current_market_type = market_type
                break
        
        provider = full_dict['provider']
        timezone = full_dict['timezone']
        comm_sched_data = full_dict["data"]
        index_dates = full_dict["index"]

        for source_country, comm_sched in comm_sched_data.items():
            for target_country, comm_sched_val in comm_sched.items():
                assert len(comm_sched_val) == len(index_dates), f"Length of index dates and production data are not equal for {source_country} to {target_country} in {file_name}"
                df = pl.DataFrame({
                    "timestamp": index_dates,
                    "value": comm_sched_val
                })
                df = df.with_columns(
                    timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                    provider = pl.lit(provider),
                    area_type = pl.lit(current_area_type),
                    market_type = pl.lit(current_market_type),
                    source_country = pl.lit(source_country),
                    target_country = pl.lit(target_country),
                    json_file = pl.lit(file_name),
                )
                
                df = df.with_columns(
                    timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                )
                full_df.append(df)
        
        if full_df:
            return pl.concat(full_df, how="vertical_relaxed")
        else:
            return None


    def _get_json_file_name(self, param):
        country = param["country"]
        area_type = param["area_type"]
        market_type = param["market_type"]

        return f"{country}_{area_type}_{market_type}.json"   
    

if __name__ == "__main__":
    if len(sys.argv) > 1:
        kpler_data_type = sys.argv[1]
    else:
        raise ValueError("Please provide the Kpler data type as a command line argument.")
    
    processor = kpler_config.DICT_KPLER_PARAMS[kpler_data_type].get("processor", None)
    if not processor:
        raise ValueError(f"No processor found for Kpler data type: {kpler_data_type}")
    
    kpler_processor_instance = globals()[processor]()
    kpler_processor_instance.process_kpler_data()
