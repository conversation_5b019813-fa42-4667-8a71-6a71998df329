from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from bloomberg.utils import get_bbg_full_ticker_for_futures

from bloomberg.per_security.parser import BloombergParser

from utils.date_utils import get_now

import pandas as pd
import numpy as np

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    df_securities = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """select BBG_FULL_TICKER 
            FROM VW_FUTURE_REF 
            WHERE last_tradeable_dt >= CURRENT_DATE;
        """,
    )

    securities = df_securities["BBG_FULL_TICKER"].tolist()

    df_future_roots = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """ SELECT ID_BB_GLOBAL_COMPANY, FUTURE_ROOT FROM BBGH_FUTURES.FUTURE_SERIES """,
    )

    dict_future_roots = dict(zip(df_future_roots["ID_BB_GLOBAL_COMPANY"], df_future_roots["FUTURE_ROOT"]))

    per_sec_req_type = PerSecurityRequestType.getdata
    batch_name = "dp_futref"
    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "sec_id": "TICKER",
        "fields": [
            "ID_BB_GLOBAL",
            "ID_BB_GLOBAL_COMPANY",
            "FUT_CONTRACT_DT",
            "FUT_FIRST_TRADE_DT",
            "LAST_TRADEABLE_DT",
            "FUT_NOTICE_FIRST",
            "FUT_DLV_DT_FIRST",
            "FUT_DLV_DT_LAST",
            "QUOTE_UNITS",
            "QUOTED_CRNCY",
            "FUT_CONT_SIZE",
            "FUT_TICK_VAL",
            "FUT_VAL_PT",
            "FUT_TICK_SIZE",
            "CDR_SETTLE_CODE",
            "ID_MIC_PRIM_EXCH",
        ],
        "securities": securities,
    }
    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_ref/"
    fut_ref_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)
    print(fut_ref_file_path)

    # fut_ref_file_path = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_ref/responses/dp_futref_2502050635.out"

    parser = BloombergParser(fut_ref_file_path, sep='|', skipinitialspace=True, on_bad_lines='error') 
    df_futures = parser.parse_data()

    df_futures['FUT_CONTRACT_DT'] = pd.to_datetime(df_futures['FUT_CONTRACT_DT'], format='%m/%Y')
    idx_missing_fut_contract_dt = df_futures['FUT_CONTRACT_DT'].isna()
    idx_missing_id_bb_global = df_futures['ID_BB_GLOBAL'].isin([None, "", "nan", pd.NA, np.nan])
    idx_invalid_rows = idx_missing_fut_contract_dt | idx_missing_id_bb_global

    df_futures = df_futures[~idx_invalid_rows]
    df_futures.reset_index(drop=True, inplace=True)

    df_futures['FUT_FIRST_TRADE_DT'] = pd.to_datetime(df_futures['FUT_FIRST_TRADE_DT'], format='%m/%d/%Y')
    df_futures['LAST_TRADEABLE_DT'] = pd.to_datetime(df_futures['LAST_TRADEABLE_DT'], format='%m/%d/%Y')
    df_futures['FUT_NOTICE_FIRST'] = pd.to_datetime(df_futures['FUT_NOTICE_FIRST'], format='%m/%d/%Y')
    df_futures['FUT_DLV_DT_FIRST'] = pd.to_datetime(df_futures['FUT_DLV_DT_FIRST'], format='%m/%d/%Y')
    df_futures['FUT_DLV_DT_LAST'] = pd.to_datetime(df_futures['FUT_DLV_DT_LAST'], format='%m/%d/%Y')

    df_futures["FUT_CONT_SIZE"] = pd.to_numeric(df_futures["FUT_CONT_SIZE"], errors="coerce")
    df_futures["FUT_TICK_VAL"] = pd.to_numeric(df_futures["FUT_TICK_VAL"], errors="coerce")
    df_futures["FUT_VAL_PT"] = pd.to_numeric(df_futures["FUT_VAL_PT"], errors="coerce")
    df_futures["FUT_TICK_SIZE"] = pd.to_numeric(df_futures["FUT_TICK_SIZE"], errors="coerce")

    df_futures["FUTURE_ROOT"] = df_futures["ID_BB_GLOBAL_COMPANY"].map(dict_future_roots)
    
    df_futures["BBG_FULL_TICKER"] = df_futures.apply(lambda x: get_bbg_full_ticker_for_futures(x["FUTURE_ROOT"], x["FUT_CONTRACT_DT"].month, x["FUT_CONTRACT_DT"].year), axis=1)
    df_futures["SECURITY"] = df_futures["SECURITY"].str.split(" ").str[0]

    df_futures["UPDATE_SOURCE"] = 3
    df_futures["LAST_UPDATED"] = get_now()
    df_futures["UPDATED_BY"] = "bbg_dl_futures_ref.py"

    df_futures.rename(columns={"SECURITY": "BBG_ORIG_TICKER"}, inplace=True)    

    df_futures = df_futures[["ID_BB_GLOBAL", "BBG_FULL_TICKER", "ID_BB_GLOBAL_COMPANY", "FUT_CONTRACT_DT", "FUT_FIRST_TRADE_DT", "LAST_TRADEABLE_DT", "FUT_NOTICE_FIRST", "FUT_DLV_DT_FIRST", "FUT_DLV_DT_LAST", "QUOTE_UNITS", "QUOTED_CRNCY", "FUT_CONT_SIZE", "FUT_TICK_VAL", "FUT_VAL_PT", "FUT_TICK_SIZE", "CDR_SETTLE_CODE", "ID_MIC_PRIM_EXCH", "BBG_ORIG_TICKER", "UPDATE_SOURCE", "LAST_UPDATED", "UPDATED_BY"]]
    
    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_FUTURES",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
    )

    ret_val = adaptor.upsert(df_futures, "FUTURE_REF_DL", ["ID_BB_GLOBAL"])
    print(ret_val)
