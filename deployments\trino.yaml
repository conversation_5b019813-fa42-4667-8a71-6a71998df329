apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: trino-cfg-vol
spec:
  storageClassName: efs-sc
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: cwiq-log-vol
spec:
  storageClassName: gp2-encrypted
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
---
kind: ConfigMap 
apiVersion: v1 
metadata:
  name: trino-configs
data:
  jvm.config: |-
    -server
    -Xmx32G
    -XX:+UseG1GC
    -XX:G1HeapRegionSize=32M
    -XX:+ExplicitGCInvokesConcurrent
    -XX:+ExitOnOutOfMemoryError
    -XX:+UseGCOverheadLimit
    -XX:+HeapDumpOnOutOfMemoryError
    -XX:ReservedCodeCacheSize=512M
    -Djdk.attach.allowAttachSelf=true
    -Djdk.nio.maxCachedBufferSize=2000000
    -Dcom.sun.jndi.ldap.object.disableEndpointIdentification=true
  config.properties.coordinator: |-
    coordinator=true
    node-scheduler.include-coordinator=false
    http-server.http.port=8080
    discovery.uri=http://trino:8080
    http-server.authentication.type=PASSWORD
    internal-communication.https.required=false
    internal-communication.shared-secret=t7Jdp9Qm6vBkZx1L
    http-server.https.enabled=true
    http-server.https.port=8443
    http-server.https.keystore.path=/jfs/tech1/apps/trino/trinokey.pem
    http-server.authentication.allow-insecure-over-http=true
    http-server.process-forwarded=true
    password-authenticator.config-files=/etc/trino/ldap-password-authenticator.properties,/etc/trino/file-password-authenticator.properties
  config.properties.worker: |-
    coordinator=false
    http-server.http.port=8080
    discovery.uri=http://trino:8080
    http-server.authentication.type=PASSWORD
    internal-communication.https.required=false
    internal-communication.shared-secret=t7Jdp9Qm6vBkZx1L
    http-server.https.enabled=true
    http-server.https.port=8443
    http-server.https.keystore.path=/jfs/tech1/apps/trino/trinokey.pem
    http-server.authentication.allow-insecure-over-http=true
    http-server.process-forwarded=true
    password-authenticator.config-files=/etc/trino/ldap-password-authenticator.properties,/etc/trino/file-password-authenticator.properties
  log.properties: |-
    io.trino=DEBUG
    io.trino.plugin.password=DEBUG
  node.properties: |-
    node.environment=docker
    node.data-dir=/data/trino
  iceberg.properties: |-
    connector.name=iceberg
    iceberg.catalog.type=testing_file_metastore
    iceberg.register-table-procedure.enabled=true
    fs.hadoop.enabled=true
    hive.metastore.catalog.dir=/jfs/tech1/apps/trino/catalog/${ENV}
  iceberg_rest.properties: |-
    connector.name=iceberg
    iceberg.catalog.type=rest
    iceberg.rest-catalog.uri=http://iceberg-rest:8181
    iceberg.rest-catalog.warehouse=/mnt/data/iceberg
    iceberg.file-format=PARQUET
  ldap-password-authenticator.properties: |-
    password-authenticator.name=ldap
    ldap.url=ldap://10.112.1.10:389
    ldap.allow-insecure=true
    #ldap.ssl.truststore.path=/jfs/tech1/apps/trino/ldap_server.pem
    ldap.user-bind-pattern=#BIND#@jainglobal.local
  file-password-authenticator.properties: |-
    password-authenticator.name=file
    file.password-file=/jfs/tech1/apps/trino/password.db
  access-control.properties: |-
    access-control.name=file
    security.config-file=/jfs/tech1/apps/trino/rules.json
    security.refresh-period=1s
  group-provider.properties: |-
    group-provider.name=file
    file.group-file=/jfs/tech1/apps/trino/group.txt
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trino-coordinator
spec:
  selector:
    matchLabels:
      app: trino-coordinator
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: trino-coordinator
    spec:
      containers:
      - name: trino
        image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-trino-${ENV}:latest
        securityContext:
            privileged: true
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: trino-cfg-vol
          mountPath: /etc/trino/jvm.config
          subPath: jvm.config
        - name: trino-cfg-vol
          mountPath: /etc/trino/log.properties
          subPath: log.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/config.properties
          subPath: config.properties.coordinator
        - name: trino-cfg-vol
          mountPath: /etc/trino/node.properties
          subPath: node.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/catalog/iceberg.properties
          subPath: iceberg.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/catalog/iceberg_rest.properties
          subPath: iceberg_rest.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/ldap-password-authenticator.properties
          subPath: ldap-password-authenticator.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/file-password-authenticator.properties
          subPath: file-password-authenticator.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/access-control.properties
          subPath: access-control.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/group-provider.properties
          subPath: group-provider.properties
        - name: cwiq-log-vol
          mountPath: /var/log/cwiq/cwiqfs
        imagePullPolicy: Always
      volumes:
        - name: trino-cfg-vol
          configMap:
            name: trino-configs
        - name: cwiq-log-vol
          persistentVolumeClaim:
            claimName: cwiq-log-vol
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: trino-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: trino-worker
  template:
    metadata:
      labels:
        app: trino-worker
    spec:
      containers:
      - name: trino
        image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-trino-${ENV}:latest
        securityContext:
            privileged: true
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: trino-cfg-vol
          mountPath: /etc/trino/jvm.config
          subPath: jvm.config
        - name: trino-cfg-vol
          mountPath: /etc/trino/log.properties
          subPath: log.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/config.properties
          subPath: config.properties.worker
        - name: trino-cfg-vol
          mountPath: /etc/trino/node.properties
          subPath: node.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/catalog/iceberg.properties
          subPath: iceberg.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/catalog/iceberg_rest.properties
          subPath: iceberg_rest.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/ldap-password-authenticator.properties
          subPath: ldap-password-authenticator.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/file-password-authenticator.properties
          subPath: file-password-authenticator.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/access-control.properties
          subPath: access-control.properties
        - name: trino-cfg-vol
          mountPath: /etc/trino/group-provider.properties
          subPath: group-provider.properties
        - name: cwiq-log-vol
          mountPath: /var/log/cwiq/cwiqfs
        imagePullPolicy: Always
      volumes:
        - name: trino-cfg-vol
          configMap:
            name: trino-configs
        - name: cwiq-log-vol
          persistentVolumeClaim:
            claimName: cwiq-log-vol
---
apiVersion: v1
kind: Service
metadata:
  name: trino
spec:
  selector:
    app: trino-coordinator
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: NodePort
  externalTrafficPolicy: Local
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "trino-ingress"
  annotations:
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/certificate-arn: ${SSL_CERTIFICATE_ARN}
  labels:
    app: trino-nginx-ingress
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: "trino"
              port:
                number: 8080