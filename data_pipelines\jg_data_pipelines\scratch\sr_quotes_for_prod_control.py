import os
import re
import pandas as pd
from datetime import datetime
from utils.postgres.adaptor import PostgresAdaptor

_option_spns = [78077882 ,78079983 ,78079984 ,78080497 ,78080498 ,78080881 ,78081166 ,78082016 ,78082316 ,78082448 ,78082780 ,78083055 ,78083163 ,78083524 ,78083524 ,78083524 ,78083524 ,78084160 ,78084253 ,78084273 ,78084564 ,78084564 ,78084567 ,78084569 ,78084575 ,78084582 ,78084583 ,78084585 ,78084902 ,78085209 ,78085210 ,78085681 ,78085682 ,78085686 ,78085687 ,78085688 ,78085689 ,78085718 ,78085879 ,78085880 ,78085880 ,78085965 ,78085966 ,78085967 ,78085968 ,78085969 ,78085970 ,78085985 ,78086347 ,78086599 ,78086607 ,78086635 ,78087047 ,78087093 ,78087094 ,78087097 ,78087098 ,78087099 ,78087112 ,78087131 ,78087331 ,78087344 ,78087345 ,78087346 ,78087347 ,78087348 ,78087362 ,78087364 ,78087596 ,78087872 ,78087910 ,78087955 ,78087956 ,78087957 ,78087958 ,78087959 ,78087960 ,78088169 ,78089314 ,78089319 ,78089320 ,78089326 ,78089360 ,78089600 ,78089601 ,78089604 ,78089605 ,78089606 ,78089609 ,78089610 ,78089625 ,78089626 ,78089638 ,78089642 ,78089832 ,78089902 ,78089907 ,78089927 ,78089928 ,78089935 ,78089946 ,78089947 ,78090120 ,78090125 ,78090140 ,78090147 ,78090150 ,78090181 ,78090368 ,78090418 ,78090419 ,78090438 ,78090439 ,78090443 ,78090703 ,78090705 ,78090706 ,78090719 ,78090724 ,78090726 ,78091191 ,78091192 ,78091226 ,78091236 ,78091237 ,78091349 ,78091357 ,78091357 ,78091357 ,78091357 ,78091357 ,78091357 ,78091385 ,78091386 ,78091388 ,78091462 ,78091463 ,78092653 ,78092654 ,78092666 ,78092673 ,78092723 ,78092728 ,78092735 ,78092736 ,78092738 ,78092739 ,78092743 ,78092753 ,78092932 ,78092955 ,78092987 ,78092989 ,78092991 ,78092992 ,78092998 ,78093000 ,78093001 ,78093002 ,78093189 ,78093190 ,78093215 ,78093219 ,78093272 ,78093276 ,78093277 ,78093280 ,78093287 ,78093288 ,78093295 ,78093295 ,78093307 ,78093525 ,78093533 ,78093565 ,78093566 ,78093584 ,78093590 ,78093591 ,78093593 ,78093596 ,78093598 ,78094988 ,78095012 ,78095044 ,78095344 ,78095345 ,78095347 ,78095348 ,78095761 ,78095762 ,78095765 ,78095783 ,78095796 ,78095797 ,78095817 ,78095823 ,78095824 ,78095825 ,78095827 ,78095828 ,78095829 ,78095831 ,78095832 ,78095840 ,78096301 ,78096306 ,78096307 ,78096608 ,78096609 ,78096655 ,78096662 ,78096664 ,78096665 ,78096666 ,78096676 ,78097035 ,78097068 ,78097070 ,78097071 ,78097073 ,78098171 ,78098172 ,78098173 ,78098187 ,78098188 ,78098193 ,78098196 ,78099432 ,78099433 ,78099464 ,78100034 ,78100035 ,78100036 ,78100037 ,78100074 ,78100075 ,78100076 ,78100077 ,78100829 ,78100831 ,78100834 ,78100835 ,78100847 ,78100852 ,78100878 ,78100924 ,78100925 ,78100949 ,78101362 ,78101403 ,78101404 ,78101405 ,78101459 ,78101472 ,78101473 ,78101474 ,78101475 ,78101476 ,78101477 ,78101909 ,78101910 ,78101934 ,78101935 ,78101936 ,78101943 ,78101930 ,78101990 ,78101993 ,78102008 ,78102178 ,78102188 ,78102189 ,78102190]
bus_date = "2025-03-27"

OPTION_SEC_TYPES = [14]
OPTION_UNDL_TO_EXCLUDE = ['NDX', 'SPX', 'SPXW']

def parse_option_type(option_str):
    pattern = r'(?P<ticker>\S+)\s+US\s+(?P<date>\d{2}/\d{2}/\d{2})\s+(?P<type>[CP])(?P<strike>\d+(?:\.\d+)?)'
    match = re.match(pattern, option_str.strip())
    
    if not match:
        raise ValueError(f"Invalid option string format: '{option_str}'")
    
    opt_type = match.group('type')
    strike = float(match.group('strike'))
    expiration = datetime.strptime(match.group('date'), "%m/%d/%y").strftime("%Y-%m-%d")

    return opt_type
 
if __name__ == "__main__":
    loader = PostgresAdaptor(
        host="tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com",
        database="postgres",
        schema="refdata",
        user=os.environ["RDS_PROD_USERNAME"],
        password=os.environ["RDS_PROD_PASSWORD"],
    )
    
    option_spn_str = ",".join([str(spn) for spn in _option_spns])
    query = "select spn, bloomberg, underlying, strike_price as raw_strike_price, expiration_date, sfs_type_id from refdata.securities where spn in ({})".format(option_spn_str)
    # print(query)
    df_opts_ref = loader.execute_query(query)

    df_opts_ref["underlying"] = df_opts_ref["underlying"].astype("Float64")
    df_opts_ref["underlying"] = df_opts_ref["underlying"].apply(lambda x: int(x) if pd.notna(x) else None)
    df_opts_ref["sfs_type_id"] = df_opts_ref["sfs_type_id"].astype("int")
    df_opts_ref["call_put"] = df_opts_ref["bloomberg"].apply(parse_option_type)
    df_opts_ref["strike_price"] = df_opts_ref["raw_strike_price"].apply(lambda x: int(x*1000))

    df_opts_ref = df_opts_ref[~df_opts_ref['bloomberg'].str.startswith(tuple(OPTION_UNDL_TO_EXCLUDE))]
    
    idx_na_undl = pd.isna(df_opts_ref["underlying"])
    if any(idx_na_undl):
        spn_without_undl = df_opts_ref.loc[idx_na_undl, "spn"].tolist()
        raise ValueError("Underlying is NaN for SPN(s) {}".format(spn_without_undl))
    
    idx_option_type = df_opts_ref["sfs_type_id"].isin(OPTION_SEC_TYPES)
    if any(~idx_option_type):
        non_option_spns = df_opts_ref.loc[~idx_option_type, "spn"].tolist()
        raise ValueError("Non Option SPN(s) {}".format(non_option_spns))

    underlying_spn = df_opts_ref["underlying"].unique().tolist()
    underlying_spn_str = ",".join([str(spn) for spn in underlying_spn])
    query_undl_tickers = "select spn, bloomberg || ' ' || 'Equity' as bbg_full_ticker from refdata.securities where spn in ({})".format(underlying_spn_str)
    df_undl_tickers = loader.execute_query(query_undl_tickers)
    dict_undl_sec = dict(zip(df_undl_tickers["spn"], df_undl_tickers["bbg_full_ticker"]))
    df_opts_ref["undl_bbg_ticker"] = df_opts_ref["underlying"].map(dict_undl_sec)
    df_opts_ref = df_opts_ref[["spn", "bloomberg", "undl_bbg_ticker", "strike_price", "expiration_date", "call_put"]]
    
    bbg_tickers = df_undl_tickers["bbg_full_ticker"].unique().tolist()
    df_query_opt_quotes_all = pd.DataFrame()
    for bbg_ticker in bbg_tickers:
        print(f"Processing ticker: {bbg_ticker}")
        query_opt_quotes = "select call_put, expiration_date, strike_price, bid_price, ask_price, sr_srctimestamp from risk_fdw.vw_eq_opt_eod_price where undl_bbg_ticker = '{}' and bus_date = '{}'".format(bbg_ticker, bus_date)
        df_query_opt_quotes = loader.execute_query(query_opt_quotes)
        df_opt_ref_sec = df_opts_ref[df_opts_ref["undl_bbg_ticker"] == bbg_ticker]
        df_opt_ref_sec.set_index(["call_put", "expiration_date", "strike_price"], inplace=True)
        df_query_opt_quotes.set_index(["call_put", "expiration_date", "strike_price"], inplace=True)
        df_opt_ref_sec = df_opt_ref_sec.join(df_query_opt_quotes, how="left")
        df_opt_ref_sec.reset_index(inplace=True)
        df_query_opt_quotes_all = pd.concat([df_query_opt_quotes_all, df_opt_ref_sec], ignore_index=True)

    df_query_opt_quotes_all = df_query_opt_quotes_all[["spn", "bloomberg", "undl_bbg_ticker", "strike_price", "expiration_date", "call_put", "bid_price", "ask_price", "sr_srctimestamp"]]
    df_query_opt_quotes_all["strike_price"] = df_query_opt_quotes_all["strike_price"].apply(lambda x: float(x/1000))
    df_query_opt_quotes_all['sr_srctimestamp'] = df_query_opt_quotes_all['sr_srctimestamp'].dt.tz_localize('UTC').dt.tz_convert('America/New_York').dt.tz_localize(None)
    df_query_opt_quotes_all.reset_index(inplace=True, drop=True)
    print(df_query_opt_quotes_all.head())
    # df_query_opt_quotes_all.to_csv("opt_quotes_{dt}.csv".format(dt = bus_date.replace("-", "")), index=False)
    