import json
import requests
import time
import pandas as pd
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s %(levelname)s:%(name)s:%(message)s', datefmt='%Y-%m-%d %H:%M:%S')
logger = logging.getLogger(__name__)

url = "http://10.115.0.92:8006/delayed-prices"
# payload = {"securities": ['BBG000HVM1N0']}
url = "https://dataservice-uat.jainglobal.net/delayed-prices"
api_key = ""

payload = {
  "securities": [
"KUOA MF EQUITY",
"KUOA MU EQUITY",
"KUOA MM EQUITY",
    # "AAPL US EQUITY"
  ]
}
response = requests.post(url, json=payload, headers={"api_key": ""})
 
if response.status_code == 200:
    print(f"Status Code: {response.status_code}")
    print(f"Text: {response.text[:100]}...")
    prices = response.json()
    print(prices)
    # prices = [row for row in prices if row["price"] != ""]
    # df = pd.DataFrame(prices)
    # df.columns = ["figi", "px_lcl", "currency"]
    # print(df)
else:
    print(f"Status Code: {response.status_code}")
    print(response.text)