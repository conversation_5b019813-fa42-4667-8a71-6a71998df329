import os
import gzip
import shutil
import tempfile
import pandas as pd

from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from utils.date_utils import get_n_bday_yyyymmdd, get_n_bday, get_now
from bloomberg.bpipe import <PERSON>pipeHandler

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    df_securities = sf_adaptor.read_data(
        "BBGH_ONDEMAND",
        """select DISTINCT BBG_TICKER from BBG_RISK_BATCH_TICKERS WHERE BBG_SOURCE = 2;
        """,
    )
    
    securities = df_securities["BBG_TICKER"].tolist()
    print(securities)

    to_date = get_n_bday(1)
    from_date = get_n_bday(15)


    bpipe_instance = ("Tech1ProdBPipe47537.jainglobal.net", 8194)
    bpipe_app = "JAIN:pmdashboard-bps"
    bpipe_handler = BpipeHandler(bpipe_instance, bpipe_app)

    df_risk_index_hist = bpipe_handler.fetch_hist(p_ticker=securities, 
    p_field=["PX_OPEN", "PX_HIGH", "PX_LOW", "PX_LAST"], 
    p_start=from_date, p_end=to_date)

    df_risk_index_hist.rename(columns= {"ticker": "BBG_TICKER", "field": "FIELD", "date": "DATE", "value": "VALUE"}, inplace=True)
    df_risk_index_hist["DATE"] = pd.to_datetime(df_risk_index_hist["DATE"], format="%m/%d/%Y", errors="coerce")
    df_risk_index_hist["VALUE"] = pd.to_numeric(df_risk_index_hist["VALUE"], errors="coerce")

    df_risk_index_hist["WHEN_UPDATED"] = get_now()
    df_risk_index_hist["BBG_SOURCE"] = 2

    # print(df_risk_index_hist.head())

    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_ONDEMAND",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
    )

    ret_val = adaptor.upsert(df_risk_index_hist, "BBG_RISK_HIST_DATA", ["BBG_TICKER", "FIELD", "DATE"])
    print(ret_val)