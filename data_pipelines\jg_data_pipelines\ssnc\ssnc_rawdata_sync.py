import boto3
import os, sys
import time
from datetime import datetime, timezone
from botocore.exceptions import NoCredentialsError
sys.path.append(os.getcwd())
import json
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '../../../source/bin')))
from strunner import *
setupEnvironment()
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def download_recent_s3_files(target_date, bucket_name, s3_prefix, rawdata_jfs_path):
    
    target_date = datetime.strptime(target_date, '%Y-%m-%d').date()
    
    s3 = boto3.client('s3')
    paginator = s3.get_paginator('list_objects_v2')
    pages = paginator.paginate(Bucket=bucket_name, Prefix=s3_prefix)
    
    os.makedirs(rawdata_jfs_path, exist_ok=True)

    try:
        for page in pages:
            if 'Contents' not in page:
                continue
            
            for obj in page.get('Contents', []):
                last_modified = obj['LastModified'].astimezone(timezone.utc)
                if last_modified.date() == target_date:
                    key = obj['Key']
                    filename = os.path.basename(key)
                    local_path = os.path.join(rawdata_jfs_path, filename)
                    
                    if not os.path.exists(local_path):
                        s3.download_file(bucket_name, key, local_path)
                        mod_time = last_modified.timestamp()
                        os.utime(local_path, (mod_time, mod_time))
                        logger.info(f"Downloaded {key} to {rawdata_jfs_path}")
                        
                    else:
                        logger.debug(f"File path {local_path} already exists")

    except Exception as e:
        logger.error(f"An error occurred: {e}")

if __name__ == "__main__":
    configPath = os.environ.get('CONFIG_PATH', os.getcwd())
    with open(f'{configPath}/config.json', 'r') as f:
        config = json.load(f)

    def jg_config_path():
        return config["JG_CONFIG_PATH"]
    
    def jg_rawdata_path():
        return config["JG_DATA_PATH"]

    def read_config_secrets():
        config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
        with open(config_secret_path, 'r') as f:
            config_secret = json.load(f)
        return config_secret
    objconfig = read_config_secrets()
    
    target_date = datetime.now(timezone.utc).date().strftime('%Y-%m-%d')
    logger.info(f"Syncing for the date:: {target_date}")
    bucket_name = objconfig['ssnc_s3_bucket']
    s3_prefix = objconfig['ssnc_s3_prefix']
    rawdata_jfs_path = f'{jg_rawdata_path()}/SSandC'

    download_recent_s3_files(target_date, bucket_name, s3_prefix, rawdata_jfs_path)