{"cells": [{"cell_type": "code", "execution_count": 40, "id": "fb8ba45f", "metadata": {}, "outputs": [], "source": ["import requests\n", "import getpass\n", "import pandas as pd\n", "import json\n", "import os\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": null, "id": "8b376fd3-0a2c-473e-97c5-395d2b627fda", "metadata": {}, "outputs": [], "source": ["baseUrl = 'https://www.globaltradetracker.com/api/rest'\n", "\n", "userid = #YOUR USERNAME\n", "password = #YOUR PASSWORD\n", "\n", "TOKEN = requests.get(baseUrl + \"/gettoken?userid=\" + userid + \"&password=\" + password).text"]}, {"cell_type": "markdown", "id": "c0f5f0f3", "metadata": {}, "source": ["## Tables with GTT's data structure info"]}, {"cell_type": "code", "execution_count": 3, "id": "601aca4f", "metadata": {}, "outputs": [], "source": ["#Subscription Definitions\n", "subscriptions = requests.get(baseUrl + \"/subscriptions?token=\" + TOKEN)\n", "period_types = requests.get(baseUrl + \"/subscriptions?token=\" + TOKEN +\"&periodtype=M\")"]}, {"cell_type": "code", "execution_count": 4, "id": "c379daff", "metadata": {}, "outputs": [{"data": {"text/plain": ["'https://www.globaltradetracker.com/api/rest/subscriptions?token=B1B9C986450667BADEEB329DA4668141&periodtype=M'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["baseUrl + \"/subscriptions?token=\" + TOKEN +\"&periodtype=M\""]}, {"cell_type": "code", "execution_count": null, "id": "5840ae1e", "metadata": {}, "outputs": [], "source": ["#Available Countries\n", "countries = requests.get(baseUrl + \"/countries?token=\" + TOKEN + \"&periods=true\").json()\n", "country_data=list(map(lambda x: pd.DataFrame.from_dict(x['country'], orient='index').transpose(),countries))\n", "countries = pd.concat(country_data, ignore_index=True)\n", "countries.to_csv('countries.csv', index=False)"]}, {"cell_type": "markdown", "id": "8af92b37", "metadata": {}, "source": ["### Trade details by country (Brazil example)"]}, {"cell_type": "code", "execution_count": 123, "id": "a0a80e72", "metadata": {}, "outputs": [], "source": ["trade_details=['SUBDIVISION','PORT','TRANSPORT','FOREIGN_PORT','PARTNER_US_STATE','CUSTOMS_REGIME','SUPPRESSION']"]}, {"cell_type": "code", "execution_count": null, "id": "89a574c6", "metadata": {}, "outputs": [], "source": ["dfs=[]\n", "for t in trade_details:\n", "    trade_data = requests.get(baseUrl + \"/tradedetails?token=\" + TOKEN + \"&countryCode=BR\"+\"&tradedetails={}\".format(t)).json()\n", "    if len(trade_data) > 0:\n", "        flat_trade_data = [item[t] for item in trade_data]\n", "        df_trade_details =  pd.DataFrame(flat_trade_data)\n", "        df_trade_details['trade_detail']=t\n", "        dfs.append(df_trade_details)\n", "BR_trade_details = pd.concat(dfs)"]}, {"cell_type": "code", "execution_count": 7, "id": "1ae732c8", "metadata": {}, "outputs": [], "source": ["#Need to make sure your default Layout Template includes all available fields\n", "report_fields = requests.get(baseUrl + \"/fields?token=\" + TOKEN).json()\n", "df_report_fields=pd.DataFrame(report_fields)\n", "df_report_fields.to_csv('report_fields.csv', index=False)"]}, {"cell_type": "code", "execution_count": 8, "id": "59a8d749", "metadata": {}, "outputs": [], "source": ["country_list = ','.join(list(countries.alphageonom2))\n", "updatedAfter = '2024-04-01' #just an example date, you can change it to any date you want\n", "data_updates = requests.get(baseUrl + \"/dataupdates?token=\" + TOKEN + \"&countryCode=\" + country_list\n", "                            + \"&updatedAfter=\" +updatedAfter).json()\n", "df_data_updates=pd.DataFrame(data_updates)\n", "df_data_updates.to_csv('data_updates.csv', index=False)"]}, {"cell_type": "code", "execution_count": 9, "id": "faf2dca5", "metadata": {}, "outputs": [{"data": {"text/plain": ["['2501', '2408']"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["gtt_versions = requests.get(baseUrl + \"/versions?token=\" + TOKEN).json()\n", "gtt_versions"]}, {"cell_type": "markdown", "id": "a99944d2", "metadata": {}, "source": ["## Report Generation Example:"]}, {"cell_type": "markdown", "id": "4042dcb0", "metadata": {}, "source": ["### Conclusion 1: not all countries have the same trade_detail level available. \n", "For example BR has Subdivision, port and transport but it doesn't have foreign port and the others).\n", "Even if you feed that trade_data type in the tradedetails parameter you won't get an empty column\n", "As a result you may get a different number of columns for a report for two different couuntries.\n", "this can be fixed by manually adding an empty column and presetting the order of columns for each dataframe"]}, {"cell_type": "code", "execution_count": 237, "id": "508350a0", "metadata": {}, "outputs": [], "source": ["#report params\n", "hsCode       = \"17\" #Sugar\n", "countryCode = \"BR\"\n", "fromDate     = \"2025-01\"\n", "flow = \"E\"\n", "hslevel = \"-1\" #tariff level (options are 0:do not explode, 2:HS2, 4:HS4, 6:HS6, -1:tariff lvl)\n", "\n", "#settings params\n", "layout = \"brief\"\n", "period_type = \"M\"\n", "decimal_places = \"4\"\n", "tradedetails=\"SUBDIVISION,PORT,TRANSPORT,FOREIGN_PORT,PARTNER_US_STATE,CUSTOMS_REGIME,SUPPRESSION\" #all trade details\n"]}, {"cell_type": "code", "execution_count": 238, "id": "d27e0db0", "metadata": {}, "outputs": [], "source": ["#Notice we will need both the field id and the field name\n", "# We need field id for the API call\n", "# the API call returns a report that returns field name (not field id) as column headers\n", "# notice that if a report doesn't contain a field name (e.g is not applicable for that country)\n", "# it won't output that field name as empty in the API call output but instead it won't include it\n", "# when wrangling the data we would want this field empty as we need to match the column number/names for all countries\n", "# so we can fit all the reports in a single table\n", "\n", "#unless you specify layout parameter to brief. In which case the report uses the field id io. field name\n", "\n", "fields_list=list(df_report_fields.id.unique())\n", "field_names=list(df_report_fields.name.unique())"]}, {"cell_type": "code", "execution_count": 239, "id": "309e42df", "metadata": {}, "outputs": [], "source": ["url = baseUrl + \"/getreport?token=\" + TOKEN + \"&impexp=\" + flow + \\\n", "                      \"&hscode=\" + hsCode+ \"&reporter=\" + countryCode + \\\n", "                      \"&from=\" + fromDate + \"&field=\" + ','.join(fields_list) + \"&periodtype=\" + period_type + \\\n", "                      \"&layout=\" + layout + \"&tradedetails=\" + tradedetails +  \"&hslevel=\" + hslevel + \"&decimalscale=\" + decimal_places\n", "report = requests.get(url).json()"]}, {"cell_type": "code", "execution_count": 240, "id": "f4e7f679", "metadata": {}, "outputs": [{"data": {"text/plain": ["'https://www.globaltradetracker.com/api/rest/getreport?token=B1B9C986450667BADEEB329DA4668141&impexp=E&hscode=17&reporter=BR&from=2025-01&field=tradeFlow,isMirror,periodType,year_start,month_start,year_end,month_end,year,month,reporterCountryNo,reporterCode,reporterIsoAlpha3Code,reporterIsoNumeric3Code,reporterName,reporterDescription,reporterExpandedGroup,firstAvailablePeriod,lastAvailablePeriod,reporterSource,incoterm,partnerCountryNo,partnerCode,partnerIsoAlpha3Code,partnerIsoNumeric3Code,partnerName,partnerExpandedGroup,hsCode,commodityDescriptionOriginal,commodityDescriptionTranslation,hs2Code,hs2DescriptionOriginal,hs2DescriptionTranslation,hs4Code,hs4DescriptionOriginal,hs4DescriptionTranslation,hs6Code,hs6DescriptionOriginal,hs6DescriptionTranslation,commodityExpandedGroup,commodityExplodedHsCode,commodityExplodedDescriptionOriginal,commodityExplodedDescriptionTranslation,subdivision,port,transport,foreignPort,usState,customsRegime,suppression,currency,value,unit1,quantity1,q1Estimation,unit2,quantity2,q2Estimation,unitPreferred,quantityPreferred,qPreferredEstimation,priceunit1,price1,priceunit2,price2,priceunitPreferred,pricePreferred&periodtype=M&layout=brief&tradedetails=SUBDIVISION,PORT,TRANSPORT,FOREIGN_PORT,PARTNER_US_STATE,CUSTOMS_REGIME,SUPPRESSION&hslevel=-1&decimalscale=4'"]}, "execution_count": 240, "metadata": {}, "output_type": "execute_result"}], "source": ["url"]}, {"cell_type": "code", "execution_count": 241, "id": "0fd53c3b", "metadata": {}, "outputs": [], "source": ["df_report_sample=pd.DataFrame(report['reportData'])\n", "df_report_sample.to_csv(\"API_report_BR_exp.csv\",index=False)"]}, {"cell_type": "code", "execution_count": 242, "id": "a75ebac4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["there are  66 fields\n", "there are  61 unique field names, some correspond to multiple field ids\n", "this report outputs  54 columns (fields), some of these columns are empty\n"]}], "source": ["print(\"there are \", len(fields_list), \"fields\")\n", "print(\"there are \", len(field_names), \"unique field names, some correspond to multiple field ids\")\n", "print(\"this report outputs \", len(list(df_report_sample.columns)), \"columns (fields), some of these columns are empty\", )\n"]}, {"cell_type": "code", "execution_count": 243, "id": "600aa1b1", "metadata": {}, "outputs": [], "source": ["diff_list = set(fields_list) - set(list(df_report_sample.columns))"]}, {"cell_type": "code", "execution_count": 232, "id": "85d55b47", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'commodityExpandedGroup',\n", " 'customsRegime',\n", " 'foreignPort',\n", " 'partnerExpandedGroup',\n", " 'pricePreferred',\n", " 'priceunitPreferred',\n", " 'qPreferredEstimation',\n", " 'quantityPreferred',\n", " 'reporterExpandedGroup',\n", " 'suppression',\n", " 'unitPreferred',\n", " 'usState'}"]}, "execution_count": 232, "metadata": {}, "output_type": "execute_result"}], "source": ["diff_list"]}, {"cell_type": "code", "execution_count": 233, "id": "8654e3bc", "metadata": {}, "outputs": [{"data": {"text/plain": ["12"]}, "execution_count": 233, "metadata": {}, "output_type": "execute_result"}], "source": ["len(diff_list)"]}, {"cell_type": "code", "execution_count": 234, "id": "8068e0ca", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>tradeFlow</th>\n", "      <th>isMirror</th>\n", "      <th>periodType</th>\n", "      <th>year_start</th>\n", "      <th>month_start</th>\n", "      <th>year_end</th>\n", "      <th>month_end</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>reporterCountryNo</th>\n", "      <th>...</th>\n", "      <th>unit1</th>\n", "      <th>quantity1</th>\n", "      <th>q1Estimation</th>\n", "      <th>unit2</th>\n", "      <th>quantity2</th>\n", "      <th>q2Estimation</th>\n", "      <th>priceunit1</th>\n", "      <th>price1</th>\n", "      <th>priceunit2</th>\n", "      <th>price2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>EXPORT</td>\n", "      <td>false</td>\n", "      <td>MONTH</td>\n", "      <td>2025</td>\n", "      <td>3</td>\n", "      <td>2025</td>\n", "      <td>3</td>\n", "      <td>2025</td>\n", "      <td>3</td>\n", "      <td>508</td>\n", "      <td>...</td>\n", "      <td>KG</td>\n", "      <td>198</td>\n", "      <td>0.0</td>\n", "      <td>KG(NET)</td>\n", "      <td>198</td>\n", "      <td>0.0</td>\n", "      <td>USD/KG</td>\n", "      <td>1.0</td>\n", "      <td>USD/KG(NET)</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>EXPORT</td>\n", "      <td>false</td>\n", "      <td>MONTH</td>\n", "      <td>2025</td>\n", "      <td>2</td>\n", "      <td>2025</td>\n", "      <td>2</td>\n", "      <td>2025</td>\n", "      <td>2</td>\n", "      <td>508</td>\n", "      <td>...</td>\n", "      <td>KG</td>\n", "      <td>6</td>\n", "      <td>0.0</td>\n", "      <td>KG(NET)</td>\n", "      <td>6</td>\n", "      <td>0.0</td>\n", "      <td>USD/KG</td>\n", "      <td>11.0</td>\n", "      <td>USD/KG(NET)</td>\n", "      <td>11.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>EXPORT</td>\n", "      <td>false</td>\n", "      <td>MONTH</td>\n", "      <td>2025</td>\n", "      <td>3</td>\n", "      <td>2025</td>\n", "      <td>3</td>\n", "      <td>2025</td>\n", "      <td>3</td>\n", "      <td>508</td>\n", "      <td>...</td>\n", "      <td>KG</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>KG(NET)</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>USD/KG</td>\n", "      <td>54.0</td>\n", "      <td>USD/KG(NET)</td>\n", "      <td>54.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>EXPORT</td>\n", "      <td>false</td>\n", "      <td>MONTH</td>\n", "      <td>2025</td>\n", "      <td>1</td>\n", "      <td>2025</td>\n", "      <td>1</td>\n", "      <td>2025</td>\n", "      <td>1</td>\n", "      <td>508</td>\n", "      <td>...</td>\n", "      <td>KG</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>KG(NET)</td>\n", "      <td>2</td>\n", "      <td>0.0</td>\n", "      <td>USD/KG</td>\n", "      <td>18.0</td>\n", "      <td>USD/KG(NET)</td>\n", "      <td>18.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>EXPORT</td>\n", "      <td>false</td>\n", "      <td>MONTH</td>\n", "      <td>2025</td>\n", "      <td>1</td>\n", "      <td>2025</td>\n", "      <td>1</td>\n", "      <td>2025</td>\n", "      <td>1</td>\n", "      <td>508</td>\n", "      <td>...</td>\n", "      <td>KG</td>\n", "      <td>11037</td>\n", "      <td>0.0</td>\n", "      <td>KG(NET)</td>\n", "      <td>11037</td>\n", "      <td>0.0</td>\n", "      <td>USD/KG</td>\n", "      <td>2.0</td>\n", "      <td>USD/KG(NET)</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 54 columns</p>\n", "</div>"], "text/plain": ["  tradeFlow isMirror periodType  year_start  month_start  year_end  month_end  \\\n", "0    EXPORT    false      MONTH        2025            3      2025          3   \n", "1    EXPORT    false      MONTH        2025            2      2025          2   \n", "2    EXPORT    false      MONTH        2025            3      2025          3   \n", "3    EXPORT    false      MONTH        2025            1      2025          1   \n", "4    EXPORT    false      MONTH        2025            1      2025          1   \n", "\n", "   year  month  reporterCountryNo  ... unit1 quantity1 q1Estimation    unit2  \\\n", "0  2025      3                508  ...    KG       198          0.0  KG(NET)   \n", "1  2025      2                508  ...    KG         6          0.0  KG(NET)   \n", "2  2025      3                508  ...    KG         2          0.0  KG(NET)   \n", "3  2025      1                508  ...    KG         2          0.0  KG(NET)   \n", "4  2025      1                508  ...    KG     11037          0.0  KG(NET)   \n", "\n", "  quantity2 q2Estimation priceunit1 price1   priceunit2  price2  \n", "0       198          0.0     USD/KG    1.0  USD/KG(NET)     1.0  \n", "1         6          0.0     USD/KG   11.0  USD/KG(NET)    11.0  \n", "2         2          0.0     USD/KG   54.0  USD/KG(NET)    54.0  \n", "3         2          0.0     USD/KG   18.0  USD/KG(NET)    18.0  \n", "4     11037          0.0     USD/KG    2.0  USD/KG(NET)     2.0  \n", "\n", "[5 rows x 54 columns]"]}, "execution_count": 234, "metadata": {}, "output_type": "execute_result"}], "source": ["df_report_sample.head()"]}, {"cell_type": "code", "execution_count": 236, "id": "cfdb69f6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>year_start</th>\n", "      <th>month_start</th>\n", "      <th>year_end</th>\n", "      <th>month_end</th>\n", "      <th>year</th>\n", "      <th>month</th>\n", "      <th>reporterCountryNo</th>\n", "      <th>partnerCountryNo</th>\n", "      <th>value</th>\n", "      <th>quantity1</th>\n", "      <th>q1Estimation</th>\n", "      <th>quantity2</th>\n", "      <th>q2Estimation</th>\n", "      <th>price1</th>\n", "      <th>price2</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>2636.0</td>\n", "      <td>2636.000000</td>\n", "      <td>2636.0</td>\n", "      <td>2636.000000</td>\n", "      <td>2636.0</td>\n", "      <td>2636.000000</td>\n", "      <td>2636.0</td>\n", "      <td>2636.000000</td>\n", "      <td>2.636000e+03</td>\n", "      <td>2.636000e+03</td>\n", "      <td>2636.0</td>\n", "      <td>2.636000e+03</td>\n", "      <td>2636.0</td>\n", "      <td>2499.000000</td>\n", "      <td>2157.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>2025.0</td>\n", "      <td>2.037178</td>\n", "      <td>2025.0</td>\n", "      <td>2.037178</td>\n", "      <td>2025.0</td>\n", "      <td>2.037178</td>\n", "      <td>508.0</td>\n", "      <td>431.254173</td>\n", "      <td>1.053963e+06</td>\n", "      <td>2.186278e+06</td>\n", "      <td>0.0</td>\n", "      <td>2.379305e+06</td>\n", "      <td>0.0</td>\n", "      <td>3.509004</td>\n", "      <td>3.732035</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.0</td>\n", "      <td>0.841151</td>\n", "      <td>0.0</td>\n", "      <td>0.841151</td>\n", "      <td>0.0</td>\n", "      <td>0.841151</td>\n", "      <td>0.0</td>\n", "      <td>241.338388</td>\n", "      <td>4.713552e+06</td>\n", "      <td>1.008722e+07</td>\n", "      <td>0.0</td>\n", "      <td>1.140962e+07</td>\n", "      <td>0.0</td>\n", "      <td>7.077704</td>\n", "      <td>7.542186</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>2025.0</td>\n", "      <td>1.000000</td>\n", "      <td>2025.0</td>\n", "      <td>1.000000</td>\n", "      <td>2025.0</td>\n", "      <td>1.000000</td>\n", "      <td>508.0</td>\n", "      <td>1.000000</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>0.000000</td>\n", "      <td>0.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2025.0</td>\n", "      <td>1.000000</td>\n", "      <td>2025.0</td>\n", "      <td>1.000000</td>\n", "      <td>2025.0</td>\n", "      <td>1.000000</td>\n", "      <td>508.0</td>\n", "      <td>268.000000</td>\n", "      <td>4.400000e+01</td>\n", "      <td>2.000000e+01</td>\n", "      <td>0.0</td>\n", "      <td>3.000000e+00</td>\n", "      <td>0.0</td>\n", "      <td>1.000000</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>2025.0</td>\n", "      <td>2.000000</td>\n", "      <td>2025.0</td>\n", "      <td>2.000000</td>\n", "      <td>2025.0</td>\n", "      <td>2.000000</td>\n", "      <td>508.0</td>\n", "      <td>442.000000</td>\n", "      <td>1.066500e+03</td>\n", "      <td>3.000000e+02</td>\n", "      <td>0.0</td>\n", "      <td>3.955000e+02</td>\n", "      <td>0.0</td>\n", "      <td>2.000000</td>\n", "      <td>2.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>2025.0</td>\n", "      <td>3.000000</td>\n", "      <td>2025.0</td>\n", "      <td>3.000000</td>\n", "      <td>2025.0</td>\n", "      <td>3.000000</td>\n", "      <td>508.0</td>\n", "      <td>612.000000</td>\n", "      <td>6.300575e+04</td>\n", "      <td>3.965200e+04</td>\n", "      <td>0.0</td>\n", "      <td>4.147900e+04</td>\n", "      <td>0.0</td>\n", "      <td>3.000000</td>\n", "      <td>3.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>2025.0</td>\n", "      <td>3.000000</td>\n", "      <td>2025.0</td>\n", "      <td>3.000000</td>\n", "      <td>2025.0</td>\n", "      <td>3.000000</td>\n", "      <td>508.0</td>\n", "      <td>837.000000</td>\n", "      <td>8.005393e+07</td>\n", "      <td>1.695155e+08</td>\n", "      <td>0.0</td>\n", "      <td>1.695150e+08</td>\n", "      <td>0.0</td>\n", "      <td>138.000000</td>\n", "      <td>138.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       year_start  month_start  year_end    month_end    year        month  \\\n", "count      2636.0  2636.000000    2636.0  2636.000000  2636.0  2636.000000   \n", "mean       2025.0     2.037178    2025.0     2.037178  2025.0     2.037178   \n", "std           0.0     0.841151       0.0     0.841151     0.0     0.841151   \n", "min        2025.0     1.000000    2025.0     1.000000  2025.0     1.000000   \n", "25%        2025.0     1.000000    2025.0     1.000000  2025.0     1.000000   \n", "50%        2025.0     2.000000    2025.0     2.000000  2025.0     2.000000   \n", "75%        2025.0     3.000000    2025.0     3.000000  2025.0     3.000000   \n", "max        2025.0     3.000000    2025.0     3.000000  2025.0     3.000000   \n", "\n", "       reporterCountryNo  partnerCountryNo         value     quantity1  \\\n", "count             2636.0       2636.000000  2.636000e+03  2.636000e+03   \n", "mean               508.0        431.254173  1.053963e+06  2.186278e+06   \n", "std                  0.0        241.338388  4.713552e+06  1.008722e+07   \n", "min                508.0          1.000000  0.000000e+00  0.000000e+00   \n", "25%                508.0        268.000000  4.400000e+01  2.000000e+01   \n", "50%                508.0        442.000000  1.066500e+03  3.000000e+02   \n", "75%                508.0        612.000000  6.300575e+04  3.965200e+04   \n", "max                508.0        837.000000  8.005393e+07  1.695155e+08   \n", "\n", "       q1Estimation     quantity2  q2Estimation       price1       price2  \n", "count        2636.0  2.636000e+03        2636.0  2499.000000  2157.000000  \n", "mean            0.0  2.379305e+06           0.0     3.509004     3.732035  \n", "std             0.0  1.140962e+07           0.0     7.077704     7.542186  \n", "min             0.0  0.000000e+00           0.0     0.000000     0.000000  \n", "25%             0.0  3.000000e+00           0.0     1.000000     1.000000  \n", "50%             0.0  3.955000e+02           0.0     2.000000     2.000000  \n", "75%             0.0  4.147900e+04           0.0     3.000000     3.000000  \n", "max             0.0  1.695150e+08           0.0   138.000000   138.000000  "]}, "execution_count": 236, "metadata": {}, "output_type": "execute_result"}], "source": ["df_report_sample.describe()"]}, {"cell_type": "code", "execution_count": 199, "id": "297aea67", "metadata": {}, "outputs": [{"data": {"text/plain": ["['Trade flow',\n", " 'Is Mirror',\n", " 'Period Type',\n", " 'Start Year',\n", " 'Start Month',\n", " 'End Year',\n", " 'End Month',\n", " 'Year',\n", " 'Month',\n", " 'Reporter Country No',\n", " 'Reporter Code',\n", " 'Reporter ISO Alpha-3',\n", " 'Reporter ISO Numeric-3',\n", " 'Reporter Name',\n", " 'Reporter Description',\n", " 'Expanded Reporter Country Group',\n", " 'First Available Period',\n", " 'Last Available Period',\n", " 'Reporter Source',\n", " 'Incoterm',\n", " 'Partner Country No',\n", " 'Partner Code',\n", " 'Partner ISO Alpha-3',\n", " 'Partner ISO Numeric-3',\n", " 'Partner Name',\n", " 'Expanded Partner Country Group',\n", " 'HS Code',\n", " 'Commodity Description',\n", " 'HS2 Code',\n", " 'HS2 Code Description',\n", " 'HS4 Code',\n", " 'HS4 Code Description',\n", " 'HS6 Code',\n", " 'HS6 Code Description',\n", " 'Expanded Commodity Group',\n", " 'Exploded Commodity HS Code',\n", " 'Exploded Commodity Description',\n", " 'Subdivision',\n", " 'Port',\n", " 'Transport',\n", " 'Foreign Port',\n", " 'US State',\n", " 'Customs Regime',\n", " 'suppression',\n", " 'Currency',\n", " 'Value',\n", " 'Unit1',\n", " 'Quantity1',\n", " 'Q1 Estimation, %',\n", " 'Unit2',\n", " 'Quantity2',\n", " 'Q2 Estimation, %',\n", " 'Preferred Unit',\n", " 'Preferred Quantity',\n", " 'Preferred Quantity Estimation, %',\n", " 'Price Unit1',\n", " 'Price1',\n", " 'Price Unit2',\n", " 'Price2',\n", " 'Price Preferred Unit',\n", " 'Price Preferred Quantity']"]}, "execution_count": 199, "metadata": {}, "output_type": "execute_result"}], "source": ["field_names"]}, {"cell_type": "code", "execution_count": null, "id": "0f7fe2d7", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}