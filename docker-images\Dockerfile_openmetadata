FROM ubuntu:jammy-20240405
RUN apt-get update && \
    apt-get install -y \
    vim \
    curl \
    traceroute \
    inetutils-tools \
    python3 \
    python3-pip
RUN pip3 install "openmetadata-ingestion[airflow]"==1.3.1
RUN pip3 install "openmetadata-ingestion[airflow-container]"==1.3.1
RUN pip3 install "openmetadata-ingestion[postgres]"==1.3.1
RUN pip3 install "openmetadata-ingestion[trino-container]"==1.3.1
RUN pip3 install "openmetadata-ingestion[trino]"==1.3.1
RUN mkdir /home/<USER>
COPY docker-images/Data-catalog/openmetadata /home/<USER>
ENV TZ="America/New_York"
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone
RUN apt-get install -y tzdata && rm -rf /var/lib/apt/lists/*
