from utils.snowflake.bulk_loader import SnowflakeBulkLoader
import time
import json
from datetime import datetime
from collections import defaultdict
import pandas as pd
import polars as pl
from pathlib import Path
from typing import Optional
import re
import os
import logging
import os
import uuid

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

_KPLER_AVAILABILITY_UNITS_RAW = "/jfs/tech1/apps/rawdata/kpler_power/availability_fueltype_yearly/"
_KPLER_AVAILABILITY_UNITS_PROCESSED = "/jfs/tech1/apps/rawdata/kpler_power/availability_fueltype_yearly_processed_jc/"

_BAD_JSON_REGEX = []

def get_monthly_bins(as_of_dates: list[str]) -> dict[str, list[str]]:
    monthly_bins = defaultdict(list)

    for ts in as_of_dates:
        dt = datetime.strptime(ts, "%Y-%m-%d %H:%M:%S")
        month_key = dt.strftime("%Y_%m")
        monthly_bins[month_key].append(ts)
    
    return monthly_bins

def extract_data_for_as_of_ts(as_of_ts: str, raw_data_dir: str):
    as_of_ts_path = os.path.join(raw_data_dir, as_of_ts)
    files_cnt = 0
    df_list = []
    skip_file = False
    for json_path in Path(as_of_ts_path).rglob('*.json'):
        for regex in _BAD_JSON_REGEX:
            skip_file = False
            if re.match(regex, str(json_path)):
                skip_file = True
                break
        
        if skip_file:
            logger.warning(f"Skipping bad JSON file: {json_path}")
            continue
        
        logger.debug(f"Processing file: {json_path}")
        flattened_json = flatten_json(json_path)

        if flattened_json is not None:
            logger.debug(f"Flattened JSON Data for {json_path}")
            df_list.append(flattened_json)
        else:
            logger.warning(f"No data found in {json_path}")
            
        files_cnt += 1

    data_result = None
    if df_list:
        data_result = pl.concat(df_list)
    return files_cnt, data_result

def examine_json(data: dict):
    json_keys = data.keys()

    expected_elements = {
        "provider": str,
        "location": str,
        "timezone": str,
        "data": dict,
        "index": list,
    }

    expected_data_keys = ["low", "central", "high"]

    missing_keys = list(set(expected_elements.keys()) - set(json_keys))
    extra_keys = list(set(json_keys) - set(expected_elements.keys()))

    mismatched_data_types = []

    if "index" in json_keys:
        timestamps = data["index"]
        if type(timestamps) == list and not timestamps:
            return [], [], [], True
    
    for key, expected_type in expected_elements.items():
        if key in json_keys:
            if type(data[key]) != expected_type:
                mismatched_data_types.append(key)
            if key == "data":
                data_val = data[key]
                data_val_keys = data_val.keys()
                lst_data_val_keys = list(data_val_keys)
                missing_data_keys = set(expected_data_keys) - set(lst_data_val_keys)
                
                if len(missing_data_keys) > 0:
                    for missing_key in missing_data_keys:
                        missing_keys.append(f"{key}.{missing_key}")
                extra_data_keys = set(lst_data_val_keys) - set(expected_data_keys)

                if len(extra_data_keys) > 0:
                    for extra_key in extra_data_keys:
                        extra_keys.append(f"{key}.{extra_key}")
            
    return list(missing_keys), list(extra_keys), mismatched_data_types, False

def extract_as_of_date(filename):
    timestamp_str = filename.split('_')[0].replace('\\:', ':')
    return datetime.fromisoformat(timestamp_str)

def flatten_json(file_path) -> list[dict]:
    with open(file_path, 'r') as f:
        full_dict = json.load(f)

    full_df = []

    (missing_keys, extra_keys, mismatched_types, no_data) = examine_json(full_dict)
    if len(missing_keys) > 0 or len(extra_keys) > 0 or len(mismatched_types) > 0:
        logger.warning(f"Unexpected file format: {file_path}. Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_types}")
        return None
    elif no_data:
        logger.warning(f"No data:{file_path}")
        return None
    else:
        logger.debug(f"Expected format: {file_path}")
    
    file_name = os.path.basename(file_path)
    as_of_date = extract_as_of_date(file_name)

    provider = full_dict['provider']
    location = full_dict['location']
    timezone = full_dict['timezone']

    timestamp = full_dict["index"]

    for level, prod_data in full_dict["data"].items():
        if len(prod_data) > 0:
            for fuel_types, avail_amt in prod_data.items():
                assert len(avail_amt) == len(timestamp), f"Length of index dates and production data are not equal for {level} in {file_name}"
                df = pl.DataFrame({
                    "timestamp": timestamp,
                    "availability_amount": avail_amt
                })
                df = df.with_columns(
                    timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                    as_of = pl.lit(as_of_date).dt.cast_time_unit('ms'),
                    provider = pl.lit(provider),
                    country = pl.lit(location),
                    timezone = pl.lit(timezone),
                    level = pl.lit(level),
                    fuel_type = pl.lit(fuel_types),
                    json_file = pl.lit(file_name),
                )
                df = df.with_columns(
                    timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                    as_of = pl.col('as_of').dt.replace_time_zone("UTC")
                )
                full_df.append(df)
        else:
            logger.warning(f"No data found for {level} in {file_path}")

    if full_df:
        return pl.concat(full_df, how="vertical_relaxed")
    else:
        return None


if __name__  ==  '__main__':
    with open(f"{_KPLER_AVAILABILITY_UNITS_PROCESSED}availability_fueltype_yearly.txt", "r") as file:
        as_of_dates = [line.strip().replace("availability_fueltype_yearly/", "") for line in file]
    
    monthly_bins = get_monthly_bins(as_of_dates)
    running_total = 0
    database = 'KPLER'
    schema_name = 'KPLER_POWER'
    target_table = 'AVAILABILITY_FUEL_TYPE'
    stage_name = 'POWER_AVAILABILITY'
    stage_path = f"FUEL_TYPE/{uuid.uuid4().hex}"
    monthly_bins = get_monthly_bins(as_of_dates)
    total_files = 0
    sf_adaptor = SnowflakeBulkLoader(
        database=database, 
        schema=schema_name,
        warehouse="BBG_DLPLUS_WH", 
        role="FR_DATA_PLATFORM")
    for month_key, month_values in monthly_bins.items():
        
        # if month_key[:4] != "2024":
        #     continue

        for as_of_ts in month_values:
            parquet_file = f"{_KPLER_AVAILABILITY_UNITS_PROCESSED}{as_of_ts}.parquet"
            if os.path.exists(parquet_file):
                logger.info(f"File already exists: {parquet_file}, recreating...")
                #sf_adaptor.put_file(parquet_file, stage_name, stage_path=stage_path)
                
            
            all_dfs = []
            start_time = time.time()
            (files_for_as_of, as_of_ts_data) = extract_data_for_as_of_ts(as_of_ts, _KPLER_AVAILABILITY_UNITS_RAW)
            end_time = time.time()

            logger.info(f"Time taken to read JSONs {as_of_ts}: {end_time - start_time:.4f} secs, Number of files: {files_for_as_of}")

            if as_of_ts_data is not None:
                file_path = f"{_KPLER_AVAILABILITY_UNITS_PROCESSED}{as_of_ts}.parquet"
                as_of_ts_data.write_parquet(file_path)
                
                # Upload file to stage
                sf_adaptor.put_file(file_path, stage_name, stage_path=stage_path)
            running_total += files_for_as_of
    
    sf_adaptor.load_generic(
                    target_table, 
                    stage_name,
                    stage_path,
                    "(TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE'"
                )
    sf_adaptor.cleanup_stg(stage_name, stage_path)
    logger.info(f"Total files processed: {running_total}")
    
    # json_path = os.path.join(os.path.dirname(__file__), "tests", "2022-01-01 10:00:00_eex_DE__2021-12.json")
    # flatten_json(json_path)

            
    
    