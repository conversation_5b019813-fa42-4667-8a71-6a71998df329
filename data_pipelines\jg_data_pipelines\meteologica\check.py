import os, random
import tempfile
import pandas as pd
import json
from glob import glob
from zipfile import ZipFile 
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor


raw_path = '/jfs/tech1_share/samuel.lasker/meteo_results_2'
sample = 10
if __name__ == "__main__":
    adaptor = SnowparkAdaptor(
        database="METEOLOGICA", 
        schema="API",
        warehouse="BBG_DLPLUS_WH", 
        role="FR_DATA_PLATFORM"
    )
    ref = adaptor.session.table("meteo_params_ref")
    normal_sf = adaptor.session.table("NORMAL")
    observation_sf = adaptor.session.table("OBSERVATION")
    reanalysis_df = adaptor.session.table("REANALYSIS")
    forecast_deterministic_sf = adaptor.session.table("FORECAST_DETERMINISTIC")
    forecast_ensemble_sf = adaptor.session.table("FORECAST_ENSEMBLE")
    forecast_extended_sf = adaptor.session.table("FORECAST_EXTENDED")


    missmatches = []
    files = os.listdir(raw_path)
    with tempfile.TemporaryDirectory() as tmpdirname:
        for i in range(sample):
            c = random.choice(files)
            source_file = os.path.join(raw_path, c)
            result = f"{tmpdirname}/{c.replace('.zip', '')}"
            with ZipFile(source_file, 'r') as zObject:   
                zObject.extractall( 
                    path=result) 
                json_df = pd.concat([pd.read_json(f_name) for f_name in glob(f'{result}/*.json')])
                if "update_id" in json_df.columns:
                    json_df["update_id"] = json_df["update_id"].astype("string")
            sdf = adaptor.session.create_dataframe(json_df)
            base = sdf.join(ref, sdf['"content_id"'] == ref.content_id)

            base = base.select_expr("*", 
                '''
                TO_TIMESTAMP_NTZ("issue_date", 'YYYY-MM-DD HH24:MI:SS UTC') issue_date,
                to_timestamp_ntz(convert_timezone(
                    'UTC', 
                    TO_TIMESTAMP_TZ(
                        to_varchar("data":"From yyyy-mm-dd hh:mm") || ' ' || split(to_varchar("data":"UTC offset from (UTC+/-hhmm)"), 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
                )) as from_timestamp
                ''',
                '''
                to_timestamp_ntz(convert_timezone(
                    'UTC', 
                    TO_TIMESTAMP_TZ(
                        to_varchar("data":"To yyyy-mm-dd hh:mm") || ' ' || split(to_varchar("data":"UTC offset to (UTC+/-hhmm)"), 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
                )) as to_timestamp
                '''
                )

            dtype = base.select_expr('data_type').limit(1).collect()[0][0]
            ftype = base.select_expr('FORECAST_TYPE').limit(1).collect()[0][0]
            fs = base.select_expr('FORECAST_SOURCE').limit(1).collect()[0][0]
            if fs == 'ECMWF-ENSEXT':
                ftype = 'extended'
            target_table = f'{dtype}_{ftype}'

            print(f"Processing {target_table} file {raw_path}/{c}...")

            normal = base.where(base['data_type'] == 'Normal')
            observation = base.where(base['data_type'] == 'Observation')
            reanalysis = base.where((base['data_type'] == 'Reanalysis') & 
                (base['FORECAST_TYPE'] == 'Deterministic'))
            forecast_deterministic = base.where((base['data_type'] == 'Forecast') & 
                (base['FORECAST_TYPE'] == 'Deterministic'))
            forecast_ensemble = base.where((base['data_type'] == 'Forecast') & 
                (base['FORECAST_TYPE'] == 'Ensemble') & (base['FORECAST_SOURCE'] != 'ECMWF-ENSEXT'))
            forecast_extended = base.where((base['data_type'] == 'Forecast') & 
                (base['FORECAST_TYPE'] == 'Ensemble') & (base['FORECAST_SOURCE'] == 'ECMWF-ENSEXT'))


            if normal.count() > 0:
                local = normal
                remote = normal_sf
            elif observation.count() > 0:
                local = observation
                remote = observation_sf
            elif reanalysis.count() > 0:
                local = reanalysis
                remote = reanalysis_df
            elif forecast_deterministic.count() > 0:
                local = forecast_deterministic
                remote = forecast_deterministic_sf
            elif forecast_ensemble.count() > 0:
                local = forecast_ensemble
                remote = forecast_ensemble_sf
            elif forecast_extended.count() > 0:
                local = forecast_extended
                remote = forecast_extended_sf
            local_count = local.count()
            matches = local.join(remote, 
                (local['"content_id"'] == remote['content_id']) &
                (local['"content_name"'] == remote['content_name']) &
                (local['"unit"'] == remote['unit']) &
                (local['issue_date'] == remote['issue_date']) &
                (local['FROM_TIMESTAMP'] == remote['from_datetime']) &
                (local['TO_TIMESTAMP'] == remote['to_datetime'])
            ).count()

            if local_count != matches:
                print(f"File {target_table} {raw_path}/{c} doesn't match counts: {local_count}(raw) != {matches}(Snowflake match)")
                missmatches.append(c)
            else:
                print(f"File {target_table} {raw_path}/{c} OK")
    
    if missmatches:
        print(f"File mismatches: {missmatches}")
    with open("missmatches.json", "w") as fp:
        json.dump(missmatches, fp)
