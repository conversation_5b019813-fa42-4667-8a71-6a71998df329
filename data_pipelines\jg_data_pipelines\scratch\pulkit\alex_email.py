import json
import time
 
from confluent_kafka import Producer
 
_conf = {
    "bootstrap.servers": "kafka1.jainglobal.net:9092,kafka2.jainglobal.net:9092",
}
_topic = "fo_tools_alerts"
# "message.max.bytes": 15 * 1024 * 1024; manish
# "max.request.size": 10485760  # Set max message size (10 MB); chatgpt
 
def delivery_report(err, msg):
    if err is not None:
        print(f"Message delivery failed: {err}")
    else:
        print(
            f"Message delivered to {msg.topic()} [{msg.partition()}] at offset {msg.offset()}"
        )
 
def send_email(
    to_recipient, subject, body, cc_recipient=[], from_=None, attachment=None
):
    producer = Producer(_conf)
 
    if attachment:
        raise NotImplementedError("Attachment not supported yet!")
 
    email_param = {
        "to": to_recipient,
        "cc": cc_recipient,
        "subject": subject,
        "from_": "<EMAIL>" if from_ is None else from_,
        "body": "" if body is None else body,
        "attachment": attachment,
        "default_footer": False,
    }
    timestamp = time.strftime("%Y%m%d%H%M%S")
    record_key = f"eqvol_{timestamp}"
    producer.produce(
        _topic, key=record_key, value=json.dumps(email_param), callback=delivery_report
    )
    producer.flush()
 
 
if __name__ == "__main__":
    send_email(
        ["<EMAIL>"],
        "test msg",
        "This is a test message.\nLet's see if it arrives.\n",
    )
 
 
