import os
import numpy as np
import pandas as pd
import time
from datetime import datetime
import pytz

from utils.postgres.adaptor import PostgresAdaptor
import bloomberg.bpipe as bpipe_helper

from sqlalchemy import (
    create_engine,
    MetaData,
    Table,
    Column,
    Integer,
    String,
    Date,
    Float,
    Sequence,
    SmallInteger,
)
from sqlalchemy.dialects.postgresql import insert

def _snap_bbg_stock_quotes(dict_securities_bbg):
    bpipe_instance = ("Tech1ProdBPipe47537.jainglobal.net", 8194)
    bpipe_app = "JAIN:pmdashboard-bps"
    bpipe_handle = bpipe_helper.BpipeHandler(bpipe_instance, bpipe_app)
    api_req_time = datetime.now(pytz.utc).replace(tzinfo=None)
    df_price = bpipe_handle.fetch_live(
        tickers = dict_securities_bbg.keys(),
        fields=["BID_ALL_SESSION", "ASK_ALL_SESSION", "PX_LAST", "BID_SIZE_ALL_SESSIONS_RT", "ASK_SIZE_ALL_SESSIONS_RT", "LAST_UPDATE_BID_RT", "LAST_UPDATE_ASK_RT"], 
    )
    df_price.reset_index(inplace=True, names="bbg_full_ticker")
    ref_date = api_req_time.date()
    df_price['BID_SIZE_ALL_SESSIONS_RT'] = df_price['BID_SIZE_ALL_SESSIONS_RT'].where(pd.notna(df_price['BID_SIZE_ALL_SESSIONS_RT']), None).astype('Int64')
    df_price['ASK_SIZE_ALL_SESSIONS_RT'] = df_price['ASK_SIZE_ALL_SESSIONS_RT'].where(pd.notna(df_price['ASK_SIZE_ALL_SESSIONS_RT']), None).astype('Int64')
    df_price["JG_API_REQ_TIMESTAMP"] = api_req_time
    df_price["JG_API_REQ_TIMESTAMP_DATE"] = ref_date
    df_price["LAST_UPDATE_BID_RT"] = df_price["LAST_UPDATE_BID_RT"].astype(str)
    df_price["LAST_UPDATE_ASK_RT"] = df_price["LAST_UPDATE_ASK_RT"].astype(str)
    df_price["security_code_id"] = df_price["bbg_full_ticker"].map(dict_securities_bbg)
    df_price.columns = df_price.columns.str.lower()

    return df_price, api_req_time.strftime('%Y%m%d%H%M%S'), df_price.shape[0]


if __name__ == "__main__":
    loader = PostgresAdaptor(
        # host="tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com",
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df_securities = loader.execute_query(
        "select security_code_id, security_code, bbg_full_ticker from eqvol.sr_security_ref where security_code not in ('NDXP', 'RUTW', 'SPXW');"
    )
    
    dict_securities_bbg = dict(
        zip(df_securities["bbg_full_ticker"], df_securities["security_code_id"])
    )

    start_time = time.time()
    (df_bbg_price, bbg_price_req_time, nos_of_stock_quotes_bbg) = _snap_bbg_stock_quotes(dict_securities_bbg)
    end_time = time.time()
    execution_time = end_time - start_time
    print(
        f"Time taken to snap Bbg stock quotes: {execution_time:.4f} secs, Number of rows: {nos_of_stock_quotes_bbg}"
    )

    df_bbg_price = df_bbg_price[["security_code_id", "bid_all_session", "bid_size_all_sessions_rt", "ask_all_session", "ask_size_all_sessions_rt", "px_last", "last_update_bid_rt", "last_update_ask_rt", "jg_api_req_timestamp", "jg_api_req_timestamp_date"]]

    print(df_bbg_price.shape)
    print(df_bbg_price.dtypes)

    success = loader.load_dataframe(
        df=df_bbg_price,
        table_name="bbg_stock_quotes",
        if_exists="append",
    )

    if success:
        print("Stock Quotes loaded successfully.")
    else:
        raise ValueError("Stock Quotes loading failed!")