# .github/workflows/deploy-prod.yml
name: Manual Deploy to Remote Server

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to deploy'
        required: true
        default: 'main'
      tag:
        description: 'Tag to deploy (overrides branch if set)'
        required: false

jobs:
  checkout:
    name: Checkout Code
    runs-on: ubuntu-latest
    outputs:
      sha: ${{ steps.get-sha.outputs.sha }}

    steps:
      - name: Checkout branch or tag
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.tag || github.event.inputs.branch }}

      - name: Get current commit SHA
        id: get-sha
        run: echo "sha=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT

      - name: Print commit info
        run: |
          echo "📦 Deploying from commit:"
          git log -1 --pretty=format:"%h - %s by %an (%ae) at %cd"

  deploy:
    name: Deploy to Remote Server
    needs: checkout
    runs-on: ubuntu-latest

    steps:
      - name: Checkout same commit again for deploy
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.tag || github.event.inputs.branch }}

      - name: Set up SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY_JFS }}

      - name: Deploy via rsync
        run: |
          echo "🚀 Deploying to remote server..."
          rsync -avz --delete ./ jsvc-datait@10.116.8.75:/jfs/tech1/apps/datait/nitish/github
