import logging
import os, sys,json
current_file_dir = os.getcwd()
sys.path.append(os.path.abspath(current_file_dir))
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

configPath = os.environ.get('CONFIG_PATH', os.getcwd())
with open(f'{configPath}/config.json', 'r') as f:
    config = json.load(f)

def jg_config_path():
    return config["JG_CONFIG_PATH"]

def read_config_secrets():
    config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret
objconfig = read_config_secrets()

os.environ['SF_USERNAME'] = objconfig['sf_user']
os.environ['SF_PASSWORD'] = objconfig['sf_password']
os.environ['SF_DATABASE'] = objconfig['factset_database']
os.environ['SF_WAREHOUSE'] = objconfig['factset_warehouse']
os.environ['SF_ROLE'] = objconfig['factset_role']
os.environ['FACTSET_FTP_PASSWORD'] = objconfig['factset_ftp_password']

from factset.factset_utils import sync_dir
from factset.factset_configs import *

if __name__ == "__main__":
   
    # FUNDAMENTALS
    
    logger.info("Syncing Factset Fundamentals Basic dir")
    sync_dir(FACTSET_BASIC_DIR, JFS_FACTSET_BASIC_DIR, FilenamePattern.FUNDAMENTALS_BASIC)
    
    logger.info("Syncing Factset Fundamentals Basic Derived dir")
    sync_dir(FACTSET_BASIC_DIR, JFS_FACTSET_BASIC_DER_DIR, FilenamePattern.FUNDAMENTALS_BASIC_DERIVED)
    
    logger.info("Syncing Factset Fundamentals Advanced dir")
    sync_dir(FACTSET_ADVANCED_DIR, JFS_FACTSET_ADVANCED_DIR, FilenamePattern.FUNDAMENTALS_ADVANCED)
    
    logger.info("Syncing Factset Fundamentals Advanced Dervied dir")
    sync_dir(FACTSET_ADVANCED_DIR, JFS_FACTSET_ADVANCED_DER_DIR, FilenamePattern.FUNDAMENTALS_ADVANCED_DERIVED)
    
    logger.info("Syncing Factset Fundamentals SEC-HUB dir")
    sync_dir(FUNDAMENTAL_SEC_HUB_DIR, JFS_FUNDAMENTAL_SEC_HUB_DIR, FilenamePattern.FUNDAMENTAL_SEC_HUB)
    
    
    # SYMBOLOGY
    
    logger.info("Syncing Factset Symbology BBG dir")  
    sync_dir(FACTSET_SYM_BBG, JFS_FACTSET_SYM_BBG, FilenamePattern.SYMBOLOGY)
    
    # ESTIMATES
    
    logger.info("Syncing Factset Estimates Advanced CON SAF dir")
    sync_dir(ESTIMATES_ADVANCED_CON_DIR, JFS_ESTIMATES_ADVANCED_CON_DIR, FilenamePattern.ESTIMATES_SAF_ADVANCED)
    
    logger.info("Syncing Factset Estimates Advanced CON AF dir")
    sync_dir(ESTIMATES_ADVANCED_CON_DIR, JFS_ESTIMATES_ADVANCED_CON_DIR, FilenamePattern.ESTIMATES_AF_ADVANCED)
    
    logger.info("Syncing Factset Estimates Advanced CON QF dir")
    sync_dir(ESTIMATES_ADVANCED_CON_DIR, JFS_ESTIMATES_ADVANCED_CON_DIR, FilenamePattern.ESTIMATES_QF_ADVANCED)
       
    logger.info("Syncing Factset Estimates Basic Con dir")
    sync_dir(ESTIMATES_BASIC_CON_DIR, JFS_ESTIMATES_BASIC_CON_DIR, FilenamePattern.ESTIMATES_BASIC_CON)
    
    logger.info("Syncing Factset Estimates Basic ACT dir")
    sync_dir(ESTIMATES_BASIC_ACT_DIR, JFS_ESTIMATES_BASIC_ACT_DIR, FilenamePattern.ESTIMATES_BASIC_ACT)
    
    logger.info("Syncing Factset Estimates Basic GUID dir")
    sync_dir(ESTIMATES_BASIC_GUID_DIR, JFS_ESTIMATES_BASIC_GUID_DIR, FilenamePattern.ESTIMATES_BASIC_GUID)
    
    logger.info("Syncing Factset Estimates Advanced ACT dir")
    sync_dir(ESTIMATES_ADVANCED_ACT_DIR, JFS_ESTIMATES_ADVANCED_ACT_DIR, FilenamePattern.ESTIMATES_ADVANCED_ACT)
    
    logger.info("Syncing Factset Estimates Advanced GUID dir")
    sync_dir(ESTIMATES_ADVANCED_GUID_DIR, JFS_ESTIMATES_ADVANCED_GUID_DIR, FilenamePattern.ESTIMATES_ADVANCED_GUID)
    
    logger.info("Syncing Factset Estimates SEC-HUB dir")
    sync_dir(ESTIMATES_SEC_HUB_DIR, JFS_ESTIMATES_SEC_HUB_DIR, FilenamePattern.ESTIMATES_SEC_HUB)

    logger.info("Symbology Downloads Sedol/Coverage/TickerRegion")
    sync_dir(FACTSET_SYMBOL_SEDOL_HIST,JFS_FACTSET_SYMBOL_SEDOL_HIST,FilenamePattern.ALL)
    sync_dir(FACTSET_SYMBOL_TICKER_REGION_HIST,JFS_FACTSET_SYMBOL_TICKER_REGION_HIST,FilenamePattern.ALL)
    sync_dir(FACTSET_SYM_COVERAGE,JFS_FACTSET_SYM_COVERAGE,FilenamePattern.ALL)
