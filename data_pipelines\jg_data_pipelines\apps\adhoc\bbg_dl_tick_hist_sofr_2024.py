from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.snowflake.adaptor import SnowflakeAdaptor

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )
    df_future_requests = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """select 'sofrfut' AS JG_FUT_CATEGORY, 
                YR - 2000 AS YR, 
                MON, 
                TO_CHAR(MONTH_START_DATE, 'YYYYMMDD') as MONTH_START_DATE,
                TO_CHAR(MONTH_END_DATE, 'YYYYMMDD') as MONTH_END_DATE,
                FULL_TICKERS_COMMA_SEP
            from MD_FUTURE_SOFR_ON_THE_RUN
            ORDER BY YR DESC, 
                MON DESC, 
                JG_FUT_CATEGORY """,
    )
    print(df_future_requests)
    for index, row in df_future_requests.iterrows():
        batch_name = f"dp_{row['JG_FUT_CATEGORY']}_{row['YR']}_{row['MON']}_ask"
        print(f"Processing {batch_name}")
        securities = row['FULL_TICKERS_COMMA_SEP'].split(',')
        date_range = f"{row['MONTH_START_DATE']}|{row['MONTH_END_DATE']}"
        per_sec_req_type = PerSecurityRequestType.gettickhistory
        request_dict = {
            "firm_name": "dl47544",
            "program_flag": "adhoc",
            "date_range":date_range,
            "sec_id": "TICKER",
            "content_type": "ohlc",
            "bar_interval": "1m",
            "event_type": "ASK",
            "securities": securities,
            "fields": [],
        }
        target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_sofr_1min_bars/"
        PerSecurityRequestRunner(
            batch_name, per_sec_req_type, request_dict, target_folder
        ).run()