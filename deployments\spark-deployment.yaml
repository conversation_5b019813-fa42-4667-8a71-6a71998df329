apiVersion: apps/v1
kind: Deployment
metadata:
  name: spark-master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: spark
  template:
    metadata:
      labels:
        app: spark
    spec:
      containers:
        - name: spark-master
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-spark-${ENV}:latest
          securityContext:
            privileged: true
          env:
            - name: JAVA_TOOL_OPTIONS
              value: "--add-exports=java.base/sun.nio.ch=ALL-UNNAMED"
            - name: SPARK_MODE
              value: "master"
            - name: SPARK_RPC_AUTHENTICATION_ENABLED
              value: "no"
            - name: SPARK_RPC_ENCRYPTION_ENABLED
              value: "no"
            - name: SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED
              value: "no"
            - name: SPARK_SSL_ENABLED
              value: "no"
          ports:
            - containerPort: 8080
            - containerPort: 7077
          volumeMounts:
            - name: shared-folder
              mountPath: /home/<USER>/spark/app
            - name: logs-data
              mountPath: /home/<USER>/spark/logs
            - name: data-platform-source
              mountPath: /home/<USER>/src
      volumes:
        - name: shared-folder
          persistentVolumeClaim:
            claimName: common-shared-folder
        - name: logs-data
          persistentVolumeClaim:
            claimName: common-logs-data
        - name: data-platform-source
          persistentVolumeClaim:
            claimName: common-resources-data


---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: spark-worker
spec:
  replicas: 2
  selector:
    matchLabels:
      app: spark
  template:
    metadata:
      labels:
        app: spark
    spec:
      containers:
        - name: spark-worker
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-spark-${ENV}:latest
          securityContext:
            privileged: true
          env:
            - name: JAVA_TOOL_OPTIONS
              value: "--add-exports=java.base/sun.nio.ch=ALL-UNNAMED"
            - name: SPARK_MODE
              value: "worker"
            - name: SPARK_MASTER_URL
              value: "spark://spark:7077"
            - name: SPARK_WORKER_MEMORY
              value: "10G"
            - name: SPARK_WORKER_CORES
              value: "7"
            - name: SPARK_RPC_AUTHENTICATION_ENABLED
              value: "no"
            - name: SPARK_RPC_ENCRYPTION_ENABLED
              value: "no"
            - name: SPARK_LOCAL_STORAGE_ENCRYPTION_ENABLED
              value: "no"
            - name: SPARK_SSL_ENABLED
              value: "no"
          volumeMounts:
            - name: shared-folder
              mountPath: /home/<USER>/spark/app
            - name: logs-data
              mountPath: /home/<USER>/spark/logs
      volumes:
        - name: shared-folder
          persistentVolumeClaim:
            claimName: common-shared-folder
        - name: logs-data
          persistentVolumeClaim:
            claimName: common-logs-data


---

apiVersion: v1
kind: Service
metadata:
  name: spark
spec:
  selector:
    app: spark
  ports:
    - name: spark-ui
      protocol: TCP
      port: 8081
      targetPort: 8080
    - name: spark-master
      protocol: TCP
      port: 7077
      targetPort: 7077
  type: NodePort
  externalTrafficPolicy: Local 

---

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "spark-master-ingress"
  annotations:
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/certificate-arn: ${SSL_CERTIFICATE_ARN}
  labels:
    app: spark-nginx-ingress
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: "spark"
              port:
                number: 8081