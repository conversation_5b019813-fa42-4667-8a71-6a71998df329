raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/spacinsider"  ## Location of Raw Files
  s3_prefix: "spacinsider" ## Internal S3path to files

  structure: '[
    "spacinsider_*_$DATE$.json"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "spacinsider"

  table_map:
    SPACINSIDER_LEAGUES_PIPE_INVESTOR_RAW:
      pattern: "spacinsider_leagues_pipe-investor_direct_$DATE$.json"
      col_num: 1
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "spacinsider/" 
      file_format: "FF_SPACINSIDER"

    SPACINSIDER_LEAGUES_SERIAL_SPONSOR_RAW:
      pattern: "spacinsider_leagues_serial-sponsor_direct_$DATE$.json"
      col_num: 1
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "spacinsider/" 
      file_format: "FF_SPACINSIDER"

    SPACINSIDER_LEAGUES_PRIVATE_PLACEMENT_RAW:
      pattern: " spacinsider_leagues_private-placement_direct_$DATE$.json"
      col_num: 1
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "spacinsider/" 
      file_format: "FF_SPACINSIDER"

    SPACINSIDER_LEAGUES_13F_RAW:
      pattern: "spacinsider_leagues_13f_.*_$DATE$.json"
      col_num: 1
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "spacinsider/" 
      file_format: "FF_SPACINSIDER"

    SPACINSIDER_RELATIONSHIPS_RAW:
      pattern: "spacinsider_relationships_$DATE$.json"
      col_num: 1
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "spacinsider/" 
      file_format: "FF_SPACINSIDER"

    SPACINSIDER_PARTICIPANTS_RAW:
      pattern: "spacinsider_participants_$DATE$.json"
      col_num: 1
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "spacinsider/" 
      file_format: "FF_SPACINSIDER"

    SPACINSIDER_CORPORATE_ACTIONS_RAW:
      pattern: "spacinsider_corporate_actions_$DATE$.json"
      col_num: 1
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "spacinsider/" 
      file_format: "FF_SPACINSIDER"

    SPACINSIDER_SPACS_RAW:
      pattern: "spacinsider_all_spacs_$DATE$.json"
      col_num: 1
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "spacinsider/" 
      file_format: "FF_SPACINSIDER"