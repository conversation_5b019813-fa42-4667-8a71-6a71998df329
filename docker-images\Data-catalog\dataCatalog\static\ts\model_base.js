"use strict";
// Use an index signature to declare that ModelBase can have any number of string properties
class ModelBase {
    constructor() {
        this.elementIdMap = {};
    }
    defineProperty(propertyName, elementId, isTextContent = false) {
        this.elementIdMap[propertyName] = { id: elementId, textContent: isTextContent };
        Object.defineProperty(this, propertyName, {
            get: () => {
                const element = document.getElementById(this.elementIdMap[propertyName].id);
                if (!element) {
                    Logger.error(`defineProperty:get: Element with ID '${this.elementIdMap[propertyName].id}' not found for property '${propertyName}'.`);
                    return null;
                }
                if (isTextContent) {
                    return element.textContent;
                }
                else {
                    if ('value' in element) {
                        return element.value;
                    }
                    else {
                        Logger.error(`defineProperty:get: Property 'value' is not supported on element type '${element.tagName}' for property '${propertyName}'.`);
                        return null;
                    }
                }
            },
            set: (value) => {
                const element = document.getElementById(this.elementIdMap[propertyName].id);
                if (!element) {
                    Logger.error(`defineProperty:set: Element with ID '${this.elementIdMap[propertyName].id}' not found for property '${propertyName}'.`);
                    return;
                }
                if (isTextContent) {
                    element.textContent = value;
                    // Logger.info(`defineProperty:set: Updated textContent for '${propertyName}'.`);
                }
                else {
                    if ('value' in element) {
                        element.value = value;
                        // Logger.info(`defineProperty:set: Updated value for '${propertyName}'.`);
                    }
                    else {
                        Logger.error(`defineProperty:set: Property 'value' is not supported on element type '${element.tagName}' for property '${propertyName}'.`);
                    }
                }
            },
        });
    }
    attachEventListener(elementId, eventType, handler) {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener(eventType, handler.bind(this));
        }
        else {
            Logger.error(`AttachEventListener: Element not found with ID: ${elementId}`);
        }
    }
}
