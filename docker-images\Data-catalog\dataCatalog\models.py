import sqlalchemy as _sql
from sqlalchemy import <PERSON>umn, Integer, String

import datetime as _dt
from datetime import date

import sqlalchemy.orm as _orm
import passlib.hash as _hash

import database as _database
import pydantic as _pydantic

from typing import Annotated, Optional, get_origin

StrOrNone = Annotated[str, lambda v: "" if v is None else v]


class User(_database.Base):
    __tablename__ = "users"
    __table_args__ = {'schema': 'dataops'}
    id = _sql.Column(_sql.Integer, primary_key=True, index=True)
    email = _sql.Column(_sql.String, unique=True, index=True)
    hashed_password = _sql.Column(_sql.String)
    isAdmin = _sql.Column(_sql.Boolean)

    datasets = _orm.relationship("Catalog", back_populates="owner")

    def verify_password(self, password: str):
        return _hash.bcrypt.verify(password, self.hashed_password)
    
class Catalog(_database.Base):
    __tablename__ = "datacatalog"
    __table_args__ = {'schema': 'dataops'}
    id = _sql.Column(_sql.Integer, primary_key=True, index=True)
    owner_id = _sql.Column(_sql.Integer, _sql.ForeignKey("dataops.users.id"))
    dataset_id = _sql.Column(_sql.String, unique=True, index=True)
    dataset_name = _sql.Column(_sql.String, index=True, default='')
    vendor = _sql.Column(_sql.String)
    dataset_description = _sql.Column(_sql.String)
    dataset_details = _sql.Column(_sql.String)
    status = _sql.Column(_sql.String)
    permission_group = _sql.Column(_sql.String)
    product_owner = _sql.Column(_sql.String)
    data_management_lead = _sql.Column(_sql.String)
    dm_lead_email = _sql.Column(_sql.String)
    dm_lead_phone = _sql.Column(_sql.String)
    dm_lead_mobile = _sql.Column(_sql.String)
    vendor_contact_other = _sql.Column(_sql.String)
    vendor_contact_title = _sql.Column(_sql.String)
    vendor_contact_work_phone = _sql.Column(_sql.String)
    vendor_contact_mobile = _sql.Column(_sql.String)
    vendor_contact_email = _sql.Column(_sql.String)
    raw_data_location = _sql.Column(_sql.String)
    process_data_location = _sql.Column(_sql.String)
    file_type = _sql.Column(_sql.String)
    update_frequency = _sql.Column(_sql.String)
    technical_notes = _sql.Column(_sql.String)
    github_repository = _sql.Column(_sql.String)
    support_document_link = _sql.Column(_sql.String)
    file_names = _sql.Column(_sql.String)
    vendor_feed_sla_est = _sql.Column(_sql.String)
    file_count = _sql.Column(_sql.String)
    vendor_feed_extraction_source = _sql.Column(_sql.String)
    vendor_feed_extraction_source_url = _sql.Column(_sql.String)
    credentials = _sql.Column(_sql.String)
    grafana_alerts = _sql.Column(_sql.String)
    dataset_billing_name = _sql.Column(_sql.String)
    tables_loaded = _sql.Column(_sql.String)
    airflow_dag_names = _sql.Column(_sql.String)
    slack_channel_name = _sql.Column(_sql.String)
    teams_channel_name = _sql.Column(_sql.String)
    vendor_account_manager = _sql.Column(_sql.String)
    vendor_account_manager_work_phone = _sql.Column(_sql.String)
    vendor_account_manager_mobile = _sql.Column(_sql.String)
    vendor_account_manager_email = _sql.Column(_sql.String)
    vendor_sales_specialist = _sql.Column(_sql.String)
    vendor_sales_specialist_work_phone = _sql.Column(_sql.String)
    vendor_sales_specialist_mobile = _sql.Column(_sql.String)
    vendor_sales_specialist_email = _sql.Column(_sql.String)
    vendor_technical_account_manager = _sql.Column(_sql.String)
    vendor_technical_account_manager_work_phone = _sql.Column(_sql.String)
    vendor_technical_account_manager_mobile = _sql.Column(_sql.String)
    vendor_technical_account_manager_email = _sql.Column(_sql.String)
    customer_success_manager_product = _sql.Column(_sql.String)
    customer_success_manager_product_work_phone = _sql.Column(_sql.String)
    customer_success_manager_product_mobile  = _sql.Column(_sql.String)
    customer_success_manager_product_email = _sql.Column(_sql.String)
    last_updated_by = _sql.Column(_sql.String)
    date_last_updated = _sql.Column(_sql.DateTime, default=_dt.datetime.now())
    users = _sql.Column(_sql.String)
    vendor_ticketing_portal = _sql.Column(_sql.String)
    vendor_customer_support_dl = _sql.Column(_sql.String)
    vendor_hotline_number = _sql.Column(_sql.String)
    sftp_details = _sql.Column(_sql.String)
    historical_data_start_date = _sql.Column(_sql.Date, default=date(1700,1,1), nullable=True)
        

    owner = _orm.relationship("User", back_populates="datasets")  
    
    _database.Base.metadata.create_all(bind=_database.engine)
