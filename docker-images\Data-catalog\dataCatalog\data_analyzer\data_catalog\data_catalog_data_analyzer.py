import os
import pandas as pd
import numpy as np
from ast import literal_eval
from data_analyzer.data_analyzer import DataAnalyzer
import json

class DataCatalogDataAnaltyzer (DataAnalyzer):
    def check_data_availability(self):
        if not os.path.exists(self.input_datapath):
            print("\033[91mData file not found. Please generate input file first\033[0m")
            return False
        return True

    def load_or_create_embeddings(self, top_n=1000):
        if not os.path.exists(self.input_datapath):
            print("Input data file not found.")
            return

        if not os.path.exists(self.output_datapath + ".csv"):
            print("Creating embeddings...")
            # Load the dataset from a JSON-like structure
            with open(self.input_datapath, 'r') as f:
                data = json.load(f)

            headers = data['headers']
            dataset = data['data']

            records = []
            for key, entry in dataset.items():
                combined_text = f"Dataset_Id: {entry['Dataset_Id']}; Vendor: {entry['Vendor']};  Dataset_Description: {entry['Dataset_Description']}; Dataset_Details: {entry['Dataset_Details']}; Data_Management_Lead: {entry['Data_Management_Lead']}; DM_Lead_Email: {entry['DM_Lead_Email']}; DM_Lead_Phone: {entry['DM_Lead_Phone']}; DM_Lead_Mobile: {entry['DM_Lead_Mobile']}; Vendor_Contact_Other: {entry['Vendor_Contact_Other']}; Vendor_Contact_Title: {entry['Vendor_Contact_Title']}; Vendor_Contact_Work_Phone: {entry['Vendor_Contact_Work_Phone']}; Vendor_Contact_Mobile: {entry['Vendor_Contact_Mobile']}; Vendor_Contact_Email: {entry['Vendor_Contact_Email']}; Raw_Data_Location: {entry['Raw_Data_Location']}; Process_Data_Location: {entry['Process_Data_Location']}; File_Type: {entry['File_Type']}; Update_Frequency: {entry['Update_Frequency']}; Technical_Notes: {entry['Technical_Notes']}; GitHub_Repository: {entry['GitHub_Repository']}; Support_Document_Link: {entry['Support_Document_Link']}; File_names: {entry['File_names']}; Vendor_Feed_SLA_EST: {entry['Vendor_Feed_SLA_EST']}; File_Count: {entry['File_Count']}; Vendor_Feed_Extraction_Source: {entry['Vendor_Feed_Extraction_Source']}, Vendor_Feed_Extraction_Source_URL:{entry['Vendor_Feed_Extraction_Source_URL']}; Credentials: {entry['Credentials']}; Grafana_Alerts: {entry['Grafana_Alerts']}; DataSet_Billing_Name: {entry['DataSet_Billing_Name']}; Tables_Loaded: {entry['Tables_Loaded']}; Airflow_Dag_Names: {entry['Airflow_Dag_Names']}; Slack_Channel_Name: {entry['Slack_Channel_Name']}; Teams_Channel_Name: {entry['Teams_Channel_Name']}; Vendor_Account_Manager: {entry['Vendor_Account_Manager']}; Vendor_Account_Manager_Work_Phone: {entry['Vendor_Account_Manager_Work_Phone']}; Vendor_Account_Manager_Mobile: {entry['Vendor_Account_Manager_Mobile']}; Vendor_Account_Manager_Email: {entry['Vendor_Account_Manager_Email']}; Vendor_Sales_Specialist: {entry['Vendor_Sales_Specialist']};  Vendor_Sales_Specialist_Mobile: {entry['Vendor_Sales_Specialist_Mobile']}; Vendor_Sales_Specialist_Email: {entry['Vendor_Sales_Specialist_Email']}; Vendor_Technical_Account_Manager: {entry['Vendor_Technical_Account_Manager']}; Vendor_Technical_Account_Manager_Work_Phone: {entry['Vendor_Technical_Account_Manager_Work_Phone']}; Vendor_Technical_Account_Manager_Mobile: {entry['Vendor_Technical_Account_Manager_Mobile']}; Vendor_Technical_Account_Manager_Email: {entry['Vendor_Technical_Account_Manager_Email']};  Customer_Success_Manager_product: {entry['Customer_Success_Manager_product']}; Customer_Success_Manager_product_Work_Phone: {entry['Customer_Success_Manager_product_Work_Phone']};  Customer_Success_Manager_product_Mobile : {entry['Customer_Success_Manager_product_Mobile']}; Customer_Success_Manager_product_Email: {entry['Customer_Success_Manager_product_Email']}"
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            # combined_text = f"Dataset_Id: {entry['id']}; Vendor: {entry['Vendor']}; Dataset_Name,Dataset_Description,Dataset_Details,Data_Management_Lead,DM_Lead_Email,DM_Lead_Phone,DM_Lead_Mobile,Vendor_Contact_Other,Vendor_Contact_Title,Vendor_Contact_Work_Phone,Vendor_Contact_Mobile,Vendor_Contact_Email,Raw_Data_Location,Process_Data_Location,File_Type,Update_Frequency,Technical_Notes,GitHub_Repository,Support_Document_Link,File_names,Vendor_Feed_SLA_EST,File_Count,Vendor_Feed_Extraction_Source,Vendor_Feed_Extraction_Source_URL,Credentials,GrafanaAlerts,Dataset_Billing_Name,TablesLoaded,AirflowDagNames,SlackChannelName,TeamsChannelName,Vendor_Account_Manager,Vendor_Account_Manager_Work_Phone,Vendor_Account_Manager_Mobile,Vendor_Account_Manager_Email,Vendor_Sales_Specialist,Vendor_Sales_Specialist_Work_Phone,Vendor_Sales_Specialist_Mobile,Vendor_Sales_Specialist_Email,Vendor_Technical_Account_Manager,Vendor_Technical_Account_Manager_Work_Phone,Vendor_Technical_Account_Manager_Mobile,Vendor_Technical_Account_Manager_Email,Customer_Success_Manager_product,Customer_Success_Manager_product_Work_Phone,Customer_Success_Manager_product_Mobile,Customer_Success_Manager_product_Email}"
                embedding = self.get_embedding(combined_text)
                records.append({
                    "Dataset_Id": entry['Dataset_Id'],
                    "Vendor": entry['Vendor'],
                    "Dataset_Description": entry['Dataset_Description'],
                    "Dataset_Details": entry['Dataset_Details'],
                    "Data_Management_Lead": entry['Data_Management_Lead'],
                    "DM_Lead_Email": entry['DM_Lead_Email'],
                    "DM_Lead_Phone": entry['DM_Lead_Phone'],
                    "DM_Lead_Mobile": entry['DM_Lead_Mobile'],
                    "Vendor_Contact_Other": entry['Vendor_Contact_Other'],
                    "Vendor_Contact_Title": entry['Vendor_Contact_Title'],
                    "Vendor_Contact_Work_Phone": entry['Vendor_Contact_Work_Phone'],
                    "Vendor_Contact_Mobile": entry['Vendor_Contact_Mobile'],
                    "Vendor_Contact_Email": entry['Vendor_Contact_Email'],
                    "Raw_Data_Location": entry['Raw_Data_Location'],
                    "Process_Data_Location": entry['Process_Data_Location'],
                    "File_Type": entry['File_Type'],
                    "Update_Frequency": entry['Update_Frequency'],
                    "Technical_Notes": entry['Technical_Notes'],
                    "GitHub_Repository": entry['GitHub_Repository'],
                    "Support_Document_Link": entry['Support_Document_Link'],
                    "File_names": entry['File_names'],
                    "Vendor_Feed_SLA_EST": entry['Vendor_Feed_SLA_EST'],
                    "Dataset_Details": entry['Dataset_Details'],
                    "File_Count": entry['File_Count'],
                    "Vendor_Feed_Extraction_Source": entry['Vendor_Feed_Extraction_Source'],
                    "Vendor_Feed_Extraction_Source_URL": entry['Vendor_Feed_Extraction_Source_URL'],
                    "Credentials": entry['Credentials'],
                    "Grafana_Alerts": entry['Grafana_Alerts'],
                    "DataSet_Billing_Name": entry['DataSet_Billing_Name'],
                    "Tables_Loaded": entry['Tables_Loaded'],
                    "Airflow_Dag_Names": entry['Airflow_Dag_Names'],
                    "Slack_Channel_Name": entry['Slack_Channel_Name'],
                    "Teams_Channel_Name": entry['Teams_Channel_Name'],
                    "Vendor_Account_Manager_Work_Phone": entry['Vendor_Account_Manager_Work_Phone'],
                    "Vendor_Account_Manager_Mobile": entry['Vendor_Account_Manager_Mobile'],
                    "Vendor_Account_Manager_Email": entry['Vendor_Account_Manager_Email'],
                    "Vendor_Sales_Specialist": entry['Vendor_Sales_Specialist'],
                    "Vendor_Sales_Specialist_Mobile": entry['Vendor_Sales_Specialist_Mobile'],
                    "Vendor_Sales_Specialist_Email": entry['Vendor_Sales_Specialist_Email'],
                    "Vendor_Technical_Account_Manager": entry['Vendor_Technical_Account_Manager'],
                    "Vendor_Technical_Account_Manager_Work_Phone": entry['Vendor_Technical_Account_Manager_Work_Phone'],
                    "Vendor_Technical_Account_Manager_Mobile": entry['Vendor_Technical_Account_Manager_Mobile'],
                    "Vendor_Technical_Account_Manager_Email": entry['Vendor_Technical_Account_Manager_Email'],
                    "Customer_Success_Manager_product": entry['Customer_Success_Manager_product'],
                    "Customer_Success_Manager_product_Work_Phone": entry['Customer_Success_Manager_product_Work_Phone'],
                    "Customer_Success_Manager_product_Email ": entry['Customer_Success_Manager_product_Email'],
                    "combined": combined_text,
                    "embedding": embedding
                })

            df = pd.DataFrame(records)
            df.to_csv(self.output_datapath + ".csv", index=False)
        else:
            print("Loading embeddings...")
            df = pd.read_csv(self.output_datapath + ".csv")
            df['embedding'] = df['embedding'].apply(lambda x: np.array(literal_eval(x)))
        
        self.df = df
        return df