from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.snowflake.adaptor import SnowflakeAdaptor

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )
    df_future_roots = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """select fs.id_bb_global_company, fs.future_root, lower(fs.future_root || '_' || fs.market_sector) as future_root_w_sector
           from BBGH_FUTURES.FIGI_COMP_BACKFILL_UNIV bf_figi join 
                BBGH_FUTURES.FUTURE_SERIES fs on bf_figi.id_bb_global_company = fs.id_bb_global_company 
            where fs.JG_FUT_CATEGORY = 'CURR_FUT'""",
    )
    dict_future_roots = dict(zip(df_future_roots["FUTURE_ROOT_W_SECTOR"], df_future_roots["ID_BB_GLOBAL_COMPANY"]))
    print(dict_future_roots)
    for future_root in dict_future_roots.keys():
        print(f"Processing {future_root}")
        batch_name = f"dp_{future_root}_px"
        id_bb_global_company = dict_future_roots[future_root]   
        per_sec_req_type = PerSecurityRequestType.gethistory

        df_futures = sf_adaptor.read_data(
        "BBGH_FUTURES",
        f"""select ref.bbg_full_ticker || ' ' || fs.market_sector AS bbg_full_ticker
            from BBGH_FUTURES.FUTURE_REF ref join 
            BBGH_FUTURES.FUTURE_SERIES fs on ref.id_bb_global_company = fs.id_bb_global_company
            where ref.id_bb_global_company = '{id_bb_global_company}' and 
            ref.FUT_CONTRACT_DT >= '1/1/2000' and 
            ref.FUT_CONTRACT_DT <= '12/1/2027'
            order by ref.fut_contract_dt""",
        )

        securities = df_futures["BBG_FULL_TICKER"].tolist()
        request_dict = {
            "firm_name": "dl47544",
            "program_flag": "adhoc",
            "date_range": "20000101|20241223",
            "sec_id": "TICKER",
            "fields": [
                "PX_OPEN",
                "PX_HIGH",
                "PX_LOW",
                "PX_SETTLE",
                "PX_VOLUME",
                "OPEN_INT",
            ],
            "securities": securities,
        }
        target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_px_eod/"
        PerSecurityRequestRunner(
            batch_name, per_sec_req_type, request_dict, target_folder
        ).run()

        
    