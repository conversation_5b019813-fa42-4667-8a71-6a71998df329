#!/bin/bash

# Path to the configuration file
config_file="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/conf/connector_config.conf"
output_file="/etc/node_exporter/connector_status.prom"
server_IP="*************"

while true; do
    # Use a temporary file to avoid incomplete writes
    temp_file="/tmp/connector_status.prom.$$"

    # Start writing Prometheus metrics
    echo "# HELP kafka_connector_status Connector status (1=RUNNING, 0=DOWN)" > "$temp_file"
    echo "# TYPE kafka_connector_status gauge" >> "$temp_file"

    # Iterate over each line in the config file
    while IFS= read -r line || [ -n "$line" ]; do
        # Skip empty lines or lines starting with #
        [[ -z "$line" || $line =~ ^# ]] && continue

        # Extract fields
        IFS='|' read -r _ port _ _ <<< "$line"

        # Ensure valid port value
        [[ -z "$port" ]] && continue

        # Fetch the list of connectors for the given port
        connectors_list=$(curl -s --max-time 5 "http://$server_IP:$port/connectors/" | jq -e -r 'select(.!=null) | .[]' 2>/dev/null)

        # If Kafka Connect is down or no connectors found, mark it as down
        if [ -z "$connectors_list" ]; then
            echo "kafka_connector_status{port=\"$port\",connector=\"none\"} 0" >> "$temp_file"
            continue
        fi

        # Loop through each connector and check its status
        for connector_name in $connectors_list; do
            # Fetch the status of the connector
            response=$(curl -s --max-time 5 "http://$server_IP:$port/connectors/$connector_name/status")

            # If response is empty or not valid JSON, mark connector as DOWN
            if [ -z "$response" ] || ! echo "$response" | jq -e . >/dev/null 2>&1; then
                echo "kafka_connector_status{port=\"$port\",connector=\"$connector_name\"} 0" >> "$temp_file"
                echo "kafka_task_status{port=\"$port\",connector=\"$connector_name\",task_id=\"none\"} 0" >> "$temp_file"
                continue
            fi

            # Extract connector state
            connector_state=$(echo "$response" | jq -r '.connector.state // "UNKNOWN"')
            state_value=$([[ "$connector_state" == "RUNNING" ]] && echo 1 || echo 0)

            echo "kafka_connector_status{port=\"$port\",connector=\"$connector_name\"} $state_value" >> "$temp_file"

            # Extract and write task states, handling empty task lists
            task_data=$(echo "$response" | jq -e '.tasks[]?' 2>/dev/null)
            if [ -z "$task_data" ]; then
                echo "kafka_task_status{port=\"$port\",connector=\"$connector_name\",task_id=\"none\"} 0" >> "$temp_file"
            else
                echo "$response" | jq -r --arg port "$port" --arg connector "$connector_name" \
                    '.tasks[] | "kafka_task_status{port=\"" + $port + "\",connector=\"" + $connector + "\",task_id=\"" + (.id|tostring) + "\"} " + (if .state == "RUNNING" then "1" else "0" end)' \
                    >> "$temp_file"
            fi
        done
    done < "$config_file"

    # Move the temp file to the actual location (overwrite safely)
    mv "$temp_file" "$output_file"

    # Ensure correct permissions
    chmod 644 "$output_file"

    echo "Status updated. Sleeping for 5 seconds..."
    sleep 5
done
