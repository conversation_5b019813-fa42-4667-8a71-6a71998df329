raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/usda_agri"
  s3_bucket: "jg-data-dp-vendor-data"
  s3_prefix: "usda-agri"
  include_prefix: true


  structure: '[
  "All_Commodities_$DATE$.csv",
  "All_Countries_$DATE$.csv",
  "All_Regions_$DATE$.csv",
  "Attributes_$DATE$.csv",
  "Availability_By_Comodity_$DATE$.csv",
  "Commodity_Groups_$DATE$.csv",
  "Commodity_Report_Headers_$DATE$.csv",
  "Dataset_Mapping_$DATE$.csv",
  "Release_Schedule_$DATE$.csv",
  "UOM_$DATE$.csv"
  ]'


snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "USDA_REFERENCE"

  table_map:
  
    Country_RAW:
      pattern: "^All_Countries_$DATE$.csv" 
      col_num: 4
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI"
    
      

    REGION_RAW:
      pattern: "^All_Regions_$DATE$.csv" 
      col_num: 2
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI"  

    COMMODITY_RAW:
      pattern: "^All_Commodities_$DATE$.csv" 
      col_num: 5
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI"

    ATTRIBUTE_RAW:
      pattern: "^Attributes_$DATE$.csv" 
      col_num: 3
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 

    AVAILABILITY_RAW:
      pattern: "^Availability_By_Comodity_$DATE$.csv" 
      col_num: 8
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 

    COMMODITY_GROUP_RAW:
      pattern: "^Commodity_Groups_$DATE$.csv" 
      col_num: 4
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 


    COMMODITY_REPORT_RAW:
      pattern: "^Commodity_Report_Headers_$DATE$.csv" 
      col_num: 5
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 

    DATASET_MAPPING_RAW:
      pattern: "^Dataset_Mapping_$DATE$.csv" 
      col_num: 2
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 


    US_AGRI_RELEASE_SCHEDULE_RAW:
      pattern: "^Release_Schedule_$DATE$.csv" 
      col_num: 3
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 


    UOM_RAW:
      pattern: "^UOM_$DATE$.csv" 
      col_num: 2
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 




  

    
    




    