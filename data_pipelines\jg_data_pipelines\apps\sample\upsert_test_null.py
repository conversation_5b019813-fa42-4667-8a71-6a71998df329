from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from timeit import default_timer as timer
import pandas as pd
import numpy as np
from datetime import datetime, timedelta


# Example usage and validation
if __name__ == "__main__":
    start = timer()
    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_SAMPLE",
        warehouse="BBG_DLPLUS_WH", 
        role="FR_BBGH_SUPPORT"
     )
    source = pd.DataFrame(
        [
            (1, 1, "A", "A"),
            (1, 2, "A", "C"),
            (1, 3, "Z", None),
        ], 
        columns=["id_1", "id_2", "val_1", "val_2"])

    adaptor.upsert(source, "upsert_test", ["id_1", "id_2"])
    end = timer()
    print(f"Upsert time: {end - start}")
