#!/bin/bash

# Define variables
SCRIPT_NAME="main.py"
ENVIRONMENT="prod"
LOG_FILE="logs/creditevent_process.log"
MODULE="creditevent"

# Ensure logs directory exists
if [ ! -d "logs" ]; then
    mkdir -p logs
    echo "Created logs directory"
fi

# Function to start the process
start_process() {
    echo "Starting credit event processing..."
    python3 $SCRIPT_NAME $ENVIRONMENT $MODULE >> $LOG_FILE 2>&1 &
    echo $! > logs/creditevent_process.pid
    echo "Process started with PID $(cat logs/creditevent_process.pid)"
}

# Function to stop the process
stop_process() {
    if [ -f logs/creditevent_process.pid ]; then
        PID=$(cat logs/creditevent_process.pid)
        echo "Stopping process with PID $PID..."
        kill $PID
        rm logs/creditevent_process.pid
        echo "Process stopped."
    else
        echo "No process found to stop."
    fi
}

# Function to check the status of the process
status_process() {
    if [ -f logs/creditevent_process.pid ]; then
        PID=$(cat logs/creditevent_process.pid)
        if ps -p $PID > /dev/null; then
            echo "Process is running with PID $PID."
        else
            echo "Process is not running, but PID file exists."
        fi
    else
        echo "No process found."
    fi
}

# Main script logic
case "$1" in
    start)
        start_process
        ;;
    stop)
        stop_process
        ;;
    status)
        status_process
        ;;
    *)
        echo "Usage: $0 {start|stop|status}"
        exit 1
        ;;
esac