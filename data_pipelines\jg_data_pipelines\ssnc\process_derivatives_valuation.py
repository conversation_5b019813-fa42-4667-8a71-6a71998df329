import pandas as pd
import re
import logging
import os, sys,json
from datetime import datetime, timezone
# from dateutil import parser
sys.path.append(os.getcwd())
import json
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '../../../source/bin')))
from strunner import *
setupEnvironment()
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)
from utils.snowflake.adaptor import SnowflakeAdaptor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
def flatten_multilevel_headers(header_tuples):
    flattened = []
    for col in header_tuples:
        parts = [str(part).strip() for part in col if pd.notna(part) and not str(part).startswith("Unnamed")]
        flattened.append("_".join(parts) if parts else "Unnamed")
    return flattened

def process_derivatives_valuations(file, table_name, stage_name, staging_table_name=None):
    xls = pd.ExcelFile(f'{jfs_rawdata_directory}/{file}', engine='openpyxl')
    all_sheets = xls.sheet_names

    for sheet in all_sheets:
        logger.info(f"\n\nPROCESSING SHEET:: {sheet}")
        df = pd.read_excel(f'{jfs_rawdata_directory}/{file}', sheet_name=sheet, header=[0, 1] if sheet!='Swaption' else [0, 1, 2], skiprows=12, engine='openpyxl', skipfooter=1)
        df.columns = flatten_multilevel_headers(df.columns)
        
        
        df.columns = df.columns.str.strip().str.replace("/ ", "/").str.replace("/", "_or_").str.replace(" ","_").str.replace("(", "").str.replace(")", "").str.replace("[", "").str.replace("]", "").str.replace("&", "").str.replace(".", "").str.replace("__", "_").str.replace("%", "_percent")
        df.columns = [f"_{col}" if col[0].isdigit() else col for col in df.columns]
        
        df.rename(columns={'Counterparty_or_Clearing_Member_Final_Source_Load_Time':'Final_Source_Load_Time'}, inplace=True)
        df.rename(columns={'SSC_GlobeOp_Trade_Attributes_Rec_Comment': 'Rec_Comment'}, inplace=True)
        
        df.columns = df.columns.str.upper()
        
        datetime_columns = ['SSC_GLOBEOP_TRADE_ATTRIBUTES_LASTMODIFIEDDATETIME', 'SSC_GLOBEOP_TRADE_ATTRIBUTES_SWAP_LASTMODIFIEDDATETIME', 'FINAL_SOURCE_LOAD_TIME']
        
        for dt_col in datetime_columns:
            if dt_col in df.columns:
                df[dt_col] = pd.to_datetime(df[dt_col], format='%d-%b-%Y %H:%M:%S')
                df[dt_col] = df[dt_col].dt.strftime('%Y-%m-%d %H:%M:%S')
            
        if 'SSC_GLOBEOP_TRADE_ATTRIBUTES_OPTION_TRADE_DATE' in df.columns:
            df['SSC_GLOBEOP_TRADE_ATTRIBUTES_OPTION_TRADE_DATE'] = pd.to_datetime(df['SSC_GLOBEOP_TRADE_ATTRIBUTES_OPTION_TRADE_DATE'], format='%d-%b-%Y')
            df['SSC_GLOBEOP_TRADE_ATTRIBUTES_OPTION_TRADE_DATE'] = df['SSC_GLOBEOP_TRADE_ATTRIBUTES_OPTION_TRADE_DATE'].dt.strftime('%Y-%m-%d')

        init_df = pd.read_excel(f'{jfs_rawdata_directory}/{file}', sheet_name=sheet, nrows=11)
        metadata = init_df.iloc[-1,0]
        metadata_dict = {key.strip().replace(" ", "_"): value for key, value in re.findall(r'([\w\s]+)\[([^\]]+)\]', metadata)}
        metadata_df = pd.DataFrame([metadata_dict])
        valuation_date_str = metadata_df['Valuation_Date'].iloc[0]
        valuation_date_obj = datetime.strptime(valuation_date_str, "%d-%b-%Y")
        metadata_df['Valuation_Date'] = valuation_date = valuation_date_obj.strftime("%Y-%m-%d")

        knowledge_date_str = metadata_df['Knowledge_Date'].iloc[0]
        knowledge_date_obj = datetime.strptime(knowledge_date_str.replace(' EST', ''), "%d-%b-%Y %H:%M:%S")
        metadata_df['Knowledge_Date'] = knowledge_date = knowledge_date_obj.strftime("%Y-%m-%d %H:%M:%S")
        
        
        tableName = f"{table_name}_{sheet.replace('&', 'AND').replace(' ', '_')}"
        stage_file_name = f"{file}_{sheet}"
        
        if "ME" not in file:
            df.insert(0, 'Knowledge_Date', knowledge_date)
            df.insert(0, 'Valuation_Date', valuation_date)
            sf_adaptor.execute_query(schema_name, f'TRUNCATE {tableName}')
            sf_adaptor.load_df(df, stage_file_name.replace(' ', '_'), schema_name, tableName, stage_name)
        
        else:
            stagingTableName = f"{staging_table_name}_{sheet.replace('&', 'AND').replace(' ', '_')}"
            
            fetch_query = f"SELECT * FROM {tableName} WHERE VERSION = (SELECT MAX(VERSION) FROM {tableName});"
            existing_data_df = sf_adaptor.read_data(schema_name, fetch_query, objconfig['ssnc_role']) 

            latest_version = sf_adaptor.read_data(schema_name, f"SELECT MAX(VERSION) FROM {tableName}", objconfig['ssnc_role'])
            latest_version = 0 if latest_version.iloc[0,0] is None else latest_version.iloc[0,0]
            
            df.insert(0, 'Version', latest_version)
            metadata_df.insert(0, 'Version', latest_version)
            
            # load staging data
            sf_adaptor.execute_query(schema_name, f'TRUNCATE {stagingTableName}')

            sf_adaptor.load_df(df, stage_file_name.replace(' ', '_'), 'STAGING', stagingTableName, stage_name)
            staging_data_df = sf_adaptor.read_data(schema_name, f'SELECT * FROM {stagingTableName}', objconfig['ssnc_role'])

            if not existing_data_df.empty and existing_data_df.equals(staging_data_df):
                fetch_query = f"SELECT MAX(DATE(KNOWLEDGE_DATE)) FROM {metadata_table} WHERE VERSION = (SELECT MAX(VERSION) FROM {metadata_table});"
                KNOWLEDGE_DATE = sf_adaptor.read_data(schema_name, fetch_query, objconfig['ssnc_role'])
                if(str(KNOWLEDGE_DATE.iloc[0,0])==date):
                    logger.info("Same data as previous ME file, no new data to load")
                else:
                    logger.info("Updating METADATA table to point to previous version")
                    sf_adaptor.load_df(metadata_df, stage_file_name.replace(' ', '_'), schema_name, metadata_table, metadata_stage)

            else:
                logger.info("Uploading Derivatives Valuation ME data...")
                metadata_df['Version'] = metadata_df['Version']+1 
                df['Version'] = df['Version']+1
                sf_adaptor.load_df(metadata_df, stage_file_name.replace(' ', '_'), schema_name, metadata_table, metadata_stage)
                sf_adaptor.load_df(df, stage_file_name.replace(' ', '_'), schema_name, tableName, stage_name, True)

def get_files_with_substring_and_date(rawdata_directory, substring, date):
        date_obj = datetime.strptime(date, '%Y-%m-%d')

        # Get the list of files in the current directory
        files = os.listdir(rawdata_directory)
        filtered_files = []
        
        for f in files:
            if substring in f :
                mod_time = datetime.fromtimestamp(os.path.getmtime(f'{rawdata_directory}/{f}'))
                if mod_time.date() == date_obj.date():
                    filtered_files.append(f'{rawdata_directory}/{f}')

        # Sort the filtered files by last modified time
        sorted_files = sorted(filtered_files, key=lambda x: os.path.getmtime(x))
        return sorted_files


if __name__ == "__main__":
    
    configPath = os.environ.get('CONFIG_PATH', os.getcwd())
    with open(f'{configPath}/config.json', 'r') as f:
        config = json.load(f)

    def jg_config_path():
        return config["JG_CONFIG_PATH"]
    
    def jg_rawdata_path():
        return config["JG_DATA_PATH"]

    def read_config_secrets():
        config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
        with open(config_secret_path, 'r') as f:
            config_secret = json.load(f)
        return config_secret
    objconfig = read_config_secrets()

    os.environ['SF_USERNAME'] = objconfig['sf_user']
    os.environ['SF_PASSWORD'] = objconfig['sf_password']
    os.environ['SF_DATABASE'] = objconfig['ssnc_database']
    os.environ['SF_WAREHOUSE'] = objconfig['ssnc_warehouse']
    os.environ['SF_ROLE'] = objconfig['ssnc_role']
    os.environ['SF_SCHEMA'] = schema = objconfig['ssnc_valuation_schema']
    
    jfs_rawdata_directory = f'{jg_rawdata_path()}/SSandC'
    sf_adaptor = SnowflakeAdaptor(database=objconfig['ssnc_database'], warehouse=objconfig['ssnc_warehouse'], role=objconfig['ssnc_role'])
    
    substring = 'Jainglobal OTC Report Per Product'
    date = datetime.now(timezone.utc).date().strftime('%Y-%m-%d')
    logger.info(f"Fetching files for date: '{date}'")
    files = get_files_with_substring_and_date(jfs_rawdata_directory, substring, date)
    
    logger.info(f"Files for processing: {files}")
    for file_path in files:  
        logger.info(f"\n\n\nProcessing file:: {file_path}")
        file_path = file_path.replace(f'{jfs_rawdata_directory}/', '')
        metadata_table = f'{schema}.DERIVATIVES_METADATA'
        metadata_stage = f'{schema}.STG_OTC_METADATA'

        if "ME" in file_path:
            stage_list = sf_adaptor.read_data(schema, f'LIST @{schema}.STG_OTC_METADATA;')
            loaded_files_list = stage_list['name'].to_list()
            for i in range(len(loaded_files_list)):
                loaded_files_list[i] = loaded_files_list[i].replace('stg_otc_metadata/', '').split('.xlsx')[0]
            
            if file_path.replace(' ', '_').replace('.xlsx', '') in loaded_files_list:
                logger.info(f"Data is already loaded for file '{file_path}'")
                continue
           
            schema_name = objconfig['ssnc_valuation_schema']
            table_name = f'{schema}.ME_DERIVATIVES_VALUATION'
            staging_table_name = f'STAGING.ME_DERIVATIVES_VALUATION'
            stage_name = f'{schema}.STG_ME_DERIVATIVES_VALUATION'
            
            process_derivatives_valuations(file_path, table_name, stage_name, staging_table_name)
            
        else:
            stage_list = sf_adaptor.read_data(schema, f'LIST @{schema}.STG_DERIVATIVES_VALUATION;')
            loaded_files_list = stage_list['name'].to_list()
            for i in range(len(loaded_files_list)):
                loaded_files_list[i] = loaded_files_list[i].replace('stg_derivatives_valuation/', '').split('.xlsx')[0]
        
            if file_path.replace(' ', '_').replace('.xlsx', '') in loaded_files_list:
                logger.info(f"Data is already loaded for file '{file_path}'")
                continue
            
            schema_name = objconfig['ssnc_valuation_schema']
            table_name = f'{schema}.DERIVATIVES_VALUATION'
            stage_name = f'{schema}.STG_DERIVATIVES_VALUATION'
            
            process_derivatives_valuations(file_path, table_name, stage_name)
    