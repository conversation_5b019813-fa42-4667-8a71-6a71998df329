#!/bin/bash
exec > catalog.out
exec 2>&1
#tail -f /dev/null
# Generate a timestamp
TIMESTAMP=$(date +"%Y%m%d%H%M%S")

# Check if uvicorn is running
UVICORN_PID=$(pgrep -f uvicorn)

if [ ! -z "$UVICORN_PID" ]; then
    echo "Uvicorn is running with PID: $UVICORN_PID. Killing uvicorn..."
    kill $UVICORN_PID
fi

# Compile TypeScript files
echo "Compiling TypeScript files..."
tsc
COMPILE_STATUS=$?

if [ $COMPILE_STATUS -eq 0 ]; then
    echo "TypeScript files compiled successfully."
    # Replace the timestamp in HTML files
    HTML_DIR="./static"
    echo "Updating HTML files in $HTML_DIR..."

    # Debugging: list files being processed
    echo "Files to be processed:"
    ls ${HTML_DIR}/*.html

    # Apply sed to each file (adding file output for debugging)
    for file in ${HTML_DIR}/*.html; do
        echo "Processing file: $file"
        sed -i "s/?v=[0-9]\{14\}/?v=${TIMESTAMP}/g" "$file"
    done

    echo -e "\033[0;33mAll JavaScript and CSS file URLs have been updated with new timestamp: ${TIMESTAMP}\033[0m"

    # Start the server
    echo "Starting uvicorn server..."
        python3 -m uvicorn app:app --host 0.0.0.0 --port 8000 --log-level debug --reload
        # uvicorn app:app --ssl-keyfile ./key.pem --ssl-certfile ./cert.pem --host 0.0.0.0 --port 8000 --log-level debug
else
    echo "TypeScript compilation failed with status $COMPILE_STATUS."
    echo "Server will not start until the errors are resolved."
fi
