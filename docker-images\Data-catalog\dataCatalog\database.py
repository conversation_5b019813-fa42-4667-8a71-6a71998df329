from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import json

f = open('config_secret.json')
config = json.load(f)

DATABASE_URL = "postgresql+psycopg2://"+config['rds_user']+":"+config['rds_password']+"@tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com:5432/postgres"

engine = create_engine(DATABASE_URL, pool_size=20, max_overflow=10)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()