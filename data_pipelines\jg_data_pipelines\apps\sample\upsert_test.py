from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from timeit import default_timer as timer
import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

def generate_random_data(n_rows=1000, start_date=None, securities=None):
    """
    Generate random financial bar data with unique composite key (SECURITY, TH_BAR_TYPE, TH_BAR_TIME).
    
    Args:
        n_rows (int): Number of rows to generate
        start_date (datetime, optional): Starting date for time series
        securities (list, optional): List of security identifiers to use
    
    Returns:
        pd.DataFrame: DataFrame with random bar data
    """
    if start_date is None:
        start_date = datetime(2015, 1, 1)
    
    if securities is None:
        # List of 100 well-known US stock tickers
        securities = [
            'AAPL US Equity', 'MSFT US Equity', 'GOOGL US Equity', 'AMZN US Equity', 'META US Equity',
            'NVDA US Equity', 'BRK/B US Equity', 'JPM US Equity', 'V US Equity', 'XOM US Equity',
            'JNJ US Equity', 'WMT US Equity', 'BAC US Equity', 'PG US Equity', 'MA US Equity',
            'TSLA US Equity', 'HD US Equity', 'CVX US Equity', 'ABBV US Equity', 'PFE US Equity',
            'KO US Equity', 'PEP US Equity', 'MRK US Equity', 'AVGO US Equity', 'COST US Equity',
            'CSCO US Equity', 'TMO US Equity', 'ABT US Equity', 'MCD US Equity', 'ACN US Equity',
            'DHR US Equity', 'NEE US Equity', 'VZ US Equity', 'WFC US Equity', 'TXN US Equity',
            'PM US Equity', 'NKE US Equity', 'RTX US Equity', 'MS US Equity', 'QCOM US Equity',
            'UNP US Equity', 'BMY US Equity', 'INTC US Equity', 'CRM US Equity', 'BA US Equity',
            'AMGN US Equity', 'T US Equity', 'DE US Equity', 'INTU US Equity', 'GS US Equity',
            'AMD US Equity', 'IBM US Equity', 'CAT US Equity', 'SPGI US Equity', 'LMT US Equity',
            'AXP US Equity', 'ORCL US Equity', 'HON US Equity', 'SBUX US Equity', 'LLY US Equity',
            'BLK US Equity', 'AMAT US Equity', 'GE US Equity', 'ADI US Equity', 'MMM US Equity',
            'MDLZ US Equity', 'GILD US Equity', 'TGT US Equity', 'PYPL US Equity', 'C US Equity',
            'DUK US Equity', 'MO US Equity', 'BDX US Equity', 'PLD US Equity', 'SCHW US Equity',
            'SO US Equity', 'USB US Equity', 'AMT US Equity', 'CI US Equity', 'ISRG US Equity',
            'FIS US Equity', 'ADP US Equity', 'BKNG US Equity', 'ANTM US Equity', 'CCI US Equity',
            'CME US Equity', 'COF US Equity', 'D US Equity', 'EOG US Equity', 'ETN US Equity',
            'FDX US Equity', 'GD US Equity', 'GPN US Equity', 'HCA US Equity', 'ICE US Equity',
            'ITW US Equity', 'KMB US Equity', 'LHX US Equity', 'LYB US Equity', 'MCK US Equity',
            'MET US Equity', 'MMC US Equity', 'MPC US Equity', 'NSC US Equity', 'PGR US Equity',
            'PSX US Equity', 'SLB US Equity', 'SRE US Equity', 'STT US Equity', 'SYK US Equity'
        ]
    
    bar_types = ['TRADE', 'BID', 'ASK']
    
    # Calculate how many timestamps we need to generate n_rows unique combinations
    min_timestamps_needed = np.ceil(n_rows / (len(securities) * len(bar_types)))
    timestamps = [start_date + timedelta(minutes=i) for i in range(int(min_timestamps_needed))]
    
    # Create all possible combinations of security, bar_type, and timestamp
    all_combinations = []
    for security in securities:
        for bar_type in bar_types:
            for ts in timestamps:
                all_combinations.append((security, bar_type, ts))
    
    # Randomly select n_rows combinations
    selected_combinations = np.random.choice(
        len(all_combinations), 
        size=n_rows, 
        replace=False
    )
    
    # Create the base data from selected combinations
    data = {
        'SECURITY': [],
        'TH_BAR_TYPE': [],
        'TH_BAR_TIME': []
    }
    
    for idx in selected_combinations:
        security, bar_type, ts = all_combinations[idx]
        data['SECURITY'].append(security)
        data['TH_BAR_TYPE'].append(bar_type)
        data['TH_BAR_TIME'].append(ts)
    
    # Generate OHLC data ensuring high >= open,close >= low
    bases = np.random.uniform(10, 1000, n_rows)  # Base prices
    spreads = np.random.uniform(0.1, 2, n_rows)  # Price spreads
    
    opens = bases + np.random.uniform(-spreads, spreads, n_rows)
    closes = bases + np.random.uniform(-spreads, spreads, n_rows)
    
    # Ensure high is highest and low is lowest
    highs = np.maximum(opens, closes) + np.random.uniform(0, spreads, n_rows)
    lows = np.minimum(opens, closes) - np.random.uniform(0, spreads, n_rows)
    
    data.update({
        'TH_BAR_OPEN': opens,
        'TH_BAR_HIGH': highs,
        'TH_BAR_LOW': lows,
        'TH_BAR_CLOSE': closes
    })
    
    # Create DataFrame and sort by security and time
    df = pd.DataFrame(data)
    df = df.sort_values(['SECURITY', 'TH_BAR_TIME', 'TH_BAR_TYPE'])
    
    # Verify uniqueness
    duplicate_check = df.duplicated(['SECURITY', 'TH_BAR_TYPE', 'TH_BAR_TIME'], keep=False)
    if duplicate_check.any():
        raise ValueError("Duplicate composite keys found! This should never happen.")
    
    return df

# Example usage and validation
if __name__ == "__main__":

    # adaptor = SnowparkAdaptor(
    #     database="BLOOMBERG", 
    #     warehouse="BBG_DLPLUS_WH", 
    #     role="FR_BBGH_SUPPORT"
    # )
    # adaptor.connect("BBGH_SAMPLE")
    # source = adaptor.session.create_dataframe([
    #         (1, 1, 'A', 'B'),
    #         (1, 2, 'A', 'C'),
    #         (1, 3, 'D', 'D'),
    #     ], 
    #     schema=["id_1", "id_2", "val_1", 'val_2'])

    # adaptor.upsert(source, "BBGH_SAMPLE", "upsert_test", ["id_1", "id_2"])


    # Generate 1000 rows of random data
    df = generate_random_data(200000)
    
    # Display first few rows
    print("\nFirst few rows of generated data:")
    print(df.head())
    
    # Data validation
    print("\nData validation:")
    print(f"Number of rows: {len(df)}")
    # print(f"High >= Open,Close >= Low check: {(df['TH_BAR_HIGH'] >= df[['TH_BAR_OPEN', 'TH_BAR_CLOSE']]).all().all() and (df[['TH_BAR_OPEN', 'TH_BAR_CLOSE']] >= df['TH_BAR_LOW']).all().all()}")
    # print(f"No null values: {not df.isnull().any().any()}")
    
    # Uniqueness validation
    duplicates = df.duplicated(['SECURITY', 'TH_BAR_TYPE', 'TH_BAR_TIME'], keep=False)
    print(f"No duplicate composite keys: {not duplicates.any()}")

    start = timer()
    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_SAMPLE",
        warehouse="BBG_DLPLUS_WH", 
        role="FR_BBGH_SUPPORT"
     )
    adaptor.upsert(df, "MEGA_UPSERT_TEST_JUAN", ["SECURITY", "TH_BAR_TYPE", "TH_BAR_TIME"])
    end = timer()
    print(f"Upsert time: {end - start}")
