/* static/css/data_catalog.css */
html {
    width: 100%;
    height: 100%;
    /* overflow: hidden; */
}

body {
    background-color: #000;
    color: #fff;
    font-family: Arial, sans-serif;
    height: 100%;
}

.tabs {
    overflow: hidden;
    border: 1px solid #ccc;
    background-color: #444;
}

.tablinks {
    background-color: inherit;
    float: left;
    border: none;
    outline: none;
    cursor: pointer;
    padding: 12px 10px;
    transition: 0.3s;
    font-size: 12px;
    color: #fff;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: bold;
}

.tablinks:hover {
    background-color: #555;
}

.tablinks.active {
    background-color: #666;
}

.tabcontent {
    height: 100%;
    display: none;
    padding: 6px 12px;
    border-top: none;
    color: #fff;
}

table {
    width: 100%;
    height: 100%;
    /* border-collapse: collapse; */
    display: block;
    /* scroll-behavior: auto; */
    /* overflow: scroll; */
    padding-right:17px;
    box-sizing: border-box;
    font-size: 10px;
}

th {
    padding: 5px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    text-wrap: nowrap;
    text-overflow: ellipsis;
    max-width: 300px;
    overflow: hidden;
    background-color: #444;
    min-width: 50pxpx;
}

td {
    padding: 3px;
    text-align: left;
    border-bottom: 1px solid #ddd;
    text-overflow: ellipsis;
    max-width: 300px;
    text-wrap: nowrap;
    overflow: hidden;
}

td:focus {
    text-wrap: wrap;
}

tr:hover {
    background-color: #555;
}

button {
    background-color: #444;
    color: white;
    padding: 5px 10px;
    border: none;
    cursor: pointer;
    font-size: 10px;
}

button:hover {
    background-color: #555;
}

.add-btn {
    /* float: right;  */
    margin-bottom: 10px;
    font-size: 16px;
    margin-left: auto;
    display:flex;
    gap:5px;
}

.columnDropdown {
    background-color: #444;
    color: white;
    padding: 4px;
    border: none;
    cursor: pointer;
    border-radius: 4px;
    font-size: 10px;
}

.input-op {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    margin-top: 10px;
}

thead {
    position: sticky;
    top: 0;
}

.details_header {
    display: block;
}

.header {
    font-family: Arial, Helvetica, sans-serif;
    font-size: 20px;
    font-weight: bold;
}
.save-btn {
    float:right
}

#add_input, #add_dataset {
    display: none;
}

#login-button, #logout-button {
    float: right;
    border: 2px solid white;
    margin: 5px 10px;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: bold;
    font-size: 12px;
    display: none;
    padding: 5px 10px;

}

.hide {
    display: none !important;
}
#view-row {
    display: inline;
}

/* modal css */
body {
    font-family: Arial, sans-serif;
}

#yesButton, #noButton{
    padding: 10px 20px;
    margin: 5px;
    cursor: pointer;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #fff;
    margin: auto;
    padding: 20px;
    border: 1px solid #888;
    width: 300px;
    text-align: center;
}

.message {
    color: black;
}

#detailsTable {
    padding: 5px;
}

/*ETL Sanity check table */
#statusTable {
    td, th {
        /* border: 1px solid #ddd; */
        padding: 8px;
    }
}
.green {
    background-color: #258d25;
    color: #fff;
    td {
        max-width:50px;
    }
}
.amber {
    /* background-color: #ff5300; */
    background-color: #ff7e00;
    color: #fff;
    td {
        max-width:50px;
    }
}
.red {
    background-color: #FF0000;
    color: #fff;
    td {
        max-width:50px;
    }
}
.blue {
    background-color: #7700ff;
    color: #fff;
    td {
        max-width:50px;
    }
}
/* .grey {
    background-color: #D9D9D9;
    color: #000;
    td {
        max-width:50px;
    }
} */
.grey {
    background-color: #888888a6;
    color: #fff;
    td {
        max-width: 50px;
    }
}

.yellow {
    background-color: yellow;
    color: #fff;
    td {
        max-width: 50px;
    }
}

#refresh-btn {
    margin-left: auto;
    display: block;
}


/* Tooltip styling */
.tooltip {
    display: none;
    position: absolute;
    background-color: #333;
    color: #fff;
    padding: 5px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
}
.tooltip.show {
    display: block;
}
/* Modal styling */
.etl_modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 50px;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
    justify-content: center;
    align-items: center;
}
.etl_modal-content {
    background-color: #fff;
    margin: auto;
    padding: 10px;
    border: 1px solid #888;
    width: 50%;
    height: 75%;
    text-align: center;
    overflow: auto;

    tr:hover {
        background-color: #555;
        color: #fff;
    }
}

#onboardingDatasetTable{
    td {
        padding: 5px;
        max-width: none;
        text-wrap: wrap;
        min-width: 55px;
    }
}

#closeModal1 {
    display: block;
    margin-top: 10px;
    padding: 10px;
    background: #F44336;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 82%;
}


