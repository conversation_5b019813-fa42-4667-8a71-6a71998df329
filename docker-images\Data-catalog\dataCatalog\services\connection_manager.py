from fastapi import <PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from typing import Dict, List

class ConnectionManager:
    def __init__(self):
        self.connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, subscriber_id: str):
        await websocket.accept()
        if subscriber_id not in self.connections:
            self.connections[subscriber_id] = []
        self.connections[subscriber_id].append(websocket)

    def disconnect(self, websocket: WebSocket, subscriber_id: str):
        if subscriber_id in self.connections:
            self.connections[subscriber_id].remove(websocket)
            if not self.connections[subscriber_id]:  # Clean up if empty
                del self.connections[subscriber_id]

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast_to_subscriber(self, message: str, subscriber_id: str):
        if subscriber_id in self.connections:
            for websocket in self.connections[subscriber_id]:
                await websocket.send_text(message)