source:
  type: airflow
  serviceName: airflow_source
  serviceConnection:
    config:
      type: Airflow
      hostPort: http://service-airflow:8080
      numberOfStatus: 15
      connection:
        type: Postgres
        username: $AIRFLOW_USERNAME
        authType:
          password: $AIRFLOW_PASSWORD
        database: airflow
        hostPort: service-postgres:5432
  sourceConfig:
    config:
      type: PipelineMetadata
      markDeletedPipelines: True
      includeTags: True
      includeLineage: true
sink:
  type: metadata-rest
  config: {}
workflowConfig:
  loggerLevel: DEBUG
  openMetadataServerConfig:
    hostPort: "http://$SERVICE_IP/api"
    authProvider: openmetadata
    securityConfig:
      jwtToken: $OPENMETADATA_JWT_TOKEN