import os
import pandas as pd
import numpy as np
import json
from annoy import AnnoyIndex
from ast import literal_eval
from sklearn.manifold import TSNE
from sklearn.cluster import KMeans
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.metrics import classification_report, accuracy_score, precision_recall_curve, mean_squared_error, mean_absolute_error
# import matplotlib.pyplot as plt
# import matplotlib.colors
from openai import OpenAI
import plotly.express as px
import plotly.io as pio
import plotly.graph_objects as go
import logging
# from gensim.models import Word2Vec
# import gensim
from nltk.tokenize import sent_tokenize, word_tokenize
import warnings
import json
from thefuzz import fuzz
warnings.filterwarnings(action='ignore')

class DataAnalyzer:
    def __init__(self, api_key, input_datapath, output_datapath, model="text-embedding-3-small", encoding="cl100k_base", max_tokens=8000, debug=False):
        self.api_key = api_key
        self.model = model
        self.encoding = encoding
        self.max_tokens = max_tokens
        self.df = None
        self.annoy_index = None
        self.input_datapath = input_datapath
        self.output_datapath = output_datapath

        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None

        self.clf = None

        self.debug = debug

        os.environ["OPENAI_API_KEY"] = api_key
        self.client = OpenAI()

    # def __init__(self, input_datapath, output_datapath, model="text-embedding-3-small", encoding="cl100k_base", max_tokens=8000, debug=False):
        
    #     self.model = model
    #     self.encoding = encoding
    #     self.max_tokens = max_tokens
    #     self.df = None
    #     self.annoy_index = None
    #     self.input_datapath = input_datapath
    #     self.output_datapath = output_datapath

    #     self.X_train = None
    #     self.X_test = None
    #     self.y_train = None
    #     self.y_test = None

    #     self.clf = None

    #     self.debug = debug

    def get_embedding(self, text):
        text = text.replace("\n", " ")
        return self.client.embeddings.create(input=[text], model=self.model).data[0].embedding

    def check_data_availability(self):
        raise NotImplementedError("The method 'check_data_availability' must be overridden in a subclass.")

    def load_or_create_embeddings(self, top_n=1000):
        raise NotImplementedError("The method 'load_or_create_embeddings' must be overridden in a subclass.")

    def build_annoy_index(self, embedding_dimension=1536, trees=10, metric='angular'):
        if self.debug:
            print("Building Annoy index...")
        self.annoy_index = AnnoyIndex(embedding_dimension, metric)
        for i, row in self.df.iterrows():
            embedding = row['embedding']
            if len(embedding) != embedding_dimension:
                raise ValueError(f"Embedding at index {i} has incorrect dimension {len(embedding)}; expected {embedding_dimension}.")
            self.annoy_index.add_item(i, embedding)
        self.annoy_index.build(trees)

    def search_annoy_embeddings(self, query_embedding, n=3):
        nearest_neighbors_ids = self.annoy_index.get_nns_by_vector(query_embedding, n, include_distances=True)
        nearest_neighbors = self.df.iloc[nearest_neighbors_ids[0]]
        nearest_neighbors['distance'] = nearest_neighbors_ids[1]
        return nearest_neighbors.sort_values('distance').to_json()
    
        
    def create_word_tokens(self):
        word_tokens = []

        for combined_text in self.df['combined']:
            combined_text = combined_text.replace("\n", " ")
            temp = []
            # tokenize the sentence into words
            for j in word_tokenize(combined_text):
                temp.append(j.lower())
            word_tokens.append(temp)
        return word_tokens

    def fuzzy_search(self, product_description, word_tokens, n=3):

        word_tokens2 = word_tokens.copy()
        max_arr=[]
        for i in range(len(word_tokens2)):
            temp_arr=[]
            for word in word_tokens2[i]:
                temp_arr.append(fuzz.ratio(product_description.lower(), word))
            max_arr.append(max(temp_arr))

        self.df['similarities'] = max_arr
        logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        logger = logging.getLogger(__name__)
        logger.info(self.df)
        return self.df.sort_values('similarities', ascending=False).head(n)

    # def search_embeddings_word2vec(self, product_description, word_tokens, n=3):

    #     word_tokens2 = word_tokens.copy()
    #     max_arr=[]
    #     for i in range(len(word_tokens2)):
    #         word_tokens2[i].append(product_description.lower())
    #         model1 = gensim.models.Word2Vec([word_tokens2[i]], min_count=1,vector_size=100, window=5)

    #         temp_arr=[]
    #         for word in word_tokens2[i]:
    #             temp_arr.append(model1.wv.similarity(product_description.lower(), word))
    #         max_arr.append(max(temp_arr[:-1]))

    #     self.df['similarities'] = max_arr
    #     logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
    #     logger = logging.getLogger(__name__)
    #     logger.info(self.df)
    #     return self.df.sort_values('similarities', ascending=False).head(n)

    def search_embeddings(self, product_description, n=3):
        embedding = self.get_embedding(product_description)
        self.df['similarities'] = self.df.embedding.apply(lambda x: self.cosine_similarity(x, embedding))
        logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        logger = logging.getLogger(__name__)
        logger.info(self.df)
        return self.df.sort_values('similarities', ascending=False).head(n)


    def cosine_similarity(self, a, b):
        a = np.array(a).astype(np.double)
        b = np.array(b).astype(np.double)
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

    def visualize_embeddings_with_tsne(self, score_column='Score', perplexity=30, n_components=2, init='random', learning_rate=200, random_state=42):
        matrix = np.array(self.df.embedding.tolist())
        tsne = TSNE(n_components=n_components, perplexity=perplexity, random_state=random_state, init=init, learning_rate=learning_rate)
        vis_dims = tsne.fit_transform(matrix)

        self.df['x'] = vis_dims[:, 0]
        self.df['y'] = vis_dims[:, 1]
        self.df['color'] = self.df[score_column]

        fig = px.scatter(
            self.df,
            x='x',
            y='y',
            color='color',
            title='Embeddings Visualized with t-SNE',
            labels={'color': score_column}
        )

        if self.debug:
            fig.show()

        return pio.to_json(fig)

    def split_data(self, test_size=0.2, random_state=42):
        if self.debug:
            print("Splitting data for the model training...")
        embeddings = np.array(self.df['embedding'].tolist())
        X_train, X_test, y_train, y_test = train_test_split(embeddings, self.df['Score'].values, test_size=test_size, random_state=random_state)
        self.X_train = X_train
        self.X_test = X_test
        self.y_train = y_train
        self.y_test = y_test

    def perform_regression(self):
        if self.debug:
            print("Performing regression model training...")
        if not hasattr(self, 'X_train'):
            raise Exception("Data not split yet. Call split_data() before performing regression.")

        rfr = RandomForestRegressor(n_estimators=100)
        rfr.fit(self.X_train, self.y_train)
        preds = rfr.predict(self.X_test)
        mse = mean_squared_error(self.y_test, preds)
        mae = mean_absolute_error(self.y_test, preds)
        
        if self.debug:
            print(f"Mean Absolute Error: {mae:.2f}, Mean Squared Error: {mse:.2f}")

        return json.dumps({"Mean Absolute Error": mae, "Mean Squared Error": mse})

    def train_classifier(self):
        self.clf = RandomForestClassifier(n_estimators=100)
        self.clf.fit(self.X_train, self.y_train)

    def evaluate_model(self):
        preds = self.clf.predict(self.X_test)
        report = classification_report(self.y_test, preds, output_dict=True)
        if self.debug:
            print(classification_report(self.y_test, preds))
        return json.dumps(report)

    def plot_multiclass_precision_recall(self):
        probas = self.clf.predict_proba(self.X_test)
        precision = dict()
        recall = dict()

        fig = go.Figure()

        for i, label in enumerate([1, 2, 3, 4, 5]):
            precision[label], recall[label], _ = precision_recall_curve(self.y_test == label, probas[:, i])
            fig.add_trace(go.Scatter(x=recall[label], y=precision[label], mode='lines', name=f'class {label}'))

        fig.update_layout(
            title="Precision vs. Recall curve",
            xaxis_title="Recall",
            yaxis_title="Precision",
            legend_title="Class"
        )
        
        if self.debug:
            fig.show()
    
        return pio.to_json(fig)
    

    def find_clusters(self, n_clusters=4, random_state=42):
        if self.debug:
            print("Clustering embeddings...")
        matrix = np.array(self.df.embedding.tolist())
        kmeans = KMeans(n_clusters=n_clusters, init="k-means++", random_state=random_state)
        self.df['Cluster'] = kmeans.fit_predict(matrix)
        
        if self.debug:
            print("Cluster scores:")
            print(self.df.groupby("Cluster").Score.mean().sort_values())

        return self.df.groupby("Cluster").Score.mean().sort_values().to_json()

    def visualize_clusters_with_tsne(self, perplexity=15, n_components=2, random_state=42, init="random", learning_rate=200):
        if self.debug:
            print("Visualizing clusters with t-SNE...")
        
        matrix = np.array(self.df.embedding.tolist())
        tsne = TSNE(n_components=n_components, perplexity=perplexity, random_state=random_state, init=init, learning_rate=learning_rate)
        vis_dims = tsne.fit_transform(matrix)

        self.df['x'] = vis_dims[:, 0]
        self.df['y'] = vis_dims[:, 1]

        fig = px.scatter(
            self.df,
            x='x',
            y='y',
            color='Cluster',
            title='Clusters Visualized with t-SNE',
            labels={'color': 'Cluster'}
        )

        if self.debug:
            fig.show()

        return pio.to_json(fig)

    def name_and_sample_clusters(self, n_samples=5):
        if 'Cluster' not in self.df.columns:
            raise ValueError("Clustering has not been performed on the dataset.")
        
        clusters_summary = {}
        
        for i in range(self.df['Cluster'].nunique()):
            if self.debug:
                print(f"Cluster {i} Theme:", end=" ")
                
            reviews = "\n".join(self.df[self.df.Cluster == i].combined.str.replace("Title: ", "").str.replace("; Content: ", ":  ").sample(n_samples, random_state=42).values)
            messages = [{"role": "user", "content": f"What do the following customer reviews have in common?\n\nCustomer reviews:\n\n{reviews}\n\nTheme:"}]
            response = self.client.chat.completions.create(model="gpt-4-0125-preview", temperature=0, messages=messages, max_tokens=4096, stream=True)
            response_text = ""
            for chunk in response:
                response_text += str(chunk.choices[0].delta.content)
            theme = response_text
            
            if self.debug:
                print(theme)
                
            sample_reviews = self.df[self.df.Cluster == i].sample(n_samples, random_state=42)
            cluster_reviews = []
            for index, review in sample_reviews.iterrows():
                review_summary = {
                    "Score": review.Score,
                    "Summary": review.Summary[:50],
                    "Review": review.Text[:100]
                }
                cluster_reviews.append(review_summary)
                
            clusters_summary[f"Cluster {i}"] = {
                "Theme": theme,
                "Sample Reviews": cluster_reviews
            }
        
        return json.dumps(clusters_summary, indent=4)
