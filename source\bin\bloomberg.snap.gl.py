import sys, os, glob, argparse
from strunner import *
setupEnvironment() # sets up environment
from jgdata import *
import stcommon
from stcommon.infra.python.module import function_from_path

parser = argparse.ArgumentParser()
parser.add_argument("--st", help="Please enter snap_time", required=True, type=str)
parser.add_argument("--tz", help="Please enter Time Zone", required=True, type=str)
options = parser.parse_args()

if options.st and options.tz:
   st=options.st
   tz=options.tz
   print(f"Parameter passed are: {st} {tz}")
else:
   print("Missing Arguments..")

dataset="bloomberg.snap"
import jgdata.datasets.bloomberg.snap


prefix = 'jgdata.datasets'
funct = function_from_path(f"{prefix}.{dataset}","buildBloombergSnaploadDataset")

funct(st,tz)
