FROM jupyter/pyspark-notebook:spark-3.5.0

USER root

RUN apt-get update && \
    apt-get install -y \
    python3 \
    python3-pip \
    wget \
    curl \
    vim \
    traceroute \
    inetutils-tools \
    fuse3 \
    findutils \
    unzip \
    alien \
    libkrb5-dev \
&& rm -rf /var/lib/apt/lists/*

# Install kubectl
RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" && \
    chmod +x kubectl && \
    mv kubectl /usr/local/bin/

RUN apt-get update
RUN apt install -y dbus
RUN apt install -y openjdk-8-jre
RUN apt-get update && apt-get install -y git

COPY docker-images/requirements_Jupyter.txt /home/<USER>/requirements_Jupyter.txt
RUN pip install -r /home/<USER>/requirements_Jupyter.txt

RUN mkdir -p /home/<USER>
    chown -R 1001:0 /home/<USER>
    chmod -R 777 /home/<USER>
    chown -R 1001:0 /home/<USER>
    chmod -R 777 /home/<USER>

RUN mkdir -p /home/<USER>/work && \ 
    chown -R 1001:0 /home/<USER>/work && \
    chmod -R 777 /home/<USER>/work && \
    mkdir -p /home/<USER>/jars && \
    chown -R 1001:0 /home/<USER>/jars && \
    chmod -R 777 /home/<USER>/jars

RUN curl -L -o /home/<USER>/jars/iceberg-spark-extensions.jar https://repo1.maven.org/maven2/org/apache/iceberg/iceberg-spark-extensions-3.5_2.12/1.4.2/iceberg-spark-extensions-3.5_2.12-1.4.2.jar && \
    curl -L -o /home/<USER>/jars/iceberg-spark-runtime.jar https://repo1.maven.org/maven2/org/apache/iceberg/iceberg-spark-runtime-3.5_2.12/1.4.2/iceberg-spark-runtime-3.5_2.12-1.4.2.jar && \ 
    curl -L -o /home/<USER>/jars/hadoop-aws.jar https://repo.maven.apache.org/maven2/org/apache/hadoop/hadoop-aws/3.2.2/hadoop-aws-3.2.2.jar && \
    curl -L -o /home/<USER>/jars/aws-java-bundle.jar https://repo1.maven.org/maven2/com/amazonaws/aws-java-sdk-bundle/1.11.375/aws-java-sdk-bundle-1.11.375.jar
    
ENV SPARK_DIST_CLASSPATH $SPARK_DIST_CLASSPATH:/home/<USER>/jars/iceberg-spark-runtime.jar:/home/<USER>/jars/iceberg-spark-extensions.jar:/home/<USER>/jars/hadoop-aws.jar:/home/<USER>/jars/aws-java-bundle.jar

COPY /docker-images/cwiqfs/fuse-libs-2.9.2-11.el7.x86_64.rpm  /tmp
COPY /docker-images/cwiqfs/fuse-sshfs-2.10-1.el7.x86_64.rpm /tmp

COPY docker-images/cwiqfs/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm /tmp
RUN  alien -i /tmp/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm
RUN ln -sf /etc/ssl/certs/ca-bundle.crt /etc/ssl/certs/ca-certificates.crt
COPY docker-images/cwiqfs/cwiqfs.yaml /etc/cwiq/cwiqfs/config_temp.yaml
RUN sed -e "s/\${USERNAME}/$USERNAME/g" -e "s/\${LOGIN_KEY}/$LOGIN_KEY/g" /etc/cwiq/cwiqfs/config_temp.yaml > /etc/cwiq/cwiqfs/config.yaml
RUN sed -i -e's/# user_allow_other/user_allow_other/g' /etc/fuse.conf
RUN mkdir /jfs
RUN useradd jsvc-tech1

RUN mkdir -p /var/tmp/cache /var/log/cwiq && chmod 777 /var/tmp/cache
COPY docker-images/docker_conf/jupyter/start_notebook.sh /home/<USER>/start_notebook.sh
RUN chmod +x /home/<USER>/start_notebook.sh
USER 1001

RUN cd /home/<USER>/
USER root

ENTRYPOINT ["sh", "/home/<USER>/start_notebook.sh"]