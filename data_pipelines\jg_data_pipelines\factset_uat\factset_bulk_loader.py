
import os
import logging
import snowflake.connector
import pandas as pd
from pathlib import Path
from typing import Optional, Dict, Any
# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)
logging.getLogger('snowflake.connector').setLevel(logging.WARNING)

class SnowflakeBulkLoader:

    __SF_ACCOUNT = "byb06077.us-east-1"
    __SF_WAREHOUSE = None
    __SF_DATABASE = None
    __SF_ROLE = None
    __SF_SCHEMA = None

    def __init__(
        self,
        database: str = None,
        warehouse: str = None,
        role: str = None,
        schema: str = None,
    ):
        if database:
            self.__SF_DATABASE = database

        if warehouse:
            self.__SF_WAREHOUSE = warehouse

        if role:
            self.__SF_ROLE = role
        
        if schema:
            self.__SF_SCHEMA = schema

        """Initialize Snowflake connection."""
        sf_pk_file = os.getenv('SF_PK_FILE')
        sf_pk_password = os.getenv('SF_PK_PASSWORD')
        sf_username = os.getenv('SF_USERNAME')
        sf_password = os.getenv('SF_PASSWORD')
        pk_file_auth = sf_username is not None and sf_pk_file is not None \
            and sf_pk_password is not None
        user_pass_auth = sf_username is not None \
            and sf_password is not None
        if not pk_file_auth and not user_pass_auth:
                raise ValueError('You must either provide (SF_USERNAME, SF_PK_FILE, SF_PK_PASSWORD)' \
                                    'or (SF_USERNAME, SF_PASSWORD) env vars for Snowflake auth')
        elif pk_file_auth:
            logging.info("Snowflake authentication using Private Key File...")
            sf_password = None
        elif user_pass_auth:
            # logging.info("Snowflake authentication using User/Password...")
            sf_pk_file = None
            sf_pk_password = None
        self.conn = snowflake.connector.connect( 
            user = sf_username,
            password = sf_password,
            private_key_file=sf_pk_file,
            private_key_file_pwd=sf_pk_password,
            account = self.__SF_ACCOUNT,
            warehouse = self.__SF_WAREHOUSE,
            database = self.__SF_DATABASE,
            schema = schema,
            role = role)
        self.cursor = self.conn.cursor()

    def put_file(self, local_file_path: str, stage_name: str, stage_path:str="", auto_compress=True) -> None:
        """Upload local file to specified stage."""
        if auto_compress:
            sql = f"PUT 'file://{local_file_path}' @{stage_name}/{stage_path} AUTO_COMPRESS=TRUE;"
        else:
            sql = f"PUT 'file://{local_file_path}' @{stage_name}/{stage_path}"
        self.cursor.execute(sql)

    def load_file_with_mtime(
        self,
        table_name: str,
        stage_name: str,
        file_name: str,
        delimiter: str = ",",
        num_columns: int=None,
        mtime: str=None,
        error_on_column_count_mismatch:bool=True,
        file_version: int=None
    ) -> Dict[str, Any]:
        """
        Load staged file into table using COPY command.
        Returns load statistics.
        """
        column_list=', '.join([f"${i}" for i in range(1,num_columns+1)])
        copy_sql = f"""
        COPY INTO {table_name} from (select {column_list}, '{file_name}','{mtime}', {file_version}
        FROM '@{stage_name}/{file_name}')
        FILE_FORMAT = (TYPE = 'CSV', SKIP_HEADER = 1, FIELD_DELIMITER = '{delimiter}', FIELD_OPTIONALLY_ENCLOSED_BY='"', TRIM_SPACE=TRUE, REPLACE_INVALID_CHARACTERS = TRUE, ERROR_ON_COLUMN_COUNT_MISMATCH = {error_on_column_count_mismatch})
        ;
        """
        # logging.info(copy_sql)
        self.cursor.execute(copy_sql)
        copy_results = self.cursor.fetchall()

        return {
            'copy_results': copy_results,
        }
    
    def load_file(
        self,
        table_name: str,
        stage_name: str,
        file_name: str,
        delimiter: str = ",",
        error_on_column_count_mismatch:bool=True,
        columns: Optional[list] = None
    ) -> Dict[str, Any]:
        """
        Load staged file into table using COPY command.
        Returns load statistics.
        """
        columns_clause = f"({','.join(columns)})" if columns else ""
        
        copy_sql = f"""
        COPY INTO {table_name} {columns_clause}
        FROM '@{stage_name}/{file_name}'
        FILE_FORMAT = (TYPE = 'CSV', SKIP_HEADER = 1, FIELD_DELIMITER = '{delimiter}', FIELD_OPTIONALLY_ENCLOSED_BY='"', TRIM_SPACE=TRUE, REPLACE_INVALID_CHARACTERS = TRUE, ERROR_ON_COLUMN_COUNT_MISMATCH = {error_on_column_count_mismatch})
        ;
        """
        self.cursor.execute(copy_sql)
        copy_results = self.cursor.fetchall()

        return {
            'copy_results': copy_results,
        }
    
    def load_path(
        self,
        table_name: str,
        stage_name: str,
        stage_path: str,
        columns: Optional[list] = None
    ) -> Dict[str, Any]:
        """
        Load staged path into table using COPY command.
        Returns load statistics.
        """
        columns_clause = f"({','.join(columns)})" if columns else ""
        
        copy_sql = f"""
        COPY INTO {table_name} {columns_clause}
        FROM '@{stage_name}/{stage_path}'
        FILE_FORMAT = (SKIP_HEADER = 1);
        """
        self.cursor.execute(copy_sql)
        copy_results = self.cursor.fetchall()

        return {
            'copy_results': copy_results,
        }

    def load_csv(
        self,
        csv_path: str,
        table_name: str,
        stage_name: str,
        columns: Optional[list] = None
    ) -> Dict[str, Any]:
        """
        Complete process to load a local CSV file into Snowflake table.
        Assumes stage and file format already exist.
        """
        # Get file name from path
        file_name = Path(csv_path).name

        # Upload file to stage
        self.put_file(csv_path, stage_name)

        # Load data into table
        results = self.load_file(table_name, stage_name, file_name, columns)

        return results

    def load_generic(
        self,
        table_name: str,
        stage_name: str,
        stage_path: str,
        file_format: str,
        columns: Optional[list] = None
    ) -> Dict[str, Any]:
        """
        Load staged file into table using COPY command.
        Returns load statistics.
        """
        columns_clause = f"({','.join(columns)})" if columns else ""
        
        copy_sql = f"""
        COPY INTO {table_name} {columns_clause}
        FROM '@{stage_name}/{stage_path}'
        FILE_FORMAT = {file_format};
        """
        self.cursor.execute(copy_sql)
        copy_results = self.cursor.fetchall()

        return {
            'copy_results': copy_results,
        }

    def cleanup_stg(self, stage_name, stage_path):
        cleanup_sql = f"""
        REMOVE @{stage_name}/{stage_path};
        """
        self.cursor.execute(cleanup_sql)

    def cleanup(self) -> None:
        """Close connections and clean up resources."""
        self.cursor.close()
        self.conn.close()