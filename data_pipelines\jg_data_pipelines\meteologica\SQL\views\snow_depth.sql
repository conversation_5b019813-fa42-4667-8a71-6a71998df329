create or replace view meteologica.api.VW_SNOWDEPTH_REANALYSIS as (
    with consolidated as (
        SELECT * FROM meteologica.api.reanalysis
        WHERE issue_date < DATE('2025-01-01')
        UNION ALL 
        SELECT * FROM meteologica.ongoing.reanalysis
        WHERE issue_date >= DATE('2025-01-01')
    ), base as (
        select content_id, issue_date, TO_TIMESTAMP(to_varchar(data:"Date yyyy-mm-dd"), 'YYYY-MM-DD') timestamp, data
         from consolidated WHERE CONTENT_ID IN (1,2,3,4,1521, 1522)
        --AND TO_TIMESTAMP_NTZ(data:"Date yyyy-mm-dd", 'YYYY-MM-DD')
        --and (date(ISSUE_DATE) in ('2023-01-06', '2023-01-06', '2023-01-08'))
    ), ranked as (
        select row_number() over (partition by content_id, timestamp order by issue_date desc) r,
        * from base
        order by content_id, issue_date, timestamp
    ), deduped as(
        select * from ranked where r = 1
    ), observations as (
        select content_id, issue_date, timestamp, to_number(value) observation
        from deduped,
        LATERAL FLATTEN(INPUT => data) f1
        where regexp_like(key, 'Observation ' || year(timestamp))
    )
    select d.content_id, d.issue_date, d.timestamp, observation, data from deduped d
        left join observations o on (
            d.content_id = o.content_id
            and d.issue_date = o.issue_date
            and d.timestamp = o.timestamp
        )
);

select * from meteologica.api.VW_SNOWDEPTH_REANALYSIS
--where regexp_like(key, 'Observation ' || year(timestamp))
order by content_id, issue_date
;
