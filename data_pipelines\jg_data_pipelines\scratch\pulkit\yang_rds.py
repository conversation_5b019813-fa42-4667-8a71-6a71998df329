import os
from utils.postgres.adaptor import PostgresAdaptor

if __name__ == "__main__":
    loader = PostgresAdaptor(
        host="tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com",
        database="postgres",
        schema="shared",
        user="",
        password="",
    )

    df_eod_pnl = loader.execute_query("""select day, COUNT(1) as  Cnt from shared.v_eod_pnl_yzhang vepy group by day order by 1""")
    print(df_eod_pnl)