USE SCHEMA ONGOING;

create or replace TAB<PERSON> KPLER.ONGOING.ACTUAL_LOAD (
	TIMESTAMP TIMESTAMP_NTZ(9),
	PROVIDER VARCHAR(16777216),
	COUNTRY VARCHAR(16777216),
	S<PERSON><PERSON><PERSON>UNTRY VARCHAR(16777216),
	DEMAND FLOAT,
    JSON_FILE VARCHAR
);

CREATE STAGE KPLER.ONGOING.ACTUAL_LOAD_STAGE; GRANT READ, WRITE ON STAGE KPLER.ONGOING.ACTUAL_LOAD_STAGE TO ROLE DR_KPLER_WRITER;

create or replace <PERSON>AB<PERSON> KPLER.ONGOING.AVAILABILITY_PER_UNITS (
	TIMESTAMP TIMESTAMP_NTZ(9),
	AVAILABILITY_AMOUNT FLOAT,
	TYPE VARCHAR(16777216),
	AS_OF TIMESTAMP_NTZ(9),
	PROVIDER VARCHAR(16777216),
	COUNTRY VARCHAR(16777216),
	UNIT VARCHA<PERSON>(16777216),
	UNIT_NAME <PERSON>RC<PERSON>R(16777216),
	GENERATION_CODE VARCHAR(16777216),
	PRODUCTION_CODE VARCHAR(16777216),
	ASSET_TYPE VARCHAR(16777216),
	FUEL_TYPE VARCHAR(16777216),
	TIMEZONE VARCHAR(16777216),
	JSON_FILE VARCHAR(16777216)
);

CREATE STAGE KPLER.ONGOING.AVAILABILITY_PER_UNITS_STAGE; GRANT READ, WRITE ON STAGE KPLER.ONGOING.AVAILABILITY_PER_UNITS_STAGE TO ROLE DR_KPLER_WRITER;

create or replace TABLE KPLER.ONGOING.AVAILABILITY_BY_FUELTYPE (
	TIMESTAMP TIMESTAMP_NTZ(9),
	AVAILABILITY_AMOUNT FLOAT,
	AS_OF TIMESTAMP_NTZ(9),
	PROVIDER VARCHAR(16777216),
	COUNTRY VARCHAR(16777216),
	TIMEZONE VARCHAR(16777216),
	LEVEL VARCHAR(16777216),
	FUEL_TYPE VARCHAR(16777216),
	JSON_FILE VARCHAR(16777216)
);

CREATE STAGE KPLER.ONGOING.AVAILABILITY_BY_FUELTYPE_STAGE; GRANT READ, WRITE ON STAGE KPLER.ONGOING.AVAILABILITY_BY_FUELTYPE_STAGE TO ROLE DR_KPLER_WRITER;

create or replace TABLE KPLER.ONGOING.COMMERCIAL_SCHEDULES (
	TIMESTAMP TIMESTAMP_NTZ(9),
	PROVIDER VARCHAR(16777216),
	AREA_TYPE VARCHAR(16777216),
	MARKET_TYPE VARCHAR(16777216),
	SOURCE_COUNTRY VARCHAR(16777216),
	TARGET_COUNTRY VARCHAR(16777216),
	VALUE FLOAT,
	JSON_FILE VARCHAR(16777216)
);

CREATE STAGE KPLER.ONGOING.COMMERCIAL_SCHEDULES_STAGE; GRANT READ, WRITE ON STAGE KPLER.ONGOING.COMMERCIAL_SCHEDULES_STAGE TO ROLE DR_KPLER_WRITER;

create or replace TABLE KPLER.ONGOING.GENERATION_BY_FUELTYPE (
	TIMESTAMP TIMESTAMP_NTZ(9),
	PROVIDER VARCHAR(16777216),
	COUNTRY VARCHAR(16777216),
	FUEL_TYPE VARCHAR(16777216),
	GENERATION FLOAT,
	JSON_FILE VARCHAR(16777216)
);

CREATE STAGE KPLER.ONGOING.GENERATION_BY_FUELTYPE_STAGE; GRANT READ, WRITE ON STAGE KPLER.ONGOING.GENERATION_BY_FUELTYPE_STAGE TO ROLE DR_KPLER_WRITER;

create or replace TABLE KPLER.ONGOING.GENERATION_BY_UNIT (
	TIMESTAMP TIMESTAMP_NTZ(9),
	PROVIDER VARCHAR(16777216),
	COUNTRY VARCHAR(16777216),
	ASSET_ID VARCHAR(16777216),
	GENERATION FLOAT,
	UNIT_NAME VARCHAR(16777216),
	ASSET_TYPE VARCHAR(16777216),
	FUEL_TYPE VARCHAR(16777216),
	JSON_FILE VARCHAR(16777216)
);

CREATE STAGE KPLER.ONGOING.GENERATION_BY_UNIT_STAGE; GRANT READ, WRITE ON STAGE KPLER.ONGOING.GENERATION_BY_UNIT_STAGE TO ROLE DR_KPLER_WRITER;

create or replace TABLE KPLER.ONGOING.CONSUMPTION_PER_FUELTYPE (
	TIMESTAMP TIMESTAMP_NTZ(9),
	PROVIDER VARCHAR(16777216),
	COUNTRY VARCHAR(16777216),
	SUBCOUNTRY VARCHAR(16777216),
	FUEL_TYPE VARCHAR(16777216),
	CONSUMPTION FLOAT,
	JSON_FILE VARCHAR(16777216)	
);

CREATE STAGE KPLER.ONGOING.CONSUMPTION_PER_FUELTYPE_STAGE; GRANT READ, WRITE ON STAGE KPLER.ONGOING.CONSUMPTION_PER_FUELTYPE_STAGE TO ROLE DR_KPLER_WRITER;

create or replace TABLE KPLER.ONGOING.FORECAST_LOAD (
	TIMESTAMP TIMESTAMP_NTZ(9),
	RUNDATE DATE,
	KIND VARCHAR(16777216),
	MODEL VARCHAR(16777216),
	COUNTRY VARCHAR(16777216),
	PROVIDER VARCHAR(16777216),
	RUN_00Z FLOAT,
	RUN_06Z FLOAT,
	RUN_12Z FLOAT,
	RUN_18Z FLOAT,
	JSON_FILE VARCHAR(16777216)
);

CREATE STAGE KPLER.ONGOING.FORECAST_LOAD_STAGE; GRANT READ, WRITE ON STAGE KPLER.ONGOING.FORECAST_LOAD_STAGE TO ROLE DR_KPLER_WRITER;