apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    io.kompose.service: jupyter-pyspark
  name: jupyter-pyspark
  namespace: jupyter
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      io.kompose.service: jupyter-pyspark
  strategy:
    type: Recreate
  template:
    metadata:
      annotations:
        kompose.cmd: kompose.exe convert
        kompose.version: 1.31.2 (HEAD)
      creationTimestamp: null
      labels:
        io.kompose.network/docker-airflow-spark-master-default-net: "true"
        io.kompose.service: jupyter-pyspark
    spec:
      containers:
      - image: #ACCOUNT_ID#.dkr.ecr.#AWS_REGION#.amazonaws.com/#PREFIX#-ecr-pyspark-notebook-#ENV#:latest
        imagePullPolicy: Always
        name: jupyter-pyspark
        ports:
        - containerPort: 8888
          hostPort: 8888
          protocol: TCP
        resources: {}
        securityContext:
          capabilities:
            add: ["SYS_ADMIN"]
          privileged: true
          runAsUser: 0
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /home/<USER>/work
          name: jupyter-pyspark-claim0
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      volumes:
      - name: jupyter-pyspark-claim0
        persistentVolumeClaim:
          claimName: jupyter-pyspark-claim0