raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/eex/market_data_parsed/1.0/power"
  s3_prefix: "eex/market_data"
  include_prefix: false
  structure: '[
    "PowerFutureHistory*$DATE$.xlsx_*.csv",
    "PowerOptionHistory*$DATE$.xlsx_*.csv",
    "FutureStylePowerOptionHistory*$DATE$.xlsx_*.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "EEX_MARKET_DATA"

  table_map:
    # options
    POWER_DERIVS_OPTION_PRICES_RAW:
      pattern: "PowerOptionHistory.*$DATE$.*PRICES.*csv" 
      col_num: 25
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/market_data/" 
      file_format: "FF_EEX_MARKET_DATA_PARSED"

    POWER_DERIVS_OPTION_EXCH_TRADED_INFO_RAW:
      pattern: "PowerOptionHistory.*$DATE$.*Exchange_Options_Total.*csv" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/market_data/" 
      file_format: "FF_EEX_MARKET_DATA_PARSED"

    POWER_DERIVS_OPTION_TRADEREG_RAW:
      pattern: "PowerOptionHistory.*$DATE$.*TradeReg.*csv" 
      col_num: 7
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/market_data/" 
      file_format: "FF_EEX_MARKET_DATA_PARSED"

    # futures
    POWER_DERIVS_FUTURE_PRICES_RAW:
      pattern: "PowerFutureHistory.*$DATE$.*PRICES.*csv" 
      col_num: 22
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/market_data/" 
      file_format: "FF_EEX_MARKET_DATA_PARSED"

    POWER_DERIVS_FUTURE_EXCH_TRADED_INFO_RAW:
      pattern: "PowerFutureHistory.*$DATE$.*Exchange.*csv" 
      col_num: 9
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/market_data/" 
      file_format: "FF_EEX_MARKET_DATA_PARSED"

    POWER_DERIVS_FUTURE_TRADEREG_RAW:
      pattern: "PowerFutureHistory.*$DATE$.*TradeRegistration.*csv" 
      col_num: 7
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/market_data/" 
      file_format: "FF_EEX_MARKET_DATA_PARSED"

    # future-style options
    POWER_DERIVS_FUTSTYLEOPTION_PRICES_RAW:
      pattern: "FutureStylePowerOptionHistory.*$DATE$.*PRICES.*csv" 
      col_num: 25
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/market_data/" 
      file_format: "FF_EEX_MARKET_DATA_PARSED"

    POWER_DERIVS_FUTSTYLEOPTION_EXCH_TRADED_INFO_RAW:
      pattern: "FutureStylePowerOptionHistory.*$DATE$.*Exchange.*csv" 
      col_num: 10
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/market_data/" 
      file_format: "FF_EEX_MARKET_DATA_PARSED"

    POWER_DERIVS_FUTSTYLEOPTION_TRADEREG_RAW:
      pattern: "FutureStylePowerOptionHistory.*$DATE$.*TradeReg.*csv" 
      col_num: 7
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "eex/market_data/" 
      file_format: "FF_EEX_MARKET_DATA_PARSED"

    