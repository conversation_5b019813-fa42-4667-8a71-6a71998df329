create or replace view vw_reanalysis as
with consolidated as (
    SELECT * FROM meteologica.api.reanalysis
    WHERE issue_date < DATE('2025-01-01')
    UNION ALL 
    SELECT * FROM meteologica.ongoing.reanalysis
    WHERE issue_date >= DATE('2025-01-01')
), reanalysis_data as(
    select content_id, content_name, 
    issue_date,
    timezone, unit, installed_capacity, update_id, source_file,
        to_varchar(data:"From yyyy-mm-dd hh:mm") from_datetime,
        to_varchar(data:"To yyyy-mm-dd hh:mm") to_datetime,
        to_varchar(data:"UTC offset from (UTC+/-hhmm)") utc_offset_from,
        to_varchar(data:"UTC offset to (UTC+/-hhmm)") utc_offset_to,
        to_varchar(data:"InsCapForecast") InsCapForecast,
        to_varchar(data:"forecast") forecast,
        from consolidated
), reanalysis_utc as (
    select content_id, content_name, issue_date, unit, installed_capacity, update_id, source_file, InsCapForecast, forecast,
    to_timestamp_ntz(convert_timezone(
            'UTC', 
            TO_TIMESTAMP_TZ(FROM_DATETIME || ' ' || split(utc_offset_from, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
        )) FROM_DATETIME,
    to_timestamp_ntz(convert_timezone(
            'UTC', 
            TO_TIMESTAMP_TZ(TO_DATETIME || ' ' || split(utc_offset_to, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
        )) TO_DATETIME
        from reanalysis_data
)
select * from reanalysis_utc;


select count(*) from vw_reanalysis limit 100;
select * from vw_reanalysis limit 100;