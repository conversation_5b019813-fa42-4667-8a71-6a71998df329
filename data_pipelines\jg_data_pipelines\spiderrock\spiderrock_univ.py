import os
import requests
import pandas as pd
import json
import psycopg2
import logging
from collections import defaultdict
from io import StringIO
from utils.date_utils import get_now

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

base_path = os.path.dirname(__file__)
with open(f"{base_path}/conf/spiderrock_api.json", "r") as f:
    config = json.load(f)

def refresh_spiderrock_tickers(message_config):
    params = message_config["params"].copy()
    response = requests.get(config["api_url"], params=params)
    if response.status_code != 200:
        raise ValueError(f"API request failed ({response.status_code}): {response.text}")
    
    try:
        raw_data = response.json()
        rows = []
        for item in raw_data:
            msg = item.get("message", {})
            ticker_obj = msg.get("pkey", {}).get("ticker", {})
            rows.append({
                "ticker": ticker_obj.get("tk"),
                "assettype": ticker_obj.get("at"),
                "tickersource": ticker_obj.get("ts"),
                "bbgcompositeticker": msg.get("bbgCompositeTicker"),
                "symboltype": msg.get("symbolType"),
                "name": msg.get("name"),
                "primaryexch": msg.get("primaryExch"),
                "country": msg.get("country"),
                "mic": msg.get("mic"),
                "symbol": msg.get("symbol"),
                "bbgexchangeticker": msg.get("bbgExchangeTicker"),
                "isin": msg.get("isin"),
                "figi": msg.get("figi"),
                "gics": msg.get("gics"),
                "naics": msg.get("naics"),
                "numoptions": msg.get("numOptions"),
                "bbgcompositeglobalid": msg.get("bbgCompositeGlobalID"),
                "bbgglobalid": msg.get("bbgGlobalID"),
                "bbgcurrency": msg.get("bbgCurrency"),
                "sr_timestamp": msg.get("timestamp"),
            })
        df = pd.DataFrame(rows)
        df.dropna(subset=["ticker", "assettype", "tickersource"], inplace=True)

        if df.shape[0] < 1:
            raise ValueError("No valid data found in the response.")
        
        df["numoptions"] = df["numoptions"].astype("Int64")
        df["when_updated_utc"] = get_now("UTC")

        pg_host = config["pg_host"]
        pg_username = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"]
        pg_password = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"]
        
        buffer = StringIO()
        df.to_csv(buffer, index=False, header=True)
        buffer.seek(0)
        column_list = ", ".join(df.columns)        
        conn_string = f"host={pg_host} dbname=fe_risk user={pg_username} password={pg_password} port=5432"
        with psycopg2.connect(conn_string) as conn:
            with conn.cursor() as cursor:
                cursor.execute("SET work_mem TO '1GB'")
                cursor.execute("TRUNCATE TABLE eqvol.sr_ticker_definition")
                copy_sql = f"""COPY eqvol.sr_ticker_definition ({column_list}) FROM STDIN WITH (FORMAT CSV, HEADER)"""
                cursor.copy_expert(copy_sql, buffer)

            conn.commit()
    except Exception as e:
        raise ValueError(f"Error processing response: {str(e)}")
        
def refresh_spiderrock_roots(message_config):
    params = message_config["params"].copy()
    response = requests.get(config["api_url"], params=params)
    if response.status_code != 200:
        raise ValueError(f"API request failed ({response.status_code}): {response.text}")
    
    try:
        raw_data = response.json()
        rows = []
        for item in raw_data:
            msg = item.get("message", {})
            root_obj = msg.get("pkey", {}).get("root", {})
            ticker_obj = msg.get("ticker", {})
            rows.append({
                "root": root_obj.get("tk"),
                "assettype": root_obj.get("at"),
                "tickersource": root_obj.get("ts"),
                "ticker": ticker_obj.get("tk"),
                "osi_root": msg.get("osiRoot"),
                "option_type": msg.get("optionType"),
                "multi_hedge": msg.get("multiHedge"),
                "exercise_time": msg.get("exerciseTime"),
                "exercise_type": msg.get("exerciseType"),
                "time_metric": msg.get("timeMetric"),
                "trading_period": msg.get("tradingPeriod"),
                "pricing_model": msg.get("pricingModel"),
                "moneyness_type": msg.get("moneynessType"),
                "price_quote_type": msg.get("priceQuoteType"),
                "volume_tier": msg.get("volumeTier"),
                "position_limit": msg.get("positionLimit"),
                "exchanges": msg.get("exchanges"),
                "tick_value": msg.get("tickValue"),
                "point_value": msg.get("pointValue"),
                "point_currency": msg.get("pointCurrency"),
                "strike_scale": msg.get("strikeScale"),
                "strike_ratio": msg.get("strikeRatio"),
                "cash_on_exercise": msg.get("cashOnExercise"),
                "premium_mult": msg.get("premiumMult"),
                "symbol_ratio": msg.get("symbolRatio"),
                "adj_convention": msg.get("adjConvention"),
                "opt_price_inc": msg.get("optPriceInc"),
                "price_format": msg.get("priceFormat"),
                "min_tick_size": msg.get("minTickSize"),
                "trade_curr": msg.get("tradeCurr"),
                "settle_curr": msg.get("settleCurr"),
                "strike_curr": msg.get("strikeCurr"),
                "ric_root": msg.get("ricRoot"),
                "bbg_root": msg.get("bbgRoot"),
                "bbg_group": msg.get("bbgGroup"),
                "timestamp": msg.get("timestamp"),
            })
            
        df = pd.DataFrame(rows)
        df.dropna(subset=["root", "assettype", "tickersource"], inplace=True)

        if df.shape[0] < 1:
            raise ValueError("No valid data found in the response.")
        
        df["position_limit"] = df["position_limit"].astype("Int64")
        df["when_updated_utc"] = get_now("UTC")

        pg_host = config["pg_host"]
        pg_username = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"]
        pg_password = os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"]
        
        buffer = StringIO()
        df.to_csv(buffer, index=False, header=True)
        buffer.seek(0)
        column_list = ", ".join(df.columns)        
        conn_string = f"host={pg_host} dbname=fe_risk user={pg_username} password={pg_password} port=5432"
        with psycopg2.connect(conn_string) as conn:
            with conn.cursor() as cursor:
                cursor.execute("SET work_mem TO '1GB'")
                cursor.execute("TRUNCATE TABLE eqvol.sr_root_definition")
                copy_sql = f"""COPY eqvol.sr_root_definition ({column_list}) FROM STDIN WITH (FORMAT CSV, HEADER)"""
                cursor.copy_expert(copy_sql, buffer)

            conn.commit()
    
    except Exception as e:
        raise ValueError(f"Error processing response: {str(e)}")
    

if __name__ == "__main__":
    message_config = config["message_types"]["TickerDefinition"]
    refresh_spiderrock_tickers(message_config)
    logger.info("Successfully updated sr_ticker_definition")

    message_config = config["message_types"]["RootDefinition"]
    refresh_spiderrock_roots(message_config)
    logger.info("Successfully updated sr_root_definition")