[Unit]
Description=Reset SpiderRock Partitions.
After=network.target

[Service]
Type=oneshot
EnvironmentFile=/jfs/tech1/apps/datait/jg-code/secure/prod/jg_data_pipelines.env
ExecStart=/bin/bash -c ' \
export MAMBA_EXE="/jfs/tools/conda/exec/micromamba"; \
export MAMBA_ROOT_PREFIX="/jfs/tech1/conda/"; \
__mamba_setup="$("$MAMBA_EXE" shell hook --shell bash --root-prefix "$MAMBA_ROOT_PREFIX" 2> /dev/null)"; \
eval "$__mamba_setup"; \
unset __mamba_setup; \
micromamba activate tech1-datait-analytics-light; \
python /jfs/tech1/apps/datait/jg-code/prod/JG-Data-Pipelines/jg_data_pipelines/spiderrock/reset_spiderrock_partitions.py >> /opt/data/process_logs/fe_risk_reset_sr_partitions_$(date +%%Y%%m%%d).log 2>&1'
Restart=no

[Install]
WantedBy=multi-user.target