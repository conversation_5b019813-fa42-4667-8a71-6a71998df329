import psycopg2
from datetime import datetime, timedelta
import snowflake.connector
import os, json, logging
import pandas as pd
from strunner import *
setupEnvironment()
import stcommon
import jglib.infra.python.fileio as fio
from stcommon.email_util_k8s import EmailUtility

logger = logging.getLogger()
logging.basicConfig(level=logging.INFO)

objconfig = {}
objconfig = fio.read_config_secrets()

# Connect to the PostgreSQL database
pg_conn = psycopg2.connect(
    host=objconfig['pg_host'],
    database="airflow",
    user=objconfig['pg_user'],
    password=objconfig['pg_password']
)
pg_query = f'''
    with cron_parts AS (
         SELECT dag_id,schedule_interval, unnest(string_to_array(schedule_interval, ' ')) AS part,generate_series(1, 5) AS part_index,
                substring(source_code, 
                          position('--tz "' in source_code)+6, 
                          position(E'"' in substring(source_code from position('--tz "' in source_code)+6))-1) AS timezone
         FROM   airflow.public.dag d
         JOIN  dag_code dc ON d.fileloc = dc.fileloc 
         WHERE is_active AND not is_paused AND schedule_interval IS NOT null AND dag_id like '%bloombers%' AND strpos(source_code, 'bloomberg.snap.gl.py') > 0
         ),
         parsed_cron AS (
         SELECT dag_id, MAX(CASE WHEN part_index = 2 THEN part END) AS hours, replace(MAX(CASE WHEN part_index = 1 THEN part END),'\"','') AS minutes,timezone
         FROM   cron_parts
         GROUP BY dag_id,timezone
         )
    SELECT CONCAT(LPAD(hours::text, 2, '0'),LPAD(minutes::text, 2, '0')) SNAP_TIME, timezone TIMEZONE
    FROM   parsed_cron pc
    ORDER BY 2,1
'''
df_pg = pd.read_sql(pg_query, pg_conn)

sf_conn = snowflake.connector.connect(
    user = objconfig['sf_user'],
    password = objconfig['sf_password'],
    account = objconfig['sf_account'],
    warehouse = objconfig['sf_bloomberg_warehouse'],
    database = objconfig['sf_bloomberg_database'],
    schema = objconfig['sf_bloomberg_schema'],
    role = objconfig['sf_bloomberg_owner']
)
sf_query="SELECT DISTINCT SNAP_TIME, TIMEZONE  FROM BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG WHERE SNAP_TIME NOT IN  ('NA','') ORDER BY 2,1"
df_sf = pd.read_sql(sf_query, sf_conn)

df_exceptions = df_sf.merge(df_pg, how='left', right_on=['snap_time','timezone'], left_on=['SNAP_TIME','TIMEZONE'], indicator=True).query("_merge == 'left_only'")[['SNAP_TIME','TIMEZONE']]

TABLE_STYLE = """
                <style>
                    table {
                        border-collapse: collapse;
                        width: auto; /* Auto width based on content */
                        max-width: 100%; /* Ensures it doesn't overflow */
                        font-size: 12px; /* Small font */
                    }
                    th {
                        background-color: #4CAF50; /* Green header */
                        color: white;
                        padding: 5px;
                        text-align: left;
                        border: 1px solid #ddd;
                    }
                    td {
                        padding: 5px;
                        text-align: left;
                        # border: 1px solid #ddd;
                        border: 1px solid black; 
                    }
                    tr:nth-child(odd) {
                        background-color: #3bdbed; 
                    }
                    tr:nth-child(even) {
                        background-color: #7ae8f5; 
                    }
                </style>
                """
                

def format_dataframe_html(df, title):
    return f"<strong>{title}:</strong><br>" + TABLE_STYLE + df.to_html(index=False, escape=False) + "<br><br>"

body = format_dataframe_html(df_exceptions, "Configuration Details")
email_util = EmailUtility()
email_util.send_email(
            to_recipient=["<EMAIL>", "<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"],
            subject=f"Missing Airflow Configurations",
            body=body,
            df=None
)
if df_exceptions is not None and not df_exceptions.empty:
    logger.error(f"Missing Configurations need to be setup in Airflow. Please contact the data-platform Dev team to get this setup at the earliest \n{df_exceptions}")

