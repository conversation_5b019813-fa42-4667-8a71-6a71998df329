import os
import json
import logging
from utils.postgres.adaptor import PostgresAdaptor

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logger.info("Resetting SpiderRock DB partitions...")

    loader = PostgresAdaptor(
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df = loader.execute_query("SHOW TIMEZONE;")

    print(df)