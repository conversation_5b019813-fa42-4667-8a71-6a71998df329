from airflow import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.contrib.operators.spark_submit_operator import SparkSubmitOperator
from datetime import datetime, timedelta
from airflow.hooks.base_hook import BaseHook

now = datetime.now()
spark_master_conn = BaseHook.get_connection("spark_default")
spark_master = spark_master_conn.host

"""
aws_region = os.environ.get('AWS_REGION')
client = boto3.client('appconfig', region_name=aws_region)
applications_resp = client.list_applications()
application_name = "etlConfig"
print(applications_resp)
for application in applications_resp.get('Items', []):
    if application['Name'] == application_name:
        client_id = application['Id']
        break

config_resp = client.get_configuration(
    Application=application_name,
    ClientId=client_id,
    Configuration='config_profile',
    Environment='etlEnv'
)
config = json.loads(config_resp['Content'].read().decode('utf-8'))
print("-----------------")
print(config)

spark_master = config["spark_master_conn"]
"""

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(now.year, now.month, now.day),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id="jainglobal-etl-pipeline", 
    description="This DAG runs an ETL pipeline to convert partition data into parquet",
    default_args=default_args, 
    schedule_interval='@once',
    catchup=False
)

start = DummyOperator(task_id="start", dag=dag)

preprocessing_job = SparkSubmitOperator(
    task_id="preprocessing_job",
    conn_id="jg_spark",  
    application="/home/<USER>/spark/app/loaders/vendor/msci/secmaster/code/preprocessing-module.py",
    name="preprocess-files-from-s3",
    verbose=1,
    conf={"spark.master": spark_master},
    spark_binary="/home/<USER>/.local/bin/spark-submit",
    dag=dag
)

etl_job = SparkSubmitOperator(
    task_id="etl_job",
    conn_id="jg_spark",
    application="/home/<USER>/spark/app/loaders/vendor/msci/secmaster/code/etl-module.py",
    name="etl-pipeline",
    verbose=1,
    conf={
        "spark.master": spark_master,
        "spark.jars": "/home/<USER>/spark/jars/jg-iceberg-extensions.jar,/home/<USER>/spark/jars/aws-java-bundle.jar",
        "PYSPARK_SUBMIT_ARGS": "--jars /opt/bitnami/spark/python/lib/py4j-********-src.zip"
    },
    spark_binary="/home/<USER>/.local/bin/spark-submit",
    dag=dag
)

end = DummyOperator(task_id="end", dag=dag)

start >> preprocessing_job >> etl_job >> end
