import fastapi as _fastapi
import fastapi.security as _security
import jwt as _jwt
import datetime as _dt
import sqlalchemy.orm as _orm
import passlib.hash as _hash
from sqlalchemy import inspect

import database as _database, models as _models, schemas as _schemas

oauth2schema = _security.OAuth2PasswordBearer(tokenUrl="/api/token")

JWT_SECRET = "myjwtsecret"


def create_database():
    return _database.Base.metadata.create_all(bind=_database.engine)


def get_db():
    db = _database.SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_user_by_email(email: str, db: _orm.Session):
    return db.query(_models.User).filter(_models.User.email == email).first()


async def create_user(user: _schemas.UserCreate, db: _orm.Session):
    user_obj = _models.User(
        email=user.email, hashed_password=_hash.bcrypt.hash(user.hashed_password), isAdmin=False
    )
    db.add(user_obj)
    db.commit()
    db.refresh(user_obj)
    return user_obj


async def authenticate_user(email: str, password: str, db: _orm.Session):
    user = await get_user_by_email(db=db, email=email)

    if not user:
        return False

    if not user.verify_password(password):
        return False

    return user


async def create_token(user: _models.User):
    user_obj = _schemas.User.from_orm(user)

    token = _jwt.encode(user_obj.dict(), JWT_SECRET)

    return dict(access_token=token, token_type="bearer")


async def get_current_user(
    db: _orm.Session = _fastapi.Depends(get_db),
    token: str = _fastapi.Depends(oauth2schema),
):
    try:
        payload = _jwt.decode(token, JWT_SECRET, algorithms=["HS256"])
        user = db.query(_models.User).get(payload["id"])
    except:
        raise _fastapi.HTTPException(
            status_code=401, detail="Invalid Email or Password"
        )
        
    return _schemas.User.from_orm(user)


async def create_dataset(user: _schemas.User, db: _orm.Session, dataset: _schemas.Catalog):
    db = _database.SessionLocal()
    last_updated_user = user.email.split('@')[0]
    dataset = _models.Catalog(**dataset.dict(exclude={"last_updated_by"}), owner_id=user.id, last_updated_by=last_updated_user)
    
    db.add(dataset)
    db.commit()
    db.refresh(dataset)
    return _schemas.Catalog.from_orm(dataset)


async def get_datasets(user: _schemas.User, db: _orm.Session):
    db = _database.SessionLocal()
    try:
        columns = get_catalog_columns()
        columns = {col: col for col in columns}
        datasets = db.query(_models.Catalog)
        db.commit()
        data = list(map(_schemas.Catalog.from_orm, datasets))
        data.insert(0, columns)
        return data
    except Exception as e:
        db.rollback()
    finally:
        db.close()

def get_catalog_columns():
    db = _database.SessionLocal()
    inspector = inspect(db.bind)
    columns = [column["name"] for column in inspector.get_columns('datacatalog','dataops')]
    print(columns)
    # columns = [column["name"] for column in inspector.get_columns('datacatalog1')]
    return columns


async def _dataset_selector(dataset_id: str, user: _schemas.User, db: _orm.Session):
    dataset = (
        db.query(_models.Catalog)
        .filter_by(owner_id=user.id)
        .filter(_models.Catalog.dataset_id == dataset_id)
        .first()
    )

    if dataset is None:
        raise _fastapi.HTTPException(status_code=404, detail="dataset does not exist")

    return dataset


async def get_dataset(dataset_id: int, user: _schemas.User, db: _orm.Session):
    dataset = await _dataset_selector(dataset_id=dataset_id, user=user, db=db)

    return _schemas.dataset.from_orm(dataset)


async def delete_dataset(dataset_id: str, user: _schemas.User, db: _orm.Session):
    db = _database.SessionLocal()
    dataset = await _dataset_selector(dataset_id, user, db)

    db.delete(dataset)
    db.commit()

async def update_dataset(dataset_id: str, dataset: _schemas.DatasetCreate, user: _schemas.User, db: _orm.Session):
    db = _database.SessionLocal()
    dataset_db = await _dataset_selector(dataset_id, user, db)
    
    # dataset_db = dataset
    dataset_db.dataset_id = dataset.dataset_id,
    dataset_db.dataset_name = dataset.dataset_name,
    dataset_db.vendor = dataset.vendor,
    dataset_db.dataset_description = dataset.dataset_description,
    dataset_db.dataset_details = dataset.dataset_details,
    dataset_db.product_owner = dataset.product_owner,
    dataset_db.data_management_lead = dataset.data_management_lead,
    dataset_db.dm_lead_email = dataset.dm_lead_email,
    dataset_db.dm_lead_phone = dataset.dm_lead_phone,
    dataset_db.dm_lead_mobile = dataset.dm_lead_mobile,
    dataset_db.vendor_contact_other = dataset.vendor_contact_other,
    dataset_db.vendor_contact_title = dataset.vendor_contact_title,
    dataset_db.vendor_contact_work_phone = dataset.vendor_contact_work_phone,
    dataset_db.vendor_contact_mobile = dataset.vendor_contact_mobile,
    dataset_db.vendor_contact_email = dataset.vendor_contact_email,
    dataset_db.raw_data_location = dataset.raw_data_location,
    dataset_db.process_data_location = dataset.process_data_location,
    dataset_db.file_type = dataset.file_type,
    dataset_db.update_frequency = dataset.update_frequency,
    dataset_db.technical_notes = dataset.technical_notes,
    dataset_db.github_repository = dataset.github_repository,
    dataset_db.support_document_link = dataset.support_document_link,
    dataset_db.file_names = dataset.file_names,
    dataset_db.vendor_feed_sla_est = dataset.vendor_feed_sla_est,
    dataset_db.users = dataset.users,
    dataset_db.vendor_ticketing_portal = dataset.vendor_ticketing_portal,
    dataset_db.vendor_customer_support_dl = dataset.vendor_customer_support_dl,
    dataset_db.vendor_hotline_number = dataset.vendor_hotline_number,
    dataset_db.sftp_details = dataset.sftp_details,
    dataset_db.historical_data_start_date = dataset.historical_data_start_date,
    dataset_db.status = dataset.status,
    dataset_db.permission_group = dataset.permission_group,
    dataset_db.file_count = dataset.file_count,
    dataset_db.vendor_feed_extraction_source = dataset.vendor_feed_extraction_source,
    dataset_db.vendor_feed_extraction_source_url = dataset.vendor_feed_extraction_source_url,
    dataset_db.credentials = dataset.credentials,
    dataset_db.grafana_alerts = dataset.grafana_alerts,
    dataset_db.dataset_billing_name = dataset.dataset_billing_name,
    dataset_db.tables_loaded = dataset.tables_loaded,
    dataset_db.airflow_dag_names = dataset.airflow_dag_names,
    dataset_db.slack_channel_name = dataset.slack_channel_name,
    dataset_db.teams_channel_name = dataset.teams_channel_name,
    dataset_db.vendor_account_manager = dataset.vendor_account_manager,
    dataset_db.vendor_account_manager_work_phone = dataset.vendor_account_manager_work_phone,
    dataset_db.vendor_account_manager_mobile = dataset.vendor_account_manager_mobile,
    dataset_db.vendor_account_manager_email = dataset.vendor_account_manager_email,
    dataset_db.vendor_sales_specialist = dataset.vendor_sales_specialist,
    dataset_db.vendor_sales_specialist_mobile = dataset.vendor_sales_specialist_mobile,
    dataset_db.vendor_sales_specialist_email = dataset.vendor_sales_specialist_email,
    dataset_db.vendor_technical_account_manager = dataset.vendor_technical_account_manager,
    dataset_db.vendor_technical_account_manager_work_phone = dataset.vendor_technical_account_manager_work_phone,
    dataset_db.vendor_technical_account_manager_mobile = dataset.vendor_technical_account_manager_mobile,
    dataset_db.vendor_technical_account_manager_email = dataset.vendor_technical_account_manager_email,
    dataset_db.customer_success_manager_product = dataset.customer_success_manager_product,
    dataset_db.customer_success_manager_product_work_phone = dataset.customer_success_manager_product_work_phone,
    dataset_db.customer_success_manager_product_mobile  = dataset.customer_success_manager_product_mobile ,
    dataset_db.customer_success_manager_product_email = dataset.customer_success_manager_product_email,
    

    dataset_db.last_updated_by = user.email.split('@')[0]
    dataset_db.date_last_updated = _dt.datetime.now()

    db.commit()
    db.refresh(dataset_db)

    return _schemas.Catalog.from_orm(dataset_db)
