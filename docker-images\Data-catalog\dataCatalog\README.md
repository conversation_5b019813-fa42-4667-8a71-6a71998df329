
# Trading Dashboard

## Description
This TypeScript project provides a trading dashboard simulation with a FastAPI server backend. The application features a dynamic pricing server that broadcasts real-time price updates for selected financial instruments and handles trade order submissions.

## Screenshot
![image](https://github.com/petmar2017/3fOrderEntry/assets/47054401/5977c088-3926-4f4c-8b6f-e47006852e25)


## Key Components
- **FastAPI Server**: Simulates a pricing server that broadcasts price updates over WebSockets and accepts trade orders through HTTP POST requests.
- **WebSockets**: Real-time communication between the server and client for price updates.
- **Order Management**: Handles order submissions and provides real-time feedback on the operation's success or failure.

## Development Setup
The frontend part of the application is developed in TypeScript. To work on this project:

1. Ensure only the `./static/ts/*` files are updated.
2. Run the `start_dev_server.sh` script to compile TypeScript files into JavaScript. This script also manages server startup and file backups.

### Running the Development Server
Use the provided shell script to start the development server:

```sh
./start_dev_server.sh
```

This script will take care of the following:
- Check if the FastAPI server is running and stop it if necessary.
- Back up the current JavaScript files.
- Compile TypeScript files using the `tsc` command.
- Start the FastAPI server.

## Project Structure
- `app.py`: The entry point for the FastAPI application.
- `services/connection_manager.py`: Manages WebSocket connections.
- `models/order.py`: Defines the structure for trade orders.
- `static/ts`: Contains TypeScript files for the frontend logic.

## Usage
Select a symbol or currency pair from the dashboard to view the latest prices and place trade orders. Real-time price updates and order confirmations will be displayed dynamically.

## Contributing
Please note that any changes should be made in the TypeScript files, and the `start_dev_server.sh` script should be used to compile the changes and restart the server.
