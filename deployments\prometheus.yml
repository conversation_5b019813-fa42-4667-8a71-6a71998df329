apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
  labels:
    app: prometheus
    component: server

data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 30s

    scrape_configs:
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)

      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            target_label: __address__
            regex: (.+)
            replacement: $1

      - job_name: 'kubernetes-services'
        kubernetes_sd_configs:
          - role: service
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_service_label_(.+)

      - job_name: 'kubernetes-ingresses'
        kubernetes_sd_configs:
          - role: ingress
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_ingress_label_(.+)

      - job_name: 'kubelet'
        scheme: https
        tls_config:
          insecure_skip_verify: true
        kubernetes_sd_configs:
          - role: node
        relabel_configs:
          - target_label: __metrics_path__
            replacement: /metrics/cadvisor

      - job_name: 'custom-metrics'
        static_configs:
          - targets: ['custom-metric-exporter:9100']
