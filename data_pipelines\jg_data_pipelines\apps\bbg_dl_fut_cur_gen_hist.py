from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor

from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from bloomberg.per_security.parser import BloombergParser
import pandas as pd
import tempfile
import shutil
import gzip
import os

from utils.date_utils import get_now, get_n_bday_yyyymmdd

if __name__ == "__main__":

    securities = [
        "TY1 Comdty",
        "ES1 Index",
        "UX1 Index",
        "UX2 Index",
    ]

    per_sec_req_type = PerSecurityRequestType.gethistory
    batch_name = "dp_fghist"

    from_date = get_n_bday_yyyymmdd(10)
    to_date = get_n_bday_yyyymmdd(0)

    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "date_range": f"{from_date}|{to_date}",
        "sec_id": "TICKER",
        "fields": [
            "FUT_CUR_GEN_TICKER",
        ],
        "securities": securities,
    }

    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_generic_ref"
    hist_fut_gen_ticker_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)

    hist_fut_gen_ticker_name = os.path.basename(hist_fut_gen_ticker_path)
    df_fut_curr_gen = pd.DataFrame()
    with tempfile.TemporaryDirectory() as temp_dir:
        tmp_file_path = f"{temp_dir}/{hist_fut_gen_ticker_name.replace('.gz', '')}"
        with gzip.open(hist_fut_gen_ticker_path, "rb") as f_in:
            with open(tmp_file_path, "wb") as f_out:
                shutil.copyfileobj(f_in, f_out)

        parser = BloombergParser(
            tmp_file_path, sep="|", skipinitialspace=True, on_bad_lines="error"
        )
        df_fut_curr_gen = parser.parse_data()

    if df_fut_curr_gen.shape[0] == 0:
        raise ValueError("No data returned from Bloomberg")
    else:
        df_fut_curr_gen.rename(columns={"Ticker": "FUT_GEN_TICKER", "Date": "DATE", "Value": "BBG_TICKER"}, inplace=True)
        df_fut_curr_gen["DATE"] = pd.to_datetime(df_fut_curr_gen["DATE"], format="%m/%d/%Y")
        df_fut_curr_gen.drop(columns=["Field"], inplace=True)
        df_fut_curr_gen["LAST_UPDATED"] = get_now()

        adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_FUTURES",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
        )

        ret_val = adaptor.upsert(df_fut_curr_gen, "FUT_CUR_GEN_TICKER", ["DATE", "FUT_GEN_TICKER"])
        print(ret_val)