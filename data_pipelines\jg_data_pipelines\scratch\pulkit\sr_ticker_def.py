import requests 

# Replace with your desired MLINK URL 
MLINK_PROD_URL = 'https://mlink-live.nms.saturn.spiderrockconnect.com/rest/json'

# Replace with your MLINK API Key
API_KEY = "76334B43-38A4-4D92-BDA3-FC3977C67AFE"

# Replace with your desired MsgType.  
MSG_TYPE = 'RootDefinition'

# VIEW = 'securityID|symbolType|name|country|primaryExch|mic|symbol|cusip|isin|figi|bbgCompositeTicker|bbgExchangeTicker|bbgCompositeGlobalID|bbgGlobalID|regionalCompositeTicker|timestamp'
VIEW = 'ticker|osiRoot|ccode|uPrcDriverKey|uPrcDriverType|uPrcDriverKey2|uPrcDriverType2|uPrcBoundCCode|expirationMap|underlierMode|optionType|multihedge|exerciseTime|exerciseType|timeMetric|tradingPeriod|pricingModel|moneynessType|priceQuoteType|volumeTier|positionLimit|exchanges|tickValue|pointValue|pointCurrency|strikeScale|strikeRatio|cashOnExercise|underliersPerCn|premiumMult|symbolRatio|adjConvention|optPriceInc|priceFormat|minTickSize|tradeCurr|settleCurr|strikeCurr|defaultSurfaceRoot|ricRoot|bbgRoot|bbgGroup|regionalCompositeRoot|timestamp'
# VIEW = 'securityID|name|bbgCompositeTicker'



# Request Parameters for Get Schema Of The MsgType
params = {
    # Required Parameters
    "apiKey": API_KEY,
    "cmd": 'getmsgs',
    "msgType": MSG_TYPE, 
    "view": VIEW, 
    "where": "root:eq:BRKB",
    "limit": 100000
    # "limit": 3,
}

response = requests.get(MLINK_PROD_URL, params=params)
if response.status_code != 200:
    raise Exception(f"Error: {response.status_code}, {response.text}")

data = response.json()
# print(len(data))
print(data)
