apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    component: "server"
    app: prometheus
    release: loki
    chart: prometheus-19.7.2
    heritage: Helm
  name: prometheus-server
  namespace: default
spec:
  selector:
    matchLabels:
      component: "server"
      app: prometheus
      release: loki
  replicas: 1
  strategy:
    type: Recreate
    rollingUpdate: null
  template:
    metadata:
      labels:
        component: "server"
        app: prometheus
        release: loki
        chart: prometheus-19.7.2
        heritage: Helm
    spec:
      enableServiceLinks: true
      serviceAccountName: loki-prometheus-server
      containers:
        - name: prometheus-server
          image: ${ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/${PREFIX}-prom-cwiqfs-${ENV}:latest
          imagePullPolicy: "IfNotPresent"
          args:
            - "--storage.tsdb.retention.time=15d"
            - "--config.file=/etc/prometheus/prometheus.yml"
            - "--storage.tsdb.path=/tmp"
            - "--web.console.libraries=/etc/prometheus/console_libraries"
            - "--web.console.templates=/etc/prometheus/consoles"
            - "--web.enable-lifecycle"
            #- "--query.active-tracker.enabled=false"
            #- --query.active-query-log=/var/tmp/prometheus/queries.active
          command:
            - /bin/sh
            - "-c"
            - |
              echo "Executing mount-jfs.sh..." && \
              /usr/lib/jfs/mount-jfs.sh && \
              echo "Starting Prometheus..." && \
              /bin/prometheus \
                --config.file=/etc/prometheus/prometheus.yml \
                --storage.tsdb.path=/jfs/tech1/apps/datait/jg-metadata/prometheus
          ports:
            - containerPort: 9090
          readinessProbe:
            httpGet:
              path: /-/ready
              port: 9090
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 5
            timeoutSeconds: 4
            failureThreshold: 3
            successThreshold: 1
          livenessProbe:
            httpGet:
              path: /-/healthy
              port: 9090
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 10
            failureThreshold: 3
            successThreshold: 1
          volumeMounts:
            - name: config-volume
              mountPath: /etc/prometheus
            - name: storage-volume
              mountPath: /prometheus
              mountPropagation: Bidirectional  # Enables shared propagation
          securityContext:
            capabilities:
              add: ["SYS_ADMIN"]   # Adds SYS_ADMIN capability for FUSE
            privileged: true        # Allows privileged operations for FUSE
            runAsUser: 0
        - name: prometheus-server-configmap-reload
          image: ${ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/configmap-reload:0.8.0
          imagePullPolicy: "IfNotPresent"
          args:
            - --volume-dir=/etc/prometheus
            - --webhook-url=http://127.0.0.1:9090/-/reload
          volumeMounts:
            - name: config-volume
              mountPath: /etc/prometheus
              readOnly: true
      dnsPolicy: ClusterFirst
      securityContext:
        fsGroup: 65534
        runAsGroup: 65534
        #runAsNonRoot: true
        runAsUser: 65534
      terminationGracePeriodSeconds: 300
      volumes:
        - name: config-volume
          configMap:
            name: loki-prometheus-server
        - name: storage-volume
          hostPath:
            path: /tmp            # Specify the path on the host node
            type: DirectoryOrCreate
