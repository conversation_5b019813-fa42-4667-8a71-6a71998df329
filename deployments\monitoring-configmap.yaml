apiVersion: v1
kind: ConfigMap
metadata:
  name: platform-process-monitoring-config
data:
  config.yaml: |
    server_ip:
      - "jsvc-datait@*************"
      - "jsvc-datait@************"
      - "jsvc-datait@***********"
      - "jsvc-datait@************"
      - "jsvc-datait@*************"
      - "jsvc-foit@************"
      - "jsvc-foit@***********"
      - "jsvc-dataops@************"
      - "jsvc-dataops@*************"
      - "jsvc-treasit@*************"
      - "jsvc-treasit@************"
    commands:
      - "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/monitoring/monitor_connectors.sh"
      - "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/monitoring/check-server-connectivity.sh"
      - "python3 /jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/monitoring/kafka_snowflake_offset_diff.py"
      - "python3 /jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/monitoring/kafka_status_polling.py"
      - "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/monitoring/service_status_check.sh"
      - "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/monitoring/monitor_script_watchdog.sh"
      - "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/monitoring/process_status_check.sh"