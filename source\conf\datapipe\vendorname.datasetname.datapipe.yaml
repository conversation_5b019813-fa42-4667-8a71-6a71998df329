raw_data:
  rawdata_location: "/jsf/path/to/files/" # path to the files to be loaded into snowflake
  s3_prefix: "s3-prefix/to_save/data" # s3 prefix to save the data
  include_prefix: true # if you want to include the prefix in the file name
  structure: '{
      "FILE*$DATE$*.NAME.GZ": [
          "*.CSV"
      ]
  }' # structure of the file to be loaded into snowflake

snowflake:
  db_name: "db_name" # database name in snowflake
  schema_name: "schema_name" # schema name in snowflake

  table_map:
    test_table:
      pattern: ".*$DATE$.*regex.*" 
      col_num: 3  # number of columns in the file to be loaded into the table with $1, $2, $3...alias
      metadata_columns: ["filename"]
      stage_path: "stage_name/stage_path/"  # internal stage path when files are loaded into snowflake
      file_format: "FF_name" # File Formats to be used in snowflake


    test_table2:
      table_alias: "test_table" # If you want to use the same table name in multiple places, you can use the table alias
      pattern: ".*$DATE$.*regex.*" 
      parse_header: true  # if need to parse header on copy commnad to snowflake (file format should be defined in snowflake with PARSE_HEADER = True and ERROR_ON_COLUMN_COUNT_MISMATCH = False)
      metadata_columns: ["filename"]
      metadata_alias:   ["col1"] # To parse_header = true, metadata_columns and metadata_alias should be defined (this col1 = filename)
      stage_path: "stage_name/stage_path/" 
      file_format: "FF_name" # File Formats to parse_header = true nned to be defined in snowflake with PARSE_HEADER = True and ERROR_ON_COLUMN_COUNT_MISMATCH = False
         
         