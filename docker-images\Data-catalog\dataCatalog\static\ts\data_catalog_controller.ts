interface Users {
    username: String,
    password: String,
    is_admin: boolean
}

class DataCatalogController {
    private model: DataCatalogModel;
    private marketDataTable: DataTable | null = null;
    private baseUrl =  `${window.location.protocol}//${window.location.host}`;
    data:DataCatalog[] = []
    token = "";
    currentUser: any

    constructor() {
        DataCatalogController.openTab(new Event('click'), 'MarketData');
        this.model = new DataCatalogModel();
        // localStorage.setItem("userToken", "null")

        const login = document.getElementById('login-button');
        login?.addEventListener("click", (event) => {
            event.preventDefault();
            this.routeToLogin();
        })

        const logout = document.getElementById('logout-button');
        logout?.addEventListener("click", (event) => {
            event.preventDefault();
            localStorage.setItem("userToken", "null")
            const login = document.getElementById('login-button')! as HTMLButtonElement;
            logout.style.display = 'none'
            login.style.display = 'block'
            // this.routeToLogin();
            const dataset_name_input = document.getElementById('add_input')! as HTMLInputElement;
            const add_dataset_btn = document.getElementById('add_dataset')! as HTMLButtonElement;
            const edit_btn = document.getElementById('edit-row')! as HTMLButtonElement;
            const delete_btn = document.getElementById('delete-row')! as HTMLButtonElement;

            dataset_name_input.style.display = 'none'
            add_dataset_btn.style.display = 'none'
            // edit_btn.style.display = 'none'
            // delete_btn.style.display = 'none'
            document.querySelectorAll('[data-action="edit"]').forEach(button => {
                button.classList.add('hide')
            })
            document.querySelectorAll('[data-action="delete"]').forEach(button => {
                button.classList.add('hide')
            })

        })

        this.renderAppCatalogTable()

        this.fetch_user()

        // add dataset
        const dataset_name_input = document.getElementById('add_input')! as HTMLInputElement;
        const add_dataset_btn = document.getElementById('add_dataset')! as HTMLButtonElement;
        add_dataset_btn?.addEventListener('click', () => {
            if (dataset_name_input.value.length > 0) {
                this.addorEditDataset(dataset_name_input.value);
                dataset_name_input.value = '';
            } else {
                alert("Please enter the Dataset name")
            }
        })

        this.renderETLStatusTable()
        // this.renderOnboardingDatasetTable()
    }

    async fetch_user(){
            const requestOptions = {
              method: "GET",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer " + localStorage.getItem("userToken"),
              },
            };

            const response = await fetch(`${this.baseUrl}/api/users/me`, requestOptions);
            if (!response.ok) {
                localStorage.setItem("userToken", "null")
                const login = document.getElementById('login-button') as HTMLButtonElement;
                const logout = document.getElementById('logout-button') as HTMLButtonElement;
                logout.style.display = 'none'
                login.style.display = 'block'

                await this.searchData("");
                const dataset_name_input = document.getElementById('add_input')! as HTMLInputElement;
                const add_dataset_btn = document.getElementById('add_dataset')! as HTMLButtonElement;
                const edit_btn = document.getElementById('edit-row')! as HTMLButtonElement;
                const delete_btn = document.getElementById('delete-row')! as HTMLButtonElement;
                dataset_name_input.style.display = 'none'
                add_dataset_btn.style.display = 'none'
                document.querySelectorAll('[data-action="edit"]').forEach(button => {
                    button.classList.add('hide')
                })
                document.querySelectorAll('[data-action="delete"]').forEach(button => {
                    button.classList.add('hide')
                })

                // if(!this.currentUser.isAdmin) {

                    // edit_btn.hidden = true
                    // delete_btn.hidden = true
                // }
            } else {
                const login = document.getElementById('login-button') as HTMLButtonElement;
                const logout = document.getElementById('logout-button') as HTMLButtonElement;
                login.style.display = 'none'
                logout.style.display = 'block'

                // login.hidden = true
                this.currentUser = await response.json()
                // this.attachSearchFieldListener();
                this.searchData("");  // Call with empty string to load everything initially
                const dataset_name_input = document.getElementById('add_input')! as HTMLInputElement;
                const add_dataset_btn = document.getElementById('add_dataset')! as HTMLButtonElement;
                const edit_btn = document.getElementById('edit-row')! as HTMLButtonElement;
                const delete_btn = document.getElementById('delete-row')! as HTMLButtonElement;


                if(this.currentUser.isAdmin) {
                    dataset_name_input.style.display = 'block'
                    add_dataset_btn.style.display = 'block'
                    document.querySelectorAll('[data-action="edit"]').forEach(button => {
                        // if(button.classList.contains('hidden')) {
                            button.classList.remove('hidden')
                        // }
                    })
                    document.querySelectorAll('[data-action="delete"]').forEach(button => {
                        // button.classList.toggle('hidden',true)
                        // if(button.classList.contains('hidden')) {
                            button.classList.remove('hidden')
                        // }

                    })
                }

        }
    }

    public async searchData(searchTerm: string): Promise<void> {
        //const response = await fetch(`${this.baseUrl}/api/search/?q=${searchTerm}`);
        const response = await fetch(`${this.baseUrl}/api/search2`);
        const dataFetched = await response.json();
        this.data = dataFetched.search_results;
        const etlStatus = dataFetched.etl_status;
        console.log(this.data)

        const datacatalog_table_headers: { [key: string]: string } = {"Action": "Action", "Dataset_Code": "dataset_id", "Dataset Name": "dataset_name", "Vendor": "vendor", "Dataset Details": "dataset_details", "Status": "status", "Permission Group": "permission_group", "Data Management Lead": "data_management_lead", "DM Lead Email": "dm_lead_email", "DM Lead Phone": "dm_lead_phone", "DM Lead Mobile": "dm_lead_mobile", "Vendor Feed Extraction Source": "vendor_feed_extraction_source", "Vendor Feed Extraction Source URL": "vendor_feed_extraction_source_url", "Raw Data Location": "raw_data_location", "Process Data Location": "process_data_location", "File Type": "file_type", "Users": "users", "Vendor Ticketing Portal": "vendor_ticketing_portal", "Vendor Customer Support DL": "vendor_customer_support_dl", "Vendor Hotline Number": "vendor_hotline_number"}
        const formattedData = this.data.map((row: any, index: number) => ({
            Action: index === 0 ? "Action": `<button class="action" id="view-row" data-id="row-${index}" data-action="view">View</button>&nbsp<button class="action edit hidden" id="edit-row" data-id="row-${index}" data-action="edit">Edit</button>&nbsp<button class="action deletehiddden" id="delete-row" data-id="row-${index}" data-action="delete">Delete</button>`,
            ...row,
            rowId: index === 0 ? "rowId" : `row-${index}`
        }));

        const attributesMap = new Map<string,{ dataset_id: string; jfs_rag_status: string; etl_rag_status: string; whdv_rag_status: string}>(
            etlStatus.map(((item: { dataset_id: string; jfs_rag_status: string; etl_rag_status: string; whdv_rag_status: string; }) => [item.dataset_id, { jfs_rag_status: item.jfs_rag_status, etl_rag_status: item.etl_rag_status, whdv_rag_status: item.whdv_rag_status }])
        ));
        let catalogTableData = formattedData.map(item => {
            const update = attributesMap.get(item.dataset_id);
            return {
                ...item,
                jfs_rag_status: update?.jfs_rag_status ?? '',
                etl_rag_status: update?.etl_rag_status ?? '',
                whdv_rag_status: update?.whdv_rag_status ?? ''
            };
        });
        catalogTableData = [catalogTableData[0], ...catalogTableData.slice(1).sort((a, b) => a.id - b.id)]
                
        this.marketDataTable = new DataTable("datacatalogTable", catalogTableData, this.onTableRowClick.bind(this), this.currentUser, datacatalog_table_headers);      
        this.marketDataTable.initializeSearch('datacatalog', datacatalog_table_headers);
        this.marketDataTable.renderTable(catalogTableData);
        const datacatalog_tab = document.getElementById('datacatalog_tab')! as HTMLButtonElement;
        datacatalog_tab?.addEventListener('click', () => {
            console.log("datacatalog tab clicked")
            this.marketDataTable!.data = catalogTableData
            this.marketDataTable!.handleTabClick('datacatalog', catalogTableData, datacatalog_table_headers)

        })
        this.marketDataTable.addObserver((updatedData) => {
            // console.log("Table data updated:", updatedData);
        });
    }

    public async fetchRows(): Promise<any[]> {
        const response = await fetch(`${this.baseUrl}/api/rows/`);
        const data = await response.json();
        this.searchData("");  // Call with empty string to load everything initially
        return data
    }

    private async addorEditDataset(dataset_name: string) {
        const newRow = this.data[0] as any
        Object.keys(newRow).forEach((key: string) => {
            newRow[key] = ""
        })
        newRow['Action'] = `<button id="view-row" class="action" data-id="row-${this.data.length}" data-action="view">View</button>&nbsp<button class="action edit hidden" id="edit-row" data-id="row-${this.data.length}" data-action="edit">Edit</button>&nbsp<button class="action delete hidden" id="delete-row" data-id="row-${this.data.length}" data-action="delete">Delete</button>`;
        newRow['rowId'] = 'row-' + this.data.length;
        newRow['dataset_id'] = dataset_name;
        newRow['dataset_name'] = dataset_name;
        newRow['id'] = this.data.length;

        const save_dataset_btn = document.getElementById('save-btn')! as HTMLButtonElement;
        save_dataset_btn.hidden = false;
        const tableElement = document.getElementById('detailsTable') as HTMLTableElement;
        tableElement.innerHTML = ''; // Clear existing content
        for (const key in newRow) {
            const columnsToBeSkipped = ['id', 'Action', 'rowId', 'owner_id', 'date_last_updated', 'last_updated_by', "Download Status", "ETL Status", "Historical Load Status", "DQ Check", "jfs_rag_status", "etl_rag_status", "whdv_rag_status", "vendor_contact_other", "vendor_contact_title", "vendor_contact_work_phone", "vendor_contact_mobile", "vendor_contact_email", "vendor_account_manager", "vendor_account_manager_work_phone", "vendor_account_manager_mobile", "vendor_account_manager_email", "vendor_sales_specialist", "vendor_sales_specialist_work_phone", "vendor_sales_specialist_mobile", "vendor_sales_specialist_email", "vendor_technical_account_manager", "vendor_technical_account_manager_work_phone", "vendor_technical_account_manager_mobile", "vendor_technical_account_manager_email", "customer_success_manager_product", "customer_success_manager_product_work_phone", "customer_success_manager_product_mobile", "customer_success_manager_product_email", "sftp_details", "rowId"]
            if (!columnsToBeSkipped.includes(key)) {
                const row = document.createElement('tr');
                const cellKey = document.createElement('th');
                cellKey.setAttribute("style", "text-wrap:wrap; border:1px solid #ddd;text-align:left;max-width:800px;padding:10px")
                cellKey.textContent = key;
                row.appendChild(cellKey);
                const cellValue = document.createElement('td');
                cellValue.setAttribute("style", "text-wrap:wrap; border:1px solid #ddd;text-align:left;max-width:800px;padding:10px;white-space: break-spaces;min-width: 500px")
                cellValue.textContent = newRow[key];

                // Add event listener to make cell editable
                cellValue.addEventListener('click', function () {
                    cellValue.contentEditable = "true";
                    cellValue.focus();
                    // Save the new value on blur or enter key press
                    const saveValue = () => {
                        cellValue.contentEditable = "false";
                        const newValue = cellValue.textContent || ''
                        cellValue.textContent = newValue;
                        newRow[key] = newValue;
                    };
                    cellValue.addEventListener('blur', saveValue);
                    cellValue.addEventListener('keydown', function (event) {
                        if (event.key === 'Enter') {
                            saveValue();
                        }
                    });
                });
                row.appendChild(cellValue);
                tableElement.appendChild(row);
            }
            DataCatalogController.openTab(new Event('click'), 'Details');
        }
        // onclick of Save button
        save_dataset_btn.addEventListener('click', async () => {
            const modal = document.getElementById("modal")!;
            modal.style.display = "flex";
            const yesButton = document.getElementById("yesButton")!;
            const noButton = document.getElementById("noButton")!;
            yesButton.addEventListener("click", () => {
                // Call your save API here
                modal.style.display = "none";
            });
            noButton.addEventListener("click", () => {
                modal.style.display = "none";
            });
            yesButton.addEventListener("click", async () => {
            const request = newRow;

            ['Action', 'rowId'].forEach((key) => delete request[key])
            const req:DataCatalog = request
            req.historical_data_start_date = new Date(req.historical_data_start_date)
            try {
                const response = await fetch(`${this.baseUrl}/api/add-row/`, {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: "Bearer " + localStorage.getItem("userToken")
                    },
                    body: JSON.stringify(req)
                });
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                } else {
                    alert("row added successfully");
                    location.reload();
                }
                const result = await response.json();
                console.log("Dataset addition successful:", result);
            } catch (error) {
                console.error("Error adding row:", error);
            }
        })
    })
    }


    private attachDocumentListeners(): void {
    // this.model.attachEventListener("marketDataViewButton", "click", (event: Event) => {
    //     const target = event.target as HTMLElement;
    //     const dataSetId = target.getAttribute("data-id");
    //     if (dataSetId) {
    //         this.loadData(dataSetId);
    //     }
    // });
}

    private loadData(dataSetId: string): void {
    fetch(`${this.baseUrl}/api/data_catalog/${dataSetId}`)
            .then((response) => response.json())
            .then((data) => {
                this.populateTabs(data);
            })
            .catch((error) => Logger.error("Error loading data: " + error));
    }

    private populateTabs(data: any): void {
        this.model.summaryContent = data.summary;
        this.model.preparationStepsContent = data.preparationSteps;
        this.model.dataSampleContent = data.dataSample;
        this.model.dependenciesContent = data.dependencies;
        this.model.supportContactsContent = data.supportContacts;
        this.model.supportNotesContent = data.supportNotes;
    }

    public attachSearchFieldListener(): void {
        const searchField = document.getElementById("searchField") as HTMLInputElement;
        searchField.addEventListener("input", async (event: Event) => {
            const target = event.target as HTMLInputElement;
            const searchTerm = target.value;
            this.searchData(searchTerm);
        });
    }


    private onTableRowClick(rowId: string, rowIndex: number, colIndex: number): void {
        if (rowId === "header") {   // Skip header row
            Logger.info("Header row clicked, skipping...");
            return;
        }
        const currentRowData = this.marketDataTable?.getRowDataById(rowId);
    }

    static openTab(evt: Event, tabName: string): void {
        const tabcontent = document.getElementsByClassName("tabcontent");
        for (let i = 0; i < tabcontent.length; i++) {
            (tabcontent[i] as HTMLElement).style.display = "none";
        }
        const tablinks = document.getElementsByClassName("tablinks");
        for (let i = 0; i < tablinks.length; i++) {
            tablinks[i].className = tablinks[i].className.replace(" active", "");
        }
        const tabToShow = document.getElementById(tabName);
        if (tabToShow) {
            tabToShow.style.display = "block";
        }
        if (evt.currentTarget instanceof HTMLElement) {
            evt.currentTarget.className += " active";
        }
    }

    routeToLogin() {
        const baseUrl = `${window.location.protocol}//${window.location.host}`;
        window.location.assign(baseUrl+"/static/login.html");
    }


    public async renderETLStatusTable() {
        const datePicker = document.getElementById('datePicker') as HTMLInputElement;
        const getDataBtn = document.getElementById('getDataBtn') as HTMLButtonElement;
        const selectedDateParagraph = document.getElementById('selectedDate') as HTMLParagraphElement;
        const refresh_btn = document.getElementById('refresh-btn') as HTMLButtonElement;
        const refreshedAt = document.getElementById('refreshedAt') as HTMLParagraphElement;


        const today = new Date();
        datePicker.setAttribute("max", today.toISOString().split('T')[0])
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are zero-based
        const day = String(today.getDate()).padStart(2, '0');
        const todayDate = `${year}-${month}-${day}`;
        datePicker.value = todayDate;

        function showCurrentTime() {
                const now = new Date();
                const options: Intl.DateTimeFormatOptions = {
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false, // Use 24-hour format
                    timeZoneName: 'short', // Include the timezone
                };
                return `Last refreshed at ${now.toLocaleTimeString('en-US', options)}`;
        }

        let data: any[] = [];

        const table_headers: { [key: string]: string } = {
            "ID": "id",
            "Dataset ID": "dataset_id",
            "Airflow Dag Names": "airflow_dag_name",
            "Download Status": "jfs_rag_status",
            "ETL Status": "etl_rag_status",
            "IST": "ist",
            "UTC": "utc",
            "EST": "est",
            "Timezone": "timezone",
            "Run Frequency":"run_frequency",
            // "Historical Load Status": "whdv_rag_status"
        };

        // Fetch data from API
        async function fetchandRenderAPIResponse(baseUrl: string, dateInput: string) {
            const response = await fetch(`${baseUrl}/api/getETL_status/${dateInput}`);
            data = await response.json();
            renderTable(data)
        }

        fetchandRenderAPIResponse(this.baseUrl, todayDate)
        selectedDateParagraph.textContent = `Displaying ETL Status for date: ${todayDate}  (IST)`
        refreshedAt.textContent = showCurrentTime()

        let refresh = setInterval(async () => {
            await fetchandRenderAPIResponse(this.baseUrl, todayDate)
            selectedDateParagraph.textContent = `Displaying ETL Status for date: ${todayDate}  (IST)`
            refreshedAt.textContent = showCurrentTime()
        }, 300000)

        // search for ETL status tab
        const searchInput = document.getElementById('ETLsearchInput') as HTMLInputElement;
        searchInput.addEventListener('input', () => {
            if (searchInput.value == '') {
                renderTable(data)
            } else {
                filterData(data)
            }
        })

        function filterData(data: any[]) {
            const searchTerm = searchInput.value.toLowerCase();
            const filteredData = data.filter(row => {
                return Object.values(row).some(value => String(value).toLowerCase().includes(searchTerm)
                );
            });
            renderTable(filteredData);
        }
        
        function formatDate(date: string): string {
            // Check if a date was selected
            if (!date) return '';
            // Format the date to yyyy-mm-dd
            const [year, month, day] = date.split('-');
            return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
        }

        refresh_btn.addEventListener('click', async() => {
            clearInterval(refresh)
            refresh = setInterval(async () => {
                await fetchandRenderAPIResponse(this.baseUrl, todayDate)
                selectedDateParagraph.textContent = `Displaying ETL Status for date: ${todayDate}  (IST)`
                refreshedAt.textContent = showCurrentTime()
            }, 300000)
        })

        getDataBtn.addEventListener('click', async () => {
            const selectedDate = datePicker.value;
            if (selectedDate) {
                const formattedDate = formatDate(selectedDate);
                fetchandRenderAPIResponse(this.baseUrl, formattedDate);
                selectedDateParagraph.textContent = `Displaying ETL Status for date: ${formattedDate} (IST)`;
                clearInterval(refresh)
                // toggle display of Refresh button
                if (selectedDate !== today.toISOString().split('T')[0]) {
                    refresh_btn.style.display = 'none';
                    refreshedAt.style.display = 'none'
                } else if (selectedDate === today.toISOString().split('T')[0]){
                    refresh_btn.style.display = 'block';
                    refreshedAt.style.display = 'block';
                    refreshedAt.textContent = showCurrentTime()
                    refresh = setInterval(async () => {
                        await fetchandRenderAPIResponse(this.baseUrl, todayDate)
                        selectedDateParagraph.textContent = `Displaying ETL Status for date: ${todayDate}  (IST)`
                        refreshedAt.textContent = showCurrentTime()
                    }, 300000)

                }
            }
            else {
                selectedDateParagraph.textContent = 'No date selected';
            }
        });

        function clearTable() {
            const table = document.getElementById('statusTable') as HTMLTableElement;
            table.innerHTML = ''
        }


        function renderTable(data:any[]) {
            clearTable()

        const table = document.getElementById('statusTable') as HTMLTableElement;

        // Create table header
        const thead = table.createTHead();
        const headerRow = thead.insertRow();
        const headers = Object.keys(table_headers);
        headers.forEach(headerText => {
            const th = document.createElement('th');
            th.textContent = headerText;
            headerRow.appendChild(th);
        });

        // Create table body
        const tbody = table.createTBody();
        data.forEach((item, index) => {
            const itemDetails: { [key: string]: string } = {
                "JFS DAG URL": item.jfs_dag_url,
                "ETL DAG URL": item.etl_dag_url,
                "JFS API HTML": item.jfs_api_html,
                "ETL API HTML": item.etl_api_html,
                "JFS API JSON": item.jfs_api_json,
                "ETL API JSON": item.etl_api_json,
                "JFS Settlement Files": item.jfs_settlement_files,
                "ETL Settlement Files": item.etl_settlement_files,
                "ETL Validation": item.etl_validation,
                "JFS Validation": item.jfs_validation,
                "Dev POC": item.dev_poc,
                "Comment": item.comment
            }
            const itemMessage: { [key: string]: string } = {
                "Download Status": item.jfs_message || 'No message available',
                "ETL Status": item.etl_message || 'No message available',
                "Historical Load Status": item.whdv_message || 'No message available'
            };

            const itemResponse: { [key: string]: string } = {
                "Download Status": item.jfs_Response || 'No response available',
                "ETL Status": item.etl_Response || 'No response available',
                "Historical Load Status": item.whdv_Response || 'No response available'
            };

            let filteredRowData: string[] = [];
            Object.values(table_headers).forEach(key => {
                if(key === 'id') {
                    filteredRowData.push(String(index+1))
                }
                else if (key in item) {
                    filteredRowData.push(item[key]);
                }
            });

            const row = tbody.insertRow();
            filteredRowData.forEach((value, index) => {
                const cell = row.insertCell();
                cell.textContent = value as string;
                if(['Download Status', 'ETL Status', 'Historical Load Status'].includes(headers[index])) {
                    cell.addEventListener('mouseover', () => showTooltip(itemMessage, headers[index]));
                    cell.addEventListener('click', () => openModal(item.dataset_id, itemResponse, headers[index]))
                }
                else if(['Dataset ID'].includes(headers[index])) {
                    cell.addEventListener('click', () => openModal(item.dataset_id, itemDetails, headers[index]))
                }
                
                // Apply background color based on value
                if (cell.textContent.toLowerCase() === 'green') {
                    cell.textContent = 'Green'
                    cell.classList.add('green');
                } else if (cell.textContent.toLowerCase() === 'amber') {
                    cell.textContent = 'Amber'
                    cell.classList.add('amber');
                } else if (cell.textContent.toLowerCase() === 'red') {
                    cell.textContent = 'Red'
                    cell.classList.add('red');
                } else if (cell.textContent.toLowerCase() === "not run") {
                    cell.textContent = 'Not Run'
                    cell.classList.add('red');
                } else if (cell.textContent.toLowerCase() === "not scheduled") {
                    cell.textContent = 'Not Scheduled'
                    cell.classList.add('grey')
                } else if (cell.textContent.toLowerCase() === "na") {
                    cell.textContent = 'NA'
                    cell.classList.add('grey')
                }
            });
        });

        // Show tooltip with details
        function showTooltip(itemDetails: { [key: string]: string }, statusKey: string) {
            const status_table = document.getElementById('ETLstatus') as HTMLTableElement;
            const tooltip = document.getElementById('tooltip') as HTMLDivElement;
            if (tooltip) {
                tooltip.innerHTML = `<strong>${statusKey}:</strong> ${itemDetails[statusKey]}`;
                tooltip.classList.add('show');
                document.addEventListener('mousemove', (e: MouseEvent) => {
                    tooltip.style.left = `${e.pageX + 10}px`;
                    tooltip.style.top = `${e.pageY + 10}px`;
                });
                status_table.addEventListener('mouseout', () => hideTooltip())
            }
        }

        function hideTooltip() {
            const tooltip = document.getElementById('tooltip') as HTMLDivElement;
            if(tooltip) {
                tooltip.classList.remove('show');
            }

        }

        // Open modal with ETL Response table
        function openModal(dataset_id: string, etlResponse: any, statusKey: string) {
            const etl_response_modal = document.getElementById('etl_modal') as HTMLDivElement;
            const etl_response_modalContent = document.getElementById('etl_modalContent') as HTMLDivElement;
            if (etl_response_modal && etl_response_modalContent) {
                if (statusKey === "Download Status" || statusKey == "ETL Status") {
                    etl_response_modalContent.innerHTML = '<div style="display:flex;flex-direction:row;font-weight:bold"><button id="download_btn">Download 30 days status</button><button id="closeModal" style="margin-left:auto; color:black; background-color: #fff; padding:0">X</button></div>'
                    etlResponse[statusKey].forEach((element: any[][]) => {
                        etl_response_modalContent.innerHTML += generateResponseTable(statusKey, element);
                    });
                    const download_btn = document.getElementById('download_btn')! as HTMLButtonElement;
                    download_btn.addEventListener('click', async() => {
                        await downloadHistoricalStatus(baseUrl, dataset_id)
                    })
                } else {
                    etl_response_modalContent.innerHTML = '<div style="display:flex;flex-direction:row;font-weight:bold"><button id="closeModal" style="margin-left:auto; color:black; background-color: #fff; padding:0">X</button></div>'
                    etl_response_modalContent.innerHTML += renderDetails(statusKey, etlResponse);
                }
                etl_response_modal.style.display = 'block';

                const closeModalBtn = document.getElementById("closeModal") as HTMLButtonElement;
                closeModalBtn.addEventListener('click', () => {
                    closeModal();
                })
                document.addEventListener('keyup',function(e){
                    if (e.key === "Escape") { 
                        closeModal();
                    }
                });

                const baseUrl =  `${window.location.protocol}//${window.location.host}`;

                async function downloadHistoricalStatus(baseUrl: string, dataset_id: string) {
                    const response = await fetch(`/api/download_historical_status/${dataset_id}`);
                    if (response.ok) {
                        const blob = await response.blob();
                        const url = window.URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = `${dataset_id}_${new Date().toISOString().split('T')[0]}.csv`;
                        document.body.appendChild(a);
                        a.click();
                        a.remove();
                    } else {
                        console.error('Failed to download file:', response.statusText);
                    }
                }
            }
            
        }

        function closeModal() {
            const etl_response_modal = document.getElementById('etl_modal') as HTMLDivElement;
            etl_response_modal.style.display = 'none';
        }

        function renderDetails(statusKey: string, etlStatusResponse: any) {
            let tableHTML = '<table id="modalTable" style="width: 100%; color: black;overflow:auto; margin-top:10px"><tbody>';
                Object.keys(etlStatusResponse).forEach(key => {
                    tableHTML += '<tr>'
                    tableHTML += `<td style="border: 1px solid #ddd;max-width:500px; font-weight:bold">${key}</td>`
                    if(etlStatusResponse[key] !== null && !["JFS Settlement Files", "ETL Settlement Files", "Dev POC", "Comment"].includes(key)){
                        tableHTML += `<td style="border: 1px solid #ddd;max-width:500px"><a href="${etlStatusResponse[key]}" target="_blank">${etlStatusResponse[key]}</a></td>`;
                    } else {
                        tableHTML += `<td style="border: 1px solid #ddd;max-width:500px">${etlStatusResponse[key]}</td>`;
                    }
                });
                tableHTML += '</tr>';
                tableHTML += '</tbody></table>';
            return tableHTML;
        }

        function max_length_object(response:any[]) {
            let maxAttributesObj:any[]|null = null
            let maxAttributeCount = -1
            for(const resp of response) {
                const attributesCount = Object.keys(resp).length
                if(attributesCount > maxAttributeCount) {
                    maxAttributeCount = attributesCount;
                    maxAttributesObj = resp;
                }
            }
            return maxAttributesObj
        }

        //Generate HTML table on click of any status in ETL status table
        function generateResponseTable(statusKey:string, response: any[]): string {
            let tableHTML = '<table id="modalTable" style="width: 100%; height:auto; color: black;overflow:auto; margin-top:10px"><thead><tr>';
            if (response.length > 0) {
                const resp = max_length_object(response)
                Object.keys(resp!).forEach(key => {
                    tableHTML += `<th style="color:#fff">${key}</th>`;
                });
                tableHTML += '</tr></thead><tbody>';
                response.forEach(row => {
                    tableHTML += '<tr>';
                    Object.values(row).forEach(value => {
                        if (typeof(value) === 'string' && value.toLowerCase() == 'missing') {
                            value = 'Missing'
                            tableHTML += `<td style="border: 1px solid #ddd; color: #FF0000">${value}</td>`;
                        } else if (typeof(value) === 'string' && value.toLowerCase() == 'anomaly') {
                            value = 'Anomaly'
                            tableHTML += `<td style="border: 1px solid #ddd; color: #ff7e00">${value}</td>`;
                        } else {
                            tableHTML += `<td style="border: 1px solid #ddd;max-width:500px">${value}</td>`;
                    }
                    });
                    tableHTML += '</tr>';
                });

                tableHTML += '</tbody></table>';
            }
            return tableHTML;
        }
    }
}

public async renderOnboardingDatasetTable() {
    const datePicker = document.getElementById('onboarding_datePicker') as HTMLInputElement;
    const getDataBtn = document.getElementById('onboarding_getDataBtn') as HTMLButtonElement;
    const selectedDateParagraph = document.getElementById('onboarding_selectedDate') as HTMLParagraphElement;

    let dateParam: string

    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are zero-based
    const day = String(today.getDate()).padStart(2, '0');
    const todayDate = `${year}-${month}-${day}`;
    datePicker.value = todayDate;
    dateParam = todayDate;
    selectedDateParagraph.textContent = `Displaying Onboarding Dataset Progress for date: ${todayDate}`

    let data: any[] = [];

    // Fetch data from API
    const response = await fetch(`${this.baseUrl}/api/getOnboardingDatasetStatus/${todayDate}`);
    data = await response.json();
    this.renderOnboardingTable(data)

    function formatDate(date: string): string {
        // Check if a date was selected
        if (!date) return '';
        // Format the date to yyyy-mm-dd
        const [year, month, day] = date.split('-');
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }
    getDataBtn.addEventListener('click', async () => {
        const selectedDate = datePicker.value;
        if (selectedDate) {
            const formattedDate = formatDate(selectedDate);
            dateParam = formattedDate;
            selectedDateParagraph.textContent = `Displaying Onboarding Dataset Progress for date: ${formattedDate}`;
            const response = await fetch(`${this.baseUrl}/api/getOnboardingDatasetStatus/${formattedDate}`);
            data = await response.json();
            this.renderOnboardingTable(data)
        } else {
            selectedDateParagraph.textContent = 'No date selected';
        }
    });

    function clearTable() {
        const table = document.getElementById('onboardingDatasetTable') as HTMLTableElement;
        table.innerHTML = ''
    }


function makeCellEditable(cell: HTMLTableCellElement, rowIndex: number, colIndex: number): void {
        const originalValue = cell.textContent || '';
        cell.contentEditable = "true";
        cell.focus();

        const onEnter = (event: KeyboardEvent) => {
            if (event.key === "Enter") {
                cell.contentEditable = "false";
                const newValue = cell.textContent || '';
                cell.removeEventListener("keydown", onEnter);
            }
        };

        cell.addEventListener("keydown", onEnter);
    }


}

clearTable() {
    const table = document.getElementById('appcatalogTable') as HTMLTableElement;
    table.innerHTML = ''
}

renderOnboardingTable(data: any[]) {
    let modifiedRows: Set<number> = new Set(); // To keep track of modified rows
    this.clearTable();
    const table_headers:{[key:string]:any} = {
        "Date": "transaction_time",
        "Dataset": "dataset",
        "Consumer": "consumer",
        "Estimated UAT Date": "estimated_uat_date",
        "UAT Status": "uat_status",
        "Estimated Go Live Date": "estimated_go_live_date",
        "Prod Status": "prod_status",
        "Daily Update": "daily_update",
    };
    const table = document.getElementById('onboardingDatasetTable') as HTMLTableElement;
    // Create table header
    const thead = table.createTHead();
    const headerRow = thead.insertRow();
    const headers = Object.keys(table_headers);
    headers.forEach(headerText => {
        const th = document.createElement('th');
        th.textContent = headerText;
        headerRow.appendChild(th);
    });
    // Create table body
    data.forEach((item, rowIndex) => {
        let filteredRowData: string[] = [];
        Object.values(table_headers).forEach((key) => {
            if (key in item) {
                if (key === 'transaction_time') {
                    const dateToBePushed: string = item[key];
                    filteredRowData.push(dateToBePushed.split('T')[0]);
                } else {
                    filteredRowData.push(item[key]);
                }
            }
        });
        const row = table.insertRow();
        filteredRowData.forEach((value, columnIndex) => {
            const cell = row.insertCell();
            cell.textContent = value as string;
            cell.contentEditable = 'false'; // Initially not editable
            cell.dataset.rowIndex = rowIndex.toString(); // Save row index
            cell.dataset.columnIndex = columnIndex.toString(); // Save column index
            // Add event listener to detect changes
            cell.addEventListener('input', () => {
                modifiedRows.add(rowIndex); // Track the entire row as modified
            });
        });
    });
    // Add event listener to the edit button
    document.getElementById('edit_status')?.addEventListener('click', () => {
        const cells = table.getElementsByTagName('td');
        Array.from(cells).forEach(cell => {
            cell.contentEditable = 'true'; // Make cells editable
        });
        document.getElementById('edit_status')!.style.display = 'none';
        document.getElementById('submit_status')!.style.display = 'inline';
        modifiedRows.clear(); // Clear previously modified rows on edit start
    });
    // Add event listener to the save button
    document.getElementById('submit_status')?.addEventListener('click', async () => {
        const updatedData: any[] = [];
        const rows = table.getElementsByTagName('tr');
        // Collect updated data for modified rows
        Array.from(rows).forEach((row, rowIndex) => {
            if (rowIndex === 0) return;
            if (modifiedRows.has(rowIndex-1)) {
                const rowData: any = {};
                const cells = row.getElementsByTagName('td');
                Array.from(cells).forEach((cell, columnIndex) => {
                    const headerKey = Object.keys(table_headers)[columnIndex];
                    const fieldName = table_headers[headerKey];
                    rowData[fieldName] = cell.textContent || '';
                });
                delete rowData.transaction_time;
                updatedData.push(rowData);
            }
        });
        // Send POST request with only updated rows

        const requestOptions = {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(updatedData),
          };

          const response = await fetch(`${this.baseUrl}/api/updateOnboardingDatasetStatus`, requestOptions);
        if (!response.ok) {
            console.log("Failed to submit")
        } else {
            console.log('Success:', data);
            alert("Status updated successfully")
            document.getElementById('edit_status')!.style.display = 'inline';
            document.getElementById('submit_status')!.style.display = 'none';
            const cells = table.getElementsByTagName('td');
            Array.from(cells).forEach(cell => {
                cell.contentEditable = 'false'; // Make cells non-editable
            });
            modifiedRows.clear(); // Clear the modified rows set
        }

        });

}

public async renderAppCatalogTable() {
    let data: any[] = [];

    // Fetch data from API
    const response = await fetch(`${this.baseUrl}/api/getAppcatalogData`);
    data = await response.json();
    console.log(data)

    const formattedData = data.map((row: any, index: number) => ({
        Action: index === 0 ? "Action": `<button class="action" id="view-row" data-id="row-${index}" data-action="view">View</button>&nbsp<button class="action edit hidden" id="edit-row" data-id="row-${index}" data-action="edit">Edit</button>&nbsp<button class="action deletehiddden" id="delete-row" data-id="row-${index}" data-action="delete">Delete</button>`,
        ...row,
        rowId: index === 0 ? "rowId" : `row-${index}`
    }));
    
    const appcatalog_table_headers:{[key:string]: string} = {
        "Action": "Action",
        "App ID": "app_id",
        "Product Name": "product_name",
        "App Name": "app_name",
        "App Link": "app_link",
        "Status": "status",
        "App Users List": "app_users_list",
        "Dev POC": "dev_poc",
        "Dev Lead": "dev_lead",
        "App Descriptions": "app_descriptions"
    };

    const appCatalogTable = new DataTable("appcatalogTable", formattedData, this.onTableRowClick.bind(this), this.currentUser, appcatalog_table_headers);   
    const appcatalog_tab = document.getElementById('appcatalog_tab')! as HTMLButtonElement;

    appcatalog_tab?.addEventListener('click', () => {
        console.log("appcatalog tab clicked")
        appCatalogTable.handleTabClick('appcatalog', formattedData, appcatalog_table_headers)
        appCatalogTable.data = formattedData
    })
}

}