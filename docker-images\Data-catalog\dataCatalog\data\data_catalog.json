{"headers": {"dataSetName": "dataSetName", "dataSetId": "dataSetId", "Vendor": "<PERSON><PERSON><PERSON>", "Dataset_Description": "Dataset_Description", "Dataset_Details": "Dataset_Details", "Data_Management_Lead": "Data_Management_Lead", "DM_Lead_Email": "DM_Lead_Email", "DM_Lead_Phone": "DM_Lead_Phone", "DM_Lead_Mobile": "DM_Lead_Phone", "Vendor_Contact_Other": "Vendor_Contact_Other", "Vendor_Contact_Title": "Vendor_Contact_Title", "Vendor_Contact_Work_Phone": "Vendor_Contact_Work_Phone", "Vendor_Contact_Mobile": "Vendor_Contact_Mobile", "Vendor_Contact_Email": "Vendor_Contact_Email", "Raw_Data_Location": "Raw_Data_Location", "Process_Data_Location": "Raw_Data_Location", "File_Type": "Raw_Data_Location", "Update_frequency": "Update_frequency", "Technical_Notes": "Technical_Notes", "GitHub_Repository": "GitHub_Repository", "Support_Procedures": "Support_Procedures", "File_names": "File_names", "SLA": "SLA", "Num_Files": "Num_Files", "Sourcing_Strategy": "Sourcing_Strategy", "Sourcing_URL": "Sourcing_URL", "Credentials": "Credentials", "Grafana_Alerts": "<PERSON><PERSON><PERSON>", "Dataset_Billing_Name": "Dataset_Billing_Name", "Tables_Loaded": "Tables_Loaded", "Airflow_Dag_Names": "Airflow_Dag_Names", "Slack_Channel_Name": "Slack_Channel_Name", "Teams_Channel_Name": "Teams_Channel_Name", "Vendor_Account_Manager": "Vendor_Account_Manager", "Vendor_Account_Manager_Work_Phone": "Vendor_Account_Manager_Work_Phone", "Vendor_Account_Manager_Mobile": "Vendor_Account_Manager_Mobile", "Vendor_Account_Manager_Email": "Vendor_Account_Manager_Email", "Vendor_Sales_Specialist": "Vendor_Sales_Specialist", "Vendor_Sales_Specialist_Work_Phone": "Vendor_Sales_Specialist_Work_Phone", "Vendor_Sales_Specialist_Mobile": "Vendor_Sales_Specialist_Mobile", "Vendor_Sales_Specialist_Email": "Vendor_Sales_Specialist_Email", "Vendor_Technical_Account_Manager": "Vendor_Technical_Account_Manager", "Vendor_Technical_Account_Manager_Work_Phone": "Vendor_Technical_Account_Manager_Work_Phone", "Vendor_Technical_Account_Manager_Mobile": "Vendor_Technical_Account_Manager_Mobile", "Vendor_Technical_Account_Manager_Email": "Vendor_Technical_Account_Manager_Email", "Customer_Success_Manager_product": "Customer_Success_Manager_product", "Customer_Success_Manager_product_Work_Phone": "Customer_Success_Manager_product_Work_Phone", "Customer_Success_Manager_product_Mobile": "Customer_Success_Manager_product_Mobile", "Customer_Success_Manager_product_Email": "Customer_Success_Manager_product_Email"}, "data": {"snpETF": {"dataSetId": "snpETF", "Vendor": "SnP", "Dataset_Description": "ETF Holdings", "Dataset_Details": "This dataset contains the ETF Holdings. SnP is providing a daily feed as well as 5 years work of history", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM Lead Mobile": "+****************", "Vendor_Contact_Other": "<PERSON><PERSON>", "Vendor_Contact_Title": "\nClient Services Analyst\nS&P Global Market Intelligence", "Vendor_Contact_Work_Phone": "+************ \n+1212-849-3733", "Vendor_Contact_Mobile": "******-752-3269", "Vendor_Contact_Email": "<EMAIL>", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "https://us-east-2.console.aws.amazon.com/s3/buckets/jg-s3-etl-<env>?region=us-east-2&bucketType=general&prefix=cache&showversions=false\n\nHere: env points to uat/tech1dev/tech1prod", "File_Type": "Iceberg Table", "Update_frequency": "Daily", "Technical_Notes": "This contains the ETF holdings data. The FTP server contains 2 variations of files. Each day has around 7.5-8K files, that need to get processed.\n\nHistorical files contain one zip file per month and one additional file per quarter.\nEach zip file contains a folder per day\nEach day folder contains multiple files per day.\nEach file contains 5 datasets\n\nIn FTP for the current dates, we have a day folder contains multiple files per day. Structure is similar to the historical folder.\nEach file contains 5 datasets, similar to he ones in the Historical Folders.", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Procedures": "TBD", "File_names": "Historical\nYYYY-MM.zip\nYYYY-MMDD.zip (for the last day fo the quarter)\nEach zip file will have thse file patterns\nETF_XXXXX_YYYYMMDD.txt\nETF_XXXXX_XXXXX_YYYYMMDD_XXXXX.txt\n\nDaily:\nETF_XXXXX_YYYYMMDD.txt\nETF_XXXXX_XXXXX_YYYYMMDD_XXXXX.txt", "SLA": "TBD", "Num_Files": "Approx 7500 to 8000 files", "Sourcing_Strategy": "FTP", "Sourcing_URL": "ffd.ebs.ihsmarkit.com ", "Credentials": "Username: Jainglobal\nPassword: Available in 1Password", "Grafana_Alerts": "To Be <PERSON>up", "Dataset_Billing_Name": "TBD", "Tables_Loaded": "Summary\nHeader\nBasket\nConstituent\nFXRate", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "snpEncylopedia": {"dataSetId": "snpEncylopedia", "Vendor": "SnP", "Dataset_Description": "Encyclopedia", "Dataset_Details": "This dataset contains the reference data to be used in conjunction with the ETF Holdings", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM Lead Mobile": "+****************", "Vendor_Contact_Other": "<PERSON><PERSON>", "Vendor_Contact_Title": "\nClient Services Analyst\nS&P Global Market Intelligence", "Vendor_Contact_Work_Phone": "+************ \n+1212-849-3733", "Vendor_Contact_Mobile": "******-752-3269", "Vendor_Contact_Email": "<EMAIL>", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "https://us-east-2.console.aws.amazon.com/s3/buckets/jg-s3-etl-<env>?region=us-east-2&bucketType=general&prefix=cache&showversions=false\n\nHere: env points to uat/tech1dev/tech1prod", "File_Type": "Iceberg Table", "Update_frequency": "Daily", "Technical_Notes": "Historical Files are available since 2021. Each file contians just one table to be loaded with has 183 columms\n\nDaily files are not zipped.", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Procedures": "TBD", "File_names": "Historical\nEtp Encyclopedia Primary Listing_YYYYMMDD_2100.zip\nDaily:\nEtp_Encyclopedia_Primary_Listing_YYYYMMDD_1428.txt", "SLA": "TBD", "Num_Files": "1 file per day", "Sourcing_Strategy": "FTP", "Sourcing_URL": "ffd.ebs.ihsmarkit.com ", "Credentials": "Username: Jainglobal\nPassword: Available in 1Password", "Grafana_Alerts": "To Be <PERSON>up", "Dataset_Billing_Name": "TBD", "Tables_Loaded": "Encyclopedia", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "bloombergHoldings": {"dataSetId": "bloombergHoldings", "Vendor": "Bloomberg", "Dataset_Description": "Bloomberg Holdings", "Dataset_Details": "This dataset contains the ETF holidings data", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM Lead Mobile": "+****************", "Vendor_Contact_Other": "\n<PERSON><PERSON>", "Vendor_Contact_Title": "Bloomberg Enterprise Data\nSolutions Engineers", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "\n<EMAIL>\n\n<EMAIL>\n\n<EMAIL>\n\n<EMAIL>", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "https://us-east-2.console.aws.amazon.com/s3/buckets/jg-s3-etl-<env>?region=us-east-2&bucketType=general&prefix=cache&showversions=false\n\nHere: env points to uat/tech1dev/tech1prod", "File_Type": "Iceberg Table", "Update_frequency": "Daily", "Technical_Notes": "All the data is in an S3 bucket that has to be requested via an API/or aws cli.\n\nThe Bloomberg Handbook can be used to check the commands.\nThe files are Avro files that can be loaded into a pandas dataframe", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Procedures": "TBD", "File_names": "History:\nequityEtf<region>HistorySnapshot.avro\n\nThe historical files are in an S3 bucket with the Location having the date\n\nDaily:\nequityEtf<region><time>.avro\n\nTime is either 0030,  0830 or 1630\n\nWe had an option of using S3 or FTP and have gone ahead with S3", "SLA": "TBD", "Num_Files": "3 Daily files", "Sourcing_Strategy": "S3", "Sourcing_URL": "aws s3api list-objects-v2 --bucket arn:aws:s3:us-east-1:************:accesspoint/dl-c0a4e143-ae06-4a08-b0ed-5483f78a53ec --prefix \"GuXDxca3/catalogs/bbg/datasets/<datasetPrefix>\" --request-payer requester", "Credentials": "We need to provide the role that can be used to download the data from their S3 bucker", "Grafana_Alerts": "To Be <PERSON>up", "Dataset_Billing_Name": "TBD", "Tables_Loaded": "equity_etf_bbGlobal_xref\nequity_etf_positions\nequity_etf", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "\n<PERSON><PERSON>", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "Bloomberg Enterprise Data\nSolutions Engineers\n\n<EMAIL>\n\n<EMAIL>\n\n<EMAIL>\n\n<EMAIL>", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "bloombergReference": {"dataSetId": "bloombergReference", "Vendor": "Bloomberg", "Dataset_Description": "Bloomberg Ref Data", "Dataset_Details": "This contains the reference tables that are to be used in conjunction with the holdings", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM Lead Mobile": "+****************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "<PERSON>\n(BLOOMBERG/ PRINCETON)", "Vendor_Contact_Work_Phone": null, "Vendor_Contact_Mobile": null, "Vendor_Contact_Email": "<EMAIL>\n<EMAIL>\ncko<PERSON>@bloomberg.net\n<EMAIL>\n<EMAIL>", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "https://us-east-2.console.aws.amazon.com/s3/buckets/jg-s3-etl-<env>?region=us-east-2&bucketType=general&prefix=cache&showversions=false\n\nHere: env points to uat/tech1dev/tech1prod", "File_Type": "Iceberg Table", "Technical_Notes": "Historical files contain multiple zip files - one for each type of record. There is one file per type of data, per month. The number of files are a lot to list here.\nEach file contains a format wherein the file is the implied table name, the file has START-OF-FIELDS which has the column names, followed by the data.\nThere could be variations of this.\nOur parsing logic will use this information to create the csv per type of filethat has to be loaded in iceberg.\n\nThis feed contains all the reference data that bloomberg provides us.\nThis reference data will be used along with our ETPs and ETFs\nThe Expectation is to load all files that starts with \"etp\" or \"fund\", excluding \"dif\", \"Alloc\", \"cins\", and \"Bulk\" files.", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Procedures": "TBD", "File_names": "etp<region>.out\netp<region>.px\netp<region>.rpx\n\nfund<region>.out\nfund<region>.px\nfund<region>.rpx\n\nRegions are Asia1, Asia2, <PERSON><PERSON>, <PERSON>r, Name", "Update_frequency": "Daily", "SLA": "TBD", "Num_Files": 27, "Sourcing_Strategy": "S3", "Sourcing_URL": "aws s3api list-objects-v2 --bucket arn:aws:s3:us-east-1:************:accesspoint/dl-c0a4e143-ae06-4a08-b0ed-5483f78a53ec --prefix \"GuXDxca3/catalogs/bbg/datasets/<datasetPrefix>\" --request-payer requester", "Credentials": "We need to provide the role that can be used to download the data from their S3 bucker", "Grafana_Alerts": "To Be <PERSON>up", "Dataset_Billing_Name": "TBD", "Tables_Loaded": "etp_out\netp_px\netp_rpx\nfund_px\nfund_rpx\nfund_out", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "MSCIData": {"dataSetId": "MSCIData", "Vendor": "MSCI", "Dataset_Description": "MSCI Data", "Dataset_Details": "This contains Index Feeds from MSCI which will be used as benchmarks for our comparisons and strategies.", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM Lead Mobile": "+****************", "Vendor_Contact_Other": "Nick Story\n\nPEPE GARZA", "Vendor_Contact_Title": "\nVice President | Index Client Coverage | \n\n\nIndex Client Service Specialist ", "Vendor_Contact_Work_Phone": "Pepe: ****** 804 1536\n\nCS Helpline: ****** 588 4567 ", "Vendor_Contact_Mobile": "Nick: ****** 361 3723", "Vendor_Contact_Email": "<EMAIL>\n\n<EMAIL>\n\n<EMAIL>", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "https://us-east-2.console.aws.amazon.com/s3/buckets/jg-s3-etl-<env>?region=us-east-2&bucketType=general&prefix=cache&showversions=false\n\nHere: env points to uat/tech1dev/tech1prod", "File_Type": "Iceberg Table", "Update_frequency": "Daily", "Technical_Notes": "Historical files contain multiple zip files - one for each type of record. There is one file per type of month per type. \nThe file contains a format where they have the table name followed by the columns, followed by the data. \nEach zip file will be processed by looking at the information and loading the data into the appropriate table.\nWe would be parsing the data for 5 years and loading the multiple tables\n\nFor Daily feeds we will get multiple zip files per day. Again, every file in the zip file will be processed and loaded to the table mentioned in the file", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Procedures": "TBD", "File_names": "Historical: \nYYYYMMDD_YYYYMMDD_<type>.zip\nThese are the different type we have for historical files.\nd15d.extension, d15d_rif, d15e.extension, d15e_rif, d16d_rif, d16e_rif, d17d.extension, d17d_rif, d17e.extension, d17e_rif, d18d_rif, d18e_rif, d51d, d51e, d52d, d52e, d95d.vg.extension, d95d.vg_rif, d95e.vg.extension, d95e.vg_rif, d96d, d96e, d98d_rif, d98e_rif, d99d_rif, d99e_rif, d_5h, d_icf_core_idx_oc, d_icf_core_idxsec_oc_d_rif, d_icf_sc_idxsec_oc_d_rif, m15d.extension, m15d_rif, m15e.extension, m15e_rif, m17d.extension, m17d_rif, m17e.extension, m17e_rif, m51d, m51e, m52d, m52e, m95d.vg.extension, m95d.vg_rif, m95e.vg.extension, m95e.vg_rif, m96d, m96e, m98d_rif, m98e_rif, m99d_rif, m99e_rif, m_5h, m_bd, m_icf_core_idx_oc, m_icf_core_idxsec_oc_d_rif, m_icf_sc_idxsec_oc_d_rif\n\nDaily\nFormat1: <MMDD><Type>.zip\nThese are the different types for Format1: d_tarif, d_tdrif, d_terif, d_tfrif, dstarif, dstdrif, dsterif, dstfrif, china_bin_securities\n\nFormat2: <YYYYMMDD><Type>.zip\nThese are the different types for Format2:\n_index_description_dom_int, daily_icfcoroc_d_rif, daily_icfcorts_amer_d_rif, daily_icfcorts_dm_d_rif, daily_icfcorts_eafe_d_rif, daily_icfcorts_em_d_rif, daily_icfscoc_d_rif, dm_ace_rif, em_ace_rif, sc_ace_rif, scem_ace_rif,", "SLA": "TBD", "Num_Files": "9 files in Format1\n\n11 Files in Format2", "Sourcing_Strategy": "FTP", "Sourcing_URL": "sftp://sftp.msci.com:22", "Credentials": "Username: gwgikulw\nPassword: Available in 1Password", "Grafana_Alerts": "To Be <PERSON>up", "Dataset_Billing_Name": "TBD", "Tables_Loaded": "DAILY_TRACKER_XRATES\nINDEX_REPORT\nINDEX_SAME_DAY\nSECURITY_REPORT\nOFFICIAL_DTR_SECURITY_FILE\nADVANCED_DIVIDENDS_FILE\nSECURITY_CODE_MAP\nCOUNTRY_<PERSON>ATA\nINDEX_DESCRIPTION\nCOUNTRIES_WEIGHT\nSECURITY_SMALL_CAP_REPORT\nSECURITY_VG_REPORT\nVENDOR_BOXES_REPORT", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "CRSP": {"dataSetId": "CRSP", "Vendor": "CRSP", "Dataset_Description": "Index Prices and Returns", "Dataset_Details": "This dataset contains pricing and returns for multipe listed indices.", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM Lead Mobile": "+****************", "Vendor_Contact_Other": "<PERSON>", "Vendor_Contact_Title": "\nSr. Support and Relationship Specialist", "Vendor_Contact_Work_Phone": "******.263.6400", "Vendor_Contact_Mobile": "******.263.6332", "Vendor_Contact_Email": null, "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "https://us-east-2.console.aws.amazon.com/s3/buckets/jg-s3-etl-<env>?region=us-east-2&bucketType=general&prefix=cache&showversions=false\n\nHere: env points to uat/tech1dev/tech1prod", "File_Type": "Iceberg Table", "Update_frequency": "Daily", "Technical_Notes": "TBD", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Procedures": "TBD", "File_names": "Historical\nhistory_icl/YYYY/YYYY_crsp_index_constituents_open_PF_V2.zip\nhistory_icl/YYYY/YYYY_<Quarter>_crsp_index_constituents_open_V2.zip\nhistory_icl/YYYY/YYYY_<Quarter>_crsp_index_constituents_open_Proj_V2.zip\n\nhistory_icl/YYYY/YYYY_crsp_index_constituents_close_PF_V2.zip\nhistory_icl/YYYY/YYYY_<Quarter>_crsp_index_constituents_close_V2.zip\n\n/history_index_levels/YYYY/YYYY_<Quarter>_crsp_index_levels.zip\n\n/history_indexchanges/crsp_indexchanges_YYYY.zip\n/history_indexchanges/crsp_indexchanges_YYYY_<Quarter>.zip\n\nDaily \n/icl_close/crsp_index_constituents_close_V2_2024MMDD.txt\n/icl_open/crsp_index_constituents_open_V2_2024MMDD.txt\n/index_levels/crsp_index_levels_2024MMDDD.txt\n/indexchanges/crsp_indexchanges_2024MMDD_HHMM.txt [9 files per date]\n/indexchanges5/crsp_indexchanges5_2024MMDD_HHMM.txt", "SLA": "TBD", "Num_Files": "TBD", "Sourcing_Strategy": "FTP", "Sourcing_URL": "ftp.crspmi.org:22", "Credentials": "Username: jainglobal\nPassword: Available in 1Password", "Grafana_Alerts": "To Be <PERSON>up", "Dataset_Billing_Name": "TBD", "Tables_Loaded": "icl_close\nicl_open\nindex_changes\nindex_changes5\nindex_level", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "<EMAIL>", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}}}