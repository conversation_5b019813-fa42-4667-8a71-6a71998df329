# Bloomberg Snap EOD Process Documentation

## Overview

The Bloomberg Snap EOD (End of Day) process is a comprehensive data pipeline that retrieves real-time and historical pricing data from Bloomberg's Snap API. The process supports both BDH (Bloomberg Data History) and BDP (Bloomberg Data Point) requests to capture snap prices at specific times throughout the day across different time zones.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Airflow DAGs  │───▶│ bloomberg.snap.  │───▶│ Bloomberg Snap API  │
│   (Schedulers)  │    │ gl.py (Entry)    │    │ (302/309 Endpoints) │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │ Configuration    │
                       │ (Snowflake)      │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │ Data Processing  │
                       │ & Storage        │
                       └──────────────────┘
```

## Core Components

### 1. Entry Point Script
**File**: `source/bin/bloomberg.snap.gl.py`

This is the main entry point that accepts snap time and timezone parameters:
```bash
python3 bloomberg.snap.gl.py --st 1100 --tz "Asia/Singapore"
```

**Parameters**:
- `--st`: Snap time (e.g., 1100, 1600)
- `--tz`: Timezone (e.g., "UTC", "Asia/Singapore", "America/New_York")

### 2. Airflow DAG Structure

The process is orchestrated through multiple Airflow DAGs scheduled at different times and timezones:

**DAG Naming Convention**: `jg-bloombergsnap-eod-{TIME}-{TIMEZONE}.py`

Examples:
- `jg-bloombergsnap-eod-1100-hk.py` - 11:00 AM Hong Kong time
- `jg-bloombergsnap-eod-1600-utc.py` - 4:00 PM UTC
- `jg-bloombergsnap-eod-0700-utc.py` - 7:00 AM UTC

**DAG Components**:
1. **ETL Job**: Executes the Bloomberg Snap data retrieval
2. **Validation Job**: Calls data validation API to check for anomalies
3. **Optional DQ Check**: Data quality validation (currently commented out)

### 3. Configuration Management

**Configuration Tables**:
- `BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG` (View)
- `APACST_PROD_DB.APAC_MACRO.BLOOMBERG_SNAP_CONFIG` (Base table)
- `APACST_PROD_DB.APAC_MACRO1.BLOOMBERG_SNAP_CONFIG` (Base table)
- `DATA_PLATFORM_CORE.CONFIG.BLOOMBERG_SNAP_CONFIG` (Base table)

**Configuration Schema**:
```sql
IDENTIFIER VARCHAR(300)           -- Bloomberg ticker/identifier
FIELD VARCHAR(300)               -- Bloomberg field (e.g., PX_LAST, PX_VOLUME)
SNAP_TYPE VARCHAR(300)           -- BDH, BDP, BDP-DYNAMIC
SNAP_TIME VARCHAR(300)           -- Time slots (pipe-separated: "1100|1600")
IS_ACTIVE INTEGER                -- Active flag (1=active, 0=inactive)
TIMEZONE VARCHAR(20)             -- Timezone for the snap
IDENTIFIER_OVERRIDES VARCHAR(300) -- JSON overrides for field parameters
```

### 4. Data Processing Flow

#### Main Processing Function
**File**: `source/jgdata/datasets/bloomberg/snap/bloombergsnap.py`

**Function**: `build_bsnap_payload(st, tz)`

**Process Flow**:
1. **Configuration Retrieval**: Query Snowflake for active configurations matching snap time and timezone
2. **Data Type Separation**: Split requests into BDH and BDP types
3. **API Calls**: Execute Bloomberg API requests
4. **Data Processing**: Transform and validate responses
5. **Storage**: Insert processed data into Snowflake

#### BDH Processing (Historical Data)
**Function**: `build_309()`
**API Endpoint**: Bloomberg 309 History Request API

**Process**:
1. Groups tickers by field type
2. Creates HistoryRequest payload with date range (current date - 15 days to current date)
3. Calls `call_main_309_for_ref_date_api()`
4. Processes time-series data
5. Stores in `BLOOMBERG.SNAP.DATA_FEED` table

#### BDP Processing (Point-in-Time Data)
**Function**: `build_302_dynamic()`
**API Endpoint**: Bloomberg 302 Data Request API

**Process**:
1. Groups tickers by field type
2. Creates DataRequest payload with field overrides
3. Calls `call_302_api()`
4. Processes current point-in-time data
5. Stores in `BLOOMBERG.SNAP.DATA_FEED` table

### 5. Bloomberg API Integration

#### API 309 (Historical Data)
**File**: `source/jgdata/datasets/bloomberg/snap_api/python/bloomberg/persecurity/py/bbg_309_history_request1.py`

**Request Type**: HistoryRequest
**Purpose**: Retrieve historical time-series data
**Date Range**: Configurable (typically 15 days)

**Payload Structure**:
```json
{
  "@type": "HistoryRequest",
  "name": "Python309HistoryRequest{uuid}",
  "universe": {
    "@type": "Universe",
    "contains": [{"@type": "Identifier", "identifierType": "TICKER", "identifierValue": "ticker"}]
  },
  "fieldList": {
    "@type": "HistoryFieldList",
    "contains": [{"mnemonic": "field_name"}]
  },
  "runtimeOptions": {
    "@type": "HistoryRuntimeOptions",
    "dateRange": {
      "@type": "IntervalDateRange",
      "startDate": "start_date",
      "endDate": "end_date"
    }
  }
}
```

#### API 302 (Point Data)
**File**: `source/jgdata/datasets/bloomberg/snap_api/python/bloomberg/persecurity/py/bbg_302_nested_data_request.py`

**Request Type**: DataRequest
**Purpose**: Retrieve current point-in-time data with field overrides

**Payload Structure**:
```json
{
  "@type": "DataRequest",
  "name": "Python302NestedDataRequest{uuid}",
  "universe": {
    "@type": "Universe",
    "contains": [
      {
        "@type": "Identifier",
        "identifierType": "TICKER",
        "identifierValue": "ticker",
        "fieldOverrides": [
          {
            "@type": "FieldOverride",
            "mnemonic": "override_field",
            "override": "override_value"
          }
        ]
      }
    ]
  },
  "fieldList": {
    "@type": "DataFieldList",
    "contains": [
      {"mnemonic": "field_name"},
      {"mnemonic": "REFERENCE_DATE"}
    ]
  }
}
```

### 6. Data Storage

**Target Table**: `BLOOMBERG.SNAP.DATA_FEED`

**Schema**:
```sql
SNAP_DATE DATE                    -- Date of the snap
IDENTIFIER VARCHAR(16777216)      -- Bloomberg identifier
ATTRIBUTE VARCHAR(16777216)       -- Bloomberg field name
VALUE VARCHAR(16777216)           -- Retrieved value
AVAILDATE TIMESTAMP_NTZ(9)        -- Timestamp when data became available
REF_DATE TIMESTAMP_NTZ(9)         -- Reference date for the data
DL_REQUEST_ID VARCHAR             -- Request tracking ID
```

## Scheduling and Time Zones

### EOD Schedules
The process runs at multiple times throughout the day to capture different market closes:

**UTC Schedules**:
- 07:00 UTC - European morning
- 10:00 UTC - European close
- 13:00 UTC - Asian afternoon
- 16:00 UTC - US morning
- 18:00 UTC - US afternoon
- 19:00 UTC - US close

**Asia/Singapore Schedules**:
- 10:00 SGT - Asian morning
- 11:00 SGT - Asian midday
- 12:00 SGT - Asian afternoon
- 13:00 SGT - Asian close
- 16:00 SGT - European morning

**America/New_York Schedules**:
- 11:00 EST - US morning (intraday)

### Cron Configuration
Each DAG uses `CronTriggerTimetable` for precise scheduling:
```python
schedule = CronTriggerTimetable('0 11 * * *', timezone="Asia/Singapore")
```

## Error Handling and Monitoring

### Retry Logic
- **Retries**: 1 attempt with 1-minute delay
- **Timeout**: 30 minutes per task
- **Failure Handling**: Comprehensive logging with stdout/stderr capture

### Data Validation
- **HTTP Validation**: Calls validation API endpoint
- **Anomaly Detection**: `check_for_anomalies` function
- **Data Quality**: Optional DQ checks (currently disabled)

### Logging
- Detailed logging at each processing step
- API request/response logging
- Data transformation logging
- Snowflake insertion logging

## Configuration Examples

### Sample Configuration Entry
```sql
INSERT INTO BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG VALUES (
    'AAPL US Equity',           -- IDENTIFIER
    'PX_LAST',                  -- FIELD
    'BDP',                      -- SNAP_TYPE
    '1100|1600',               -- SNAP_TIME (multiple times)
    1,                          -- IS_ACTIVE
    'UTC',                      -- TIMEZONE
    '{"mnemonic":"PRICING_SOURCE","override":"BGN"}'  -- IDENTIFIER_OVERRIDES
);
```

### Field Override Example
```json
{
  "mnemonic": "PRICING_SOURCE",
  "override": "BGN"
}
```

## Adhoc Processing

**File**: `jg-bloombergsnap-adhoc.py`

Supports on-demand processing with custom configurations:
```python
snap_configs = [
    "IDENTIFIER=ABC, SNAP_TYPE=BDH, ATTRIBUTE=PX_LAST",
    "IDENTIFIER=XYZ, SNAP_TYPE=BDP, ATTRIBUTE=PX_LAST"
]
```

## Dependencies

### External Dependencies
- Bloomberg Terminal/API access
- Bloomberg user terminal number configuration
- Network connectivity to Bloomberg Snap API endpoints

### Internal Dependencies
- Snowflake database connectivity
- Airflow orchestration platform
- Python environment with required packages
- JG Data platform infrastructure

## Troubleshooting

### Common Issues
1. **API Timeout**: Increase timeout settings or reduce batch size
2. **Configuration Errors**: Verify BLOOMBERG_SNAP_CONFIG entries
3. **Network Issues**: Check Bloomberg API connectivity
4. **Data Quality**: Review validation rules and thresholds

### Monitoring Points
- DAG execution status in Airflow
- Data validation API responses
- Snowflake table row counts
- Bloomberg API response times

## BDH vs BDP: Static vs Dynamic Processing Explained

### Overview of Bloomberg Data Types

The Bloomberg Snap process handles three distinct data request types, each with specific characteristics and use cases:

1. **BDH (Bloomberg Data History)** - Historical time-series data
2. **BDP (Bloomberg Data Point)** - Current point-in-time data
3. **BDP-DYNAMIC** - Point-in-time data with time-sensitive reference dates
4. **BDP-STATIC** - Point-in-time data with static reference handling

### Processing Flow Separation

<augment_code_snippet path="source/jgdata/datasets/bloomberg/snap/bloombergsnap.py" mode="EXCERPT">
````python
def build_all(df, obj_sf, st, tz, ticker_list, type='scheduled'):
    # Separate processing by SNAP_TYPE
    logger.info("Qualified Ticker to go to BDH")
    logger.info(df[(df['SNAP_TYPE'] == 'BDH')])
    build_309(df[(df['SNAP_TYPE'] == 'BDH')], sd, ed, obj_sf, st, tz, type)

    logger.info(df[df['SNAP_TYPE'].isin(['BDP-DYNAMIC', 'BDP'])])
    build_302_dynamic(df[df['SNAP_TYPE'].isin(['BDP-DYNAMIC', 'BDP'])], sd, ed, obj_sf, st, tz, ticker_list, type)

    logger.info("Qualified Ticker to go to BDP_STATIC")
    logger.info(df[(df['SNAP_TYPE'] == 'BDP-STATIC')])
    build_302_static(df[(df['SNAP_TYPE'] == 'BDP-STATIC')], sd, ed, obj_sf, st, tz, type)
````
</augment_code_snippet>

### 1. BDH Processing (Historical Data - API 309)

**Purpose**: Retrieve historical time-series data over a date range
**API Endpoint**: Bloomberg 309 History Request API
**Function**: `build_309()`

#### Key Characteristics:
- **Time Range**: Retrieves data for a 15-day window (current date - 15 days to current date)
- **Data Type**: Historical time-series with multiple data points per identifier
- **Use Cases**: Price history, volume history, volatility analysis
- **Reference Date**: Each data point has its own historical reference date

#### Processing Logic:
1. **Grouping**: Groups tickers by field type (e.g., PX_LAST, PX_VOLUME)
2. **API Call**: Creates HistoryRequest payload with date range
3. **Data Structure**: Returns multiple rows per identifier (one per historical date)
4. **Storage**: Each historical data point stored with its original reference date

#### Example Configuration:
```sql
INSERT INTO BLOOMBERG_SNAP_CONFIG VALUES (
    'AAPL US Equity',    -- IDENTIFIER
    'PX_LAST',          -- FIELD
    'BDH',              -- SNAP_TYPE
    '1600',             -- SNAP_TIME
    1,                  -- IS_ACTIVE
    'UTC',              -- TIMEZONE
    NULL                -- IDENTIFIER_OVERRIDES
);
```

#### API Payload Structure:
```json
{
  "@type": "HistoryRequest",
  "runtimeOptions": {
    "@type": "HistoryRuntimeOptions",
    "dateRange": {
      "@type": "IntervalDateRange",
      "startDate": "2024-01-15",  // Current date - 15 days
      "endDate": "2024-01-30"     // Current date
    }
  }
}
```

### 2. BDP-DYNAMIC Processing (Dynamic Point Data - API 302)

**Purpose**: Retrieve current point-in-time data with time-sensitive reference date handling
**API Endpoint**: Bloomberg 302 Data Request API
**Function**: `build_302_dynamic()`

#### Key Characteristics:
- **Time Sensitivity**: Reference date is dynamically set based on API response
- **Data Type**: Single current value per identifier
- **Use Cases**: Real-time prices, current market data, live quotes
- **Reference Date**: Uses Bloomberg's REFERENCE_DATE field or current timestamp

#### Processing Logic:
1. **Dynamic Reference Date**:
   <augment_code_snippet path="source/jgdata/datasets/bloomberg/snap/bloombergsnap.py" mode="EXCERPT">
   ````python
   # Dynamic reference date handling
   df_302_api_response_final['ref_date'] = df_302_api_response_final['REFERENCE_DATE'].apply(
       lambda x: x if pd.notnull(x) else pd.Timestamp.utcnow().strftime('%Y-%m-%d')
   )
   ````
   </augment_code_snippet>

2. **Duplicate Detection**: Compares against existing data using REF_DATE + IDENTIFIER + ATTRIBUTE
   <augment_code_snippet path="source/jgdata/datasets/bloomberg/snap/bloombergsnap.py" mode="EXCERPT">
   ````python
   # Dynamic duplicate detection includes REF_DATE
   existing_df = existing_df.drop_duplicates(subset=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'VALUE_TRANSFORM'])
   df_merged = df_302_api_response_final.merge(existing_df, on=['REF_DATE', 'IDENTIFIER', 'ATTRIBUTE'], how='left', indicator=True)
   ````
   </augment_code_snippet>

3. **Time-Aware Querying**: Filters existing data by specific reference dates
   ```sql
   WHERE AVAILDATE = (SELECT MAX(AVAILDATE) FROM BLOOMBERG.SNAP.DATA_FEED df2
         WHERE df.IDENTIFIER = df2.IDENTIFIER
         AND df.ATTRIBUTE = df2.ATTRIBUTE
         AND df.REF_DATE = df2.REF_DATE)  -- Time-specific filtering
   AND REF_DATE IN ('2024-01-30 00:00:00')
   ```

#### Use Case Example:
- **Scenario**: Real-time equity prices that change throughout the day
- **Behavior**: Each price update gets stored with its specific timestamp
- **Result**: Multiple entries per day for the same identifier, each with different REF_DATE

### 3. BDP-STATIC Processing (Static Point Data - API 302)

**Purpose**: Retrieve current point-in-time data with static reference date handling
**API Endpoint**: Bloomberg 302 Data Request API
**Function**: `build_302_static()`

#### Key Characteristics:
- **Time Insensitive**: Reference date handling is simplified
- **Data Type**: Single current value per identifier
- **Use Cases**: Static reference data, end-of-day snapshots, daily closes
- **Reference Date**: Uses Bloomberg's REFERENCE_DATE field without fallback

#### Processing Logic:
1. **Static Reference Date**:
   <augment_code_snippet path="source/jgdata/datasets/bloomberg/snap/bloombergsnap.py" mode="EXCERPT">
   ````python
   # Static reference date handling (no fallback to current time)
   df_302_api_response_final['ref_date'] = df_302_api_response_final['REFERENCE_DATE'].apply(
       lambda x: x if pd.notnull(x) else None
   )
   ````
   </augment_code_snippet>

2. **Simplified Duplicate Detection**: Ignores REF_DATE in comparison
   <augment_code_snippet path="source/jgdata/datasets/bloomberg/snap/bloombergsnap.py" mode="EXCERPT">
   ````python
   # Static duplicate detection excludes REF_DATE
   existing_df = existing_df.drop_duplicates(subset=['IDENTIFIER', 'ATTRIBUTE', 'VALUE', 'VALUE_TRANSFORM'])
   df_merged = df_302_api_response_final.merge(existing_df, on=['IDENTIFIER', 'ATTRIBUTE'], how='left', indicator=True)
   ````
   </augment_code_snippet>

3. **Time-Agnostic Querying**: Gets latest data regardless of reference date
   ```sql
   WHERE AVAILDATE = (SELECT MAX(AVAILDATE) FROM BLOOMBERG.SNAP.DATA_FEED df2
         WHERE df.IDENTIFIER = df2.IDENTIFIER
         AND df.ATTRIBUTE = df2.ATTRIBUTE)  -- No REF_DATE filtering
   ```

#### Use Case Example:
- **Scenario**: Daily closing prices or static reference data
- **Behavior**: Only stores the latest value, replacing previous entries
- **Result**: One entry per identifier per day, regardless of when retrieved

### Key Differences Summary

| Aspect | BDH (309) | BDP-DYNAMIC (302) | BDP-STATIC (302) |
|--------|-----------|-------------------|------------------|
| **Data Range** | Historical (15 days) | Current point-in-time | Current point-in-time |
| **API Endpoint** | 309 History Request | 302 Data Request | 302 Data Request |
| **Reference Date** | Historical dates | Dynamic (API or current) | Static (API only) |
| **Duplicate Logic** | By date range | REF_DATE + ID + ATTR | ID + ATTR only |
| **Storage Pattern** | Multiple rows/ID | Time-stamped entries | Latest value only |
| **Use Case** | Trend analysis | Real-time monitoring | Daily snapshots |
| **Update Frequency** | Batch historical | Continuous updates | Daily updates |

### Data Deduplication Strategy

#### Value Tolerance Comparison
All three processes use a tolerance-based comparison to detect meaningful changes:

<augment_code_snippet path="source/jgdata/datasets/bloomberg/snap/bloombergsnap.py" mode="EXCERPT">
````python
tolerance = 0.0001
df_merged['value_diff'] = df_merged['VALUE_TRANSFORM_x'] - df_merged['VALUE_TRANSFORM_y']

# Only insert if new data or significant value change
df_toBeLoaded = df_merged[
    (df_merged['_merge'] == 'left_only') |
    ((df_merged['_merge'] == 'both') & (df_merged['value_diff'].abs() > tolerance))
]
````
</augment_code_snippet>

This ensures that:
- **New data** is always inserted
- **Existing data** is only updated if the value change exceeds 0.0001
- **Noise** and rounding differences are filtered out

### Configuration Examples

#### BDH Configuration (Historical Data)
```sql
-- Get 15 days of price history
INSERT INTO BLOOMBERG_SNAP_CONFIG VALUES (
    'SPY US Equity', 'PX_LAST', 'BDH', '1600', 1, 'UTC', NULL
);
```

#### BDP-DYNAMIC Configuration (Real-time Data)
```sql
-- Get real-time prices with timestamps
INSERT INTO BLOOMBERG_SNAP_CONFIG VALUES (
    'SPY US Equity', 'PX_LAST', 'BDP-DYNAMIC', '1600', 1, 'UTC',
    '{"mnemonic":"PRICING_SOURCE","override":"BGN"}'
);
```

#### BDP-STATIC Configuration (Daily Snapshots)
```sql
-- Get daily closing price
INSERT INTO BLOOMBERG_SNAP_CONFIG VALUES (
    'SPY US Equity', 'PX_LAST', 'BDP-STATIC', '1600', 1, 'UTC', NULL
);
```

### Performance Considerations

1. **BDH Processing**: Higher data volume due to historical range
2. **BDP-DYNAMIC**: More frequent updates, higher storage requirements
3. **BDP-STATIC**: Most efficient for daily reporting needs
4. **API Limits**: All types respect Bloomberg API rate limits and batching

## Future Enhancements

1. **Real-time Processing**: Implement streaming data ingestion
2. **Enhanced Error Recovery**: Automatic retry with exponential backoff
3. **Performance Optimization**: Parallel processing for large datasets
4. **Extended Coverage**: Additional Bloomberg fields and markets
