# Bloomberg Snap EOD Process Documentation

## Overview

The Bloomberg Snap EOD (End of Day) process is a comprehensive data pipeline that retrieves real-time and historical pricing data from Bloomberg's Snap API. The process supports both BDH (Bloomberg Data History) and BDP (Bloomberg Data Point) requests to capture snap prices at specific times throughout the day across different time zones.

## Architecture Overview

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   Airflow DAGs  │───▶│ bloomberg.snap.  │───▶│ Bloomberg Snap API  │
│   (Schedulers)  │    │ gl.py (Entry)    │    │ (302/309 Endpoints) │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │ Configuration    │
                       │ (Snowflake)      │
                       └──────────────────┘
                                │
                                ▼
                       ┌──────────────────┐
                       │ Data Processing  │
                       │ & Storage        │
                       └──────────────────┘
```

## Core Components

### 1. Entry Point Script
**File**: `source/bin/bloomberg.snap.gl.py`

This is the main entry point that accepts snap time and timezone parameters:
```bash
python3 bloomberg.snap.gl.py --st 1100 --tz "Asia/Singapore"
```

**Parameters**:
- `--st`: Snap time (e.g., 1100, 1600)
- `--tz`: Timezone (e.g., "UTC", "Asia/Singapore", "America/New_York")

### 2. Airflow DAG Structure

The process is orchestrated through multiple Airflow DAGs scheduled at different times and timezones:

**DAG Naming Convention**: `jg-bloombergsnap-eod-{TIME}-{TIMEZONE}.py`

Examples:
- `jg-bloombergsnap-eod-1100-hk.py` - 11:00 AM Hong Kong time
- `jg-bloombergsnap-eod-1600-utc.py` - 4:00 PM UTC
- `jg-bloombergsnap-eod-0700-utc.py` - 7:00 AM UTC

**DAG Components**:
1. **ETL Job**: Executes the Bloomberg Snap data retrieval
2. **Validation Job**: Calls data validation API to check for anomalies
3. **Optional DQ Check**: Data quality validation (currently commented out)

### 3. Configuration Management

**Configuration Tables**:
- `BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG` (View)
- `APACST_PROD_DB.APAC_MACRO.BLOOMBERG_SNAP_CONFIG` (Base table)
- `APACST_PROD_DB.APAC_MACRO1.BLOOMBERG_SNAP_CONFIG` (Base table)
- `DATA_PLATFORM_CORE.CONFIG.BLOOMBERG_SNAP_CONFIG` (Base table)

**Configuration Schema**:
```sql
IDENTIFIER VARCHAR(300)           -- Bloomberg ticker/identifier
FIELD VARCHAR(300)               -- Bloomberg field (e.g., PX_LAST, PX_VOLUME)
SNAP_TYPE VARCHAR(300)           -- BDH, BDP, BDP-DYNAMIC
SNAP_TIME VARCHAR(300)           -- Time slots (pipe-separated: "1100|1600")
IS_ACTIVE INTEGER                -- Active flag (1=active, 0=inactive)
TIMEZONE VARCHAR(20)             -- Timezone for the snap
IDENTIFIER_OVERRIDES VARCHAR(300) -- JSON overrides for field parameters
```

### 4. Data Processing Flow

#### Main Processing Function
**File**: `source/jgdata/datasets/bloomberg/snap/bloombergsnap.py`

**Function**: `build_bsnap_payload(st, tz)`

**Process Flow**:
1. **Configuration Retrieval**: Query Snowflake for active configurations matching snap time and timezone
2. **Data Type Separation**: Split requests into BDH and BDP types
3. **API Calls**: Execute Bloomberg API requests
4. **Data Processing**: Transform and validate responses
5. **Storage**: Insert processed data into Snowflake

#### BDH Processing (Historical Data)
**Function**: `build_309()`
**API Endpoint**: Bloomberg 309 History Request API

**Process**:
1. Groups tickers by field type
2. Creates HistoryRequest payload with date range (current date - 15 days to current date)
3. Calls `call_main_309_for_ref_date_api()`
4. Processes time-series data
5. Stores in `BLOOMBERG.SNAP.DATA_FEED` table

#### BDP Processing (Point-in-Time Data)
**Function**: `build_302_dynamic()`
**API Endpoint**: Bloomberg 302 Data Request API

**Process**:
1. Groups tickers by field type
2. Creates DataRequest payload with field overrides
3. Calls `call_302_api()`
4. Processes current point-in-time data
5. Stores in `BLOOMBERG.SNAP.DATA_FEED` table

### 5. Bloomberg API Integration

#### API 309 (Historical Data)
**File**: `source/jgdata/datasets/bloomberg/snap_api/python/bloomberg/persecurity/py/bbg_309_history_request1.py`

**Request Type**: HistoryRequest
**Purpose**: Retrieve historical time-series data
**Date Range**: Configurable (typically 15 days)

**Payload Structure**:
```json
{
  "@type": "HistoryRequest",
  "name": "Python309HistoryRequest{uuid}",
  "universe": {
    "@type": "Universe",
    "contains": [{"@type": "Identifier", "identifierType": "TICKER", "identifierValue": "ticker"}]
  },
  "fieldList": {
    "@type": "HistoryFieldList",
    "contains": [{"mnemonic": "field_name"}]
  },
  "runtimeOptions": {
    "@type": "HistoryRuntimeOptions",
    "dateRange": {
      "@type": "IntervalDateRange",
      "startDate": "start_date",
      "endDate": "end_date"
    }
  }
}
```

#### API 302 (Point Data)
**File**: `source/jgdata/datasets/bloomberg/snap_api/python/bloomberg/persecurity/py/bbg_302_nested_data_request.py`

**Request Type**: DataRequest
**Purpose**: Retrieve current point-in-time data with field overrides

**Payload Structure**:
```json
{
  "@type": "DataRequest",
  "name": "Python302NestedDataRequest{uuid}",
  "universe": {
    "@type": "Universe",
    "contains": [
      {
        "@type": "Identifier",
        "identifierType": "TICKER",
        "identifierValue": "ticker",
        "fieldOverrides": [
          {
            "@type": "FieldOverride",
            "mnemonic": "override_field",
            "override": "override_value"
          }
        ]
      }
    ]
  },
  "fieldList": {
    "@type": "DataFieldList",
    "contains": [
      {"mnemonic": "field_name"},
      {"mnemonic": "REFERENCE_DATE"}
    ]
  }
}
```

### 6. Data Storage

**Target Table**: `BLOOMBERG.SNAP.DATA_FEED`

**Schema**:
```sql
SNAP_DATE DATE                    -- Date of the snap
IDENTIFIER VARCHAR(16777216)      -- Bloomberg identifier
ATTRIBUTE VARCHAR(16777216)       -- Bloomberg field name
VALUE VARCHAR(16777216)           -- Retrieved value
AVAILDATE TIMESTAMP_NTZ(9)        -- Timestamp when data became available
REF_DATE TIMESTAMP_NTZ(9)         -- Reference date for the data
DL_REQUEST_ID VARCHAR             -- Request tracking ID
```

## Scheduling and Time Zones

### EOD Schedules
The process runs at multiple times throughout the day to capture different market closes:

**UTC Schedules**:
- 07:00 UTC - European morning
- 10:00 UTC - European close
- 13:00 UTC - Asian afternoon
- 16:00 UTC - US morning
- 18:00 UTC - US afternoon
- 19:00 UTC - US close

**Asia/Singapore Schedules**:
- 10:00 SGT - Asian morning
- 11:00 SGT - Asian midday
- 12:00 SGT - Asian afternoon
- 13:00 SGT - Asian close
- 16:00 SGT - European morning

**America/New_York Schedules**:
- 11:00 EST - US morning (intraday)

### Cron Configuration
Each DAG uses `CronTriggerTimetable` for precise scheduling:
```python
schedule = CronTriggerTimetable('0 11 * * *', timezone="Asia/Singapore")
```

## Error Handling and Monitoring

### Retry Logic
- **Retries**: 1 attempt with 1-minute delay
- **Timeout**: 30 minutes per task
- **Failure Handling**: Comprehensive logging with stdout/stderr capture

### Data Validation
- **HTTP Validation**: Calls validation API endpoint
- **Anomaly Detection**: `check_for_anomalies` function
- **Data Quality**: Optional DQ checks (currently disabled)

### Logging
- Detailed logging at each processing step
- API request/response logging
- Data transformation logging
- Snowflake insertion logging

## Configuration Examples

### Sample Configuration Entry
```sql
INSERT INTO BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG VALUES (
    'AAPL US Equity',           -- IDENTIFIER
    'PX_LAST',                  -- FIELD
    'BDP',                      -- SNAP_TYPE
    '1100|1600',               -- SNAP_TIME (multiple times)
    1,                          -- IS_ACTIVE
    'UTC',                      -- TIMEZONE
    '{"mnemonic":"PRICING_SOURCE","override":"BGN"}'  -- IDENTIFIER_OVERRIDES
);
```

### Field Override Example
```json
{
  "mnemonic": "PRICING_SOURCE",
  "override": "BGN"
}
```

## Adhoc Processing

**File**: `jg-bloombergsnap-adhoc.py`

Supports on-demand processing with custom configurations:
```python
snap_configs = [
    "IDENTIFIER=ABC, SNAP_TYPE=BDH, ATTRIBUTE=PX_LAST",
    "IDENTIFIER=XYZ, SNAP_TYPE=BDP, ATTRIBUTE=PX_LAST"
]
```

## Dependencies

### External Dependencies
- Bloomberg Terminal/API access
- Bloomberg user terminal number configuration
- Network connectivity to Bloomberg Snap API endpoints

### Internal Dependencies
- Snowflake database connectivity
- Airflow orchestration platform
- Python environment with required packages
- JG Data platform infrastructure

## Troubleshooting

### Common Issues
1. **API Timeout**: Increase timeout settings or reduce batch size
2. **Configuration Errors**: Verify BLOOMBERG_SNAP_CONFIG entries
3. **Network Issues**: Check Bloomberg API connectivity
4. **Data Quality**: Review validation rules and thresholds

### Monitoring Points
- DAG execution status in Airflow
- Data validation API responses
- Snowflake table row counts
- Bloomberg API response times

## Future Enhancements

1. **Real-time Processing**: Implement streaming data ingestion
2. **Enhanced Error Recovery**: Automatic retry with exponential backoff
3. **Performance Optimization**: Parallel processing for large datasets
4. **Extended Coverage**: Additional Bloomberg fields and markets
