import os
import gzip
import shutil
import tempfile
import pandas as pd

from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from utils.postgres.adaptor import PostgresAdaptor
from utils.date_utils import get_n_bday_yyyymmdd, get_n_bday, get_now
from bloomberg.bpipe import BpipeHandler
from utils.email_utils import send_email

if __name__ == "__main__":
    pg_adaptor = PostgresAdaptor(
        # host="tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com",
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df_etfs = pg_adaptor.execute_query(
        """select distinct bbg_full_ticker from eqvol.sr_security_ref""",
    )
    securities = df_etfs["bbg_full_ticker"].tolist()
    
    if "VXX US Equity" not in securities:
        securities.append("VXX US Equity")
    
    if "VIX Index" not in securities:
        securities.append("VIX Index")
    
    to_date = get_n_bday(0)
    from_date = get_n_bday(1)


    bpipe_instance = ("Tech1ProdBPipe47537.jainglobal.net", 8194)
    bpipe_app = "JAIN:pmdashboard-bps"
    bpipe_handler = BpipeHandler(bpipe_instance, bpipe_app)

    df_etf_closing_px = bpipe_handler.fetch_hist(p_ticker=securities, p_field="PX_LAST", p_start=from_date, p_end=to_date)
    df_etf_closing_px.rename(columns= {"ticker": "BBG_TICKER", "field": "FIELD", "date": "DATE", "value": "VALUE"}, inplace=True)
    df_etf_closing_px["DATE"] = pd.to_datetime(df_etf_closing_px["DATE"], format="%m/%d/%Y", errors="coerce")
    df_etf_closing_px["VALUE"] = pd.to_numeric(df_etf_closing_px["VALUE"], errors="coerce")
    df_etf_closing_px["WHEN_UPDATED"] = get_now()

    # print(df_etf_closing_px.head())

    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_ONDEMAND",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
    )

    ret_val = adaptor.upsert(df_etf_closing_px, "BBG_EQVOL_HIST_DATA", ["BBG_TICKER", "FIELD", "DATE"])
    print(ret_val)