from confluent_kafka import Producer, KafkaException
import base64
import json
import os

conf = {
    'bootstrap.servers': 'kafka1.jainglobal.net:9092,kafka2.jainglobal.net:9092',
}

producer = Producer(conf)

def delivery_report(err, msg):    
    if err is not None:
        print(f'Message delivery failed: {err}')
    else:
        print(f'Message delivered to {msg.topic()} [{msg.partition()}] at offset {msg.offset()}')

topic = 'fo_tools_alerts'

email_param = {
    "to": ["<EMAIL>", "<EMAIL>"],
    "cc": [],
    "subject": "Email Test Subject",
    "from_": "<EMAIL>",
    "message": "Test message",
    "body": "Test body. Please ignore. <b>Test body in bold</b>",
    "default_footer": False,
}

with open("cron.txt", "rb") as file:
    encoded_attachment = base64.b64encode(file.read()).decode("utf-8")
email_param["attachment"] = os.path.basename("cron.txt")
email_param["attachment_content"] = encoded_attachment

record_key = 'some_key'
record_value = email_param
producer.produce(topic, key=record_key, value=json.dumps(email_param), callback=delivery_report)
producer.flush()
