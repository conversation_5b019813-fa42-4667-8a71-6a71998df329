"use strict";
class Logger {
    static get logContainer() {
        return document.getElementById("logContainer");
    }
    static get statusBar() {
        return document.getElementById("statusBar");
    }
    static log(message) {
        this.addToLog("log", message);
    }
    static error(message) {
        this.addToLog("error", message);
        this.showStatus(message, "error", Config.get("errorStatusMessageDuration")); // Show error messages for 20 seconds
    }
    static info(message) {
        this.addToLog("info", message);
    }
    static warn(message) {
        this.addToLog("warn", message);
    }
    static addToLog(type, message) {
        let formattedMessage = typeof message === "object" ? JSON.stringify(message, null, 2) : message;
        // Attempt to parse and re-stringify if message is a string that looks like JSON
        if (typeof message === "string" && ((message.startsWith("{") && message.endsWith("}")) ||
            (message.startsWith("[") && message.endsWith("]")))) {
            try {
                const json = JSON.parse(message);
                formattedMessage = JSON.stringify(json, null, 2);
            }
            catch (e) {
                // Keep original message if parsing fails
            }
        }
        const logEntry = `[${new Date().toISOString()}] [${type.toUpperCase()}]: ${formattedMessage}`;
        const container = this.logContainer;
        if (container) {
            const messageElement = document.createElement("div");
            messageElement.className = `log-message ${type}`;
            messageElement.textContent = logEntry;
            container.appendChild(messageElement);
        }
        else {
            console.log(logEntry);
        }
    }
    static showStatusObject(message, jsonObj, type, timeout) {
        let formattedJsonObj;
        try {
            formattedJsonObj = JSON.stringify(jsonObj, null, 2);
        }
        catch (e) {
            if (e instanceof Error) {
                formattedJsonObj = e.message; // If you're sure it's an Error object
            }
            else {
                formattedJsonObj = String(e); // Otherwise, safely convert to string
            }
        }
        const logEntry = `${message} ${formattedJsonObj}`;
        this.log(logEntry);
        this.showStatus(logEntry, type, timeout);
    }
    static showStatus(message, type, timeout = 10000) {
        const statusBar = this.statusBar;
        if (statusBar) {
            statusBar.textContent = message;
            statusBar.className = 'status-bar ' + (type === "error" ? 'error' : 'normal');
            statusBar.style.display = "block";
            setTimeout(() => {
                statusBar.style.display = "none";
            }, timeout);
        }
        else {
            console.log(`[${type.toUpperCase()}]: ${message}`);
        }
    }
}
Logger.logs = [];
