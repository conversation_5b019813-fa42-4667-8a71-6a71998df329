from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from bloomberg.utils import get_bbg_full_ticker_for_futures

from bloomberg.per_security.parser import BloombergParser

from utils.date_utils import get_now

import pandas as pd
import numpy as np

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    df_securities = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """select ID_BB_GLOBAL
            FROM BLOOMBERG.BBGH_FUTURES.VW_FUTURE_REF 
            WHERE last_tradeable_dt >= CURRENT_DATE and FUTURE_ROOT IN ('TY', 'FV', 'TU');
        """,
    )

    securities = df_securities["ID_BB_GLOBAL"].tolist()

    per_sec_req_type = PerSecurityRequestType.getdata
    batch_name = "dp_futoad"
    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "sec_id": "BB_GLOBAL",
        "fields": [
            "DUR_MID"
        ],
        "securities": securities,
    }
    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_oad/"
    fut_oad_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)
    print(fut_oad_file_path)

    # fut_ref_file_path = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_ref/responses/dp_futref_2502050635.out"

    parser = BloombergParser(fut_oad_file_path, sep='|', skipinitialspace=True, on_bad_lines='error') 
    df_futures = parser.parse_data()

    df_futures["UPDATE_SOURCE"] = 3
    df_futures["LAST_UPDATED"] = get_now()
    # df_futures["UPDATED_BY"] = "bbg_dl_futures_ref.py"

    print(df_futures)

    # adaptor = SnowparkAdaptor(
    #     database="BLOOMBERG", 
    #     schema="BBGH_FUTURES",
    #     warehouse="BLOOMBERG_HUB_WH", 
    #     role="DR_BBGH_OWNER"
    # )

    # ret_val = adaptor.upsert(df_futures, "FUTURE_REF_DL", ["ID_BB_GLOBAL"])
    # print(ret_val)
