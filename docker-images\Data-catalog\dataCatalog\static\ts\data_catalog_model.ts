class DataCatalogModel extends ModelBase {

    private urlParams: URLSearchParams;
    public baseUrl: string = "";
    public FeedName: string = "";
    public FeedDescription: string = "";
    public VendorContactDetails: string = "";
    public Credentials: string = "";
    public JGDataOwner: string = "";
    public GrafanaAlerts: string = "";
    public HistoricalFeeds: string = "";
    public DailyFeeds: string = "";
    public Historical_FilePatternsExpected: string = "";
    public Daily_FilePatternsExpected: string = "";
    public TablesLoaded: string = "";
    public SLA: string = "";
    public AirflowDagName: string = "";
    public Estimated_Files: string = "";
    public SlackChannelName: string = "";
    public TeamsChannelName: string = "";

    constructor() {
        super();

        // this.defineProperties();
        // this.setDefaultValues();

        // Initialize properties with default values or from URL parameters so assignments trigger HTML updates
        this.baseUrl = `${window.location.protocol}//${window.location.host}`;
        this.urlParams = new URLSearchParams(window.location.search);

        // Ensure the properties are strings, provide a default value if null
        // this.dataSetId = this.urlParams.get("Dataset_Id") || "defaultDataSetId";

        // Logger.info(`DataSet ID: ${this.dataSetId}`);

        // if (!this.dataSetId) {
        //     Logger.error("DataSet ID not found in URL");
        // } else {
        //     Logger.info(`DataSet ID: ${this.dataSetId}`);
        // }
    }

    private setDefaultValues(): void {
        this.FeedName = "Please select a data set to view the FeedName.";
        this.FeedDescription = "Please select a data set to view the FeedDescription.";
        this.VendorContactDetails = "Please select a data set to view the Vendor Contact Details.";
        this.SourcingURL = "Please select a data set to view the Sourcing URL.";
        this.Credentials = "Please select a data set to view the Credentials.";
        this.JGDataOwner = "Please select a data set to view the JG Data Owner.";
        this.GrafanaAlerts = "Please select a data set to view the Grafana Alerts.";
        this.HistoricalFeeds = "Please select a data set to view the Historical Feeds.";
        this.DailyFeeds = "Please select a data set to view the Daily Feeds.";
        this.Historical_FilePatternsExpected = "Please select a data set to view the FilePatternsExpected(Historical).";
        this.Daily_FilePatternsExpected = "Please select a data set to view the FilePatternsExpected(Daily).";
        this.TablesLoaded = "Please select a data set to view the Tables Loaded.";
        this.SLA = "Please select a data set to view the SLA.";
        this.AirflowDagName = "Please select a data set to view the AirflowDagName.";
        this.Estimated_Files = "Please select a data set to view the Estimated Files.";
        this.SlackChannelName = "Please select a data set to view the Slack Channel Name.";
        this.TeamsChannelName = "Please select a data set to view the Teams Channel Name.";
    }

    private defineProperties(): void {
        // this.defineProperty("DataSet", "DataSet", true);
        this.defineProperty("Dataset_Id", "Dataset_Id", true);
        this.defineProperty("FeedName", "FeedName", true);
        this.defineProperty("FeedDescription", "FeedDescription", true);
        this.defineProperty("VendorContactDetails", "VendorContactDetails", true);
        this.defineProperty("SourcingStrategy", "SourcingStrategy", true);
        this.defineProperty("SourcingURL", "SourcingURL", true);
        this.defineProperty("Credentials", "Credentials", true);
        this.defineProperty("JGDataOwner", "JGDataOwner", true);
        this.defineProperty("GrafanaAlerts", "GrafanaAlerts", true);
        this.defineProperty("HistoricalFeeds", "HistoricalFeeds", true);
        this.defineProperty("DailyFeeds", "DailyFeeds", true);
        this.defineProperty("FilePatternsExpected_Historical", "FilePatternsExpected_Historical", true);
        this.defineProperty("FilePatternsExpected_Daily", "FilePatternsExpected_Daily", true);
        this.defineProperty("TablesLoaded", "TablesLoaded", true);
        this.defineProperty("SLA", "SLA", true);
        this.defineProperty("AirflowDagName", "AirflowDagName", true);
        this.defineProperty("Estimated#Files", "Estimated#Files", true);
        this.defineProperty("SlackChannelName", "SlackChannelName", true);
        this.defineProperty("Teams_Channel_Name", "Teams_Channel_Name", true);
    }

    // // Attach event listeners to DOM elements
    // public attachEventListener(elementId: string, event: string, handler: EventListener): void {
    //     const element = document.getElementById(elementId);
    //     if (element) {
    //         element.addEventListener(event, handler);
    //     }
    // }
}

interface DataCatalog {
    'id': number,
    'dataset_id': string,
    'dataset_name': string,
    'vendor': string,
    'dataset_description': string,
    'dataset_details': string,
    'status': string,
    'permission_group': string,
    'data_management_lead': string,
    'dm_lead_email': string,
    'dm_lead_phone': string,
    'dm_lead_mobile': string,
    'vendor_contact_other': string,
    'vendor_contact_title': string,
    'vendor_contact_work_phone': string,
    'vendor_contact_mobile': string,
    'vendor_contact_email': string,
    'raw_data_location': string,
    'process_data_location': string,
    'file_type': string,
    'update_frequency': string,
    'technical_notes': string,
    'github_repository': string,
    'support_document_link': string,
    'file_names': string,
    'vendor_feed_sla_est': string,
    'file_count': string,
    'vendor_feed_extraction_source': string,
    'vendor_feed_extraction_source_url': string,
    'credentials': string,
    'grafana_alerts': string,
    'dataset_billing_name': string,
    'tables_loaded': string,
    'airflow_dag_names': string,
    'slack_channel_name': string,
    'teams_channel_name': string,
    'vendor_account_manager': string,
    'vendor_account_manager_work_phone': string,
    'vendor_account_manager_mobile': string,
    'vendor_account_manager_email': string,
    'vendor_sales_specialist': string,
    'vendor_sales_specialist_work_phone': string,
    'vendor_sales_specialist_mobile': string,
    'vendor_sales_specialist_email': string,
    'vendor_technical_account_manager': string,
    'vendor_technical_account_manager_work_phone': string,
    'vendor_technical_account_manager_mobile': string,
    'vendor_technical_account_manager_email': string,
    'customer_success_manager_product': string,
    'customer_success_manager_product_work_phone': string,
    'customer_success_manager_product_mobile': string,
    'customer_success_manager_product_email': string,
    'product_owner': string,
    'jfs_rag_status'?:string,
    'etl_rag_status'?: string,
    'whdv_rag_status'?: string,
    'users': string,
    'vendor_ticketing_portal': string,
    'vendor_customer_support_dl': string,
    'vendor_hotline_number': string,
    'sftp_details': string,
    'historical_data_start_date': Date    
}
