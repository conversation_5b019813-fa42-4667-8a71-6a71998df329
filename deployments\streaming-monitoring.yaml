apiVersion: apps/v1
kind: Deployment
metadata:
  name: streaming-monitoring
  namespace: default
  labels:
    app: streaming-monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: streaming-monitoring
  template:
    metadata:
      labels:
        app: streaming-monitoring
    spec:
      containers:
      - name: streaming-monitoring
        image: ${ACCOUNT_ID}.dkr.ecr.us-east-1.amazonaws.com/${PREFIX}-streaming-job-monitor-${ENV}:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 9100  # Node Exporter port
          name: http-metrics
          protocol: TCP
        - containerPort: 22     # SSH port
          name: ssh
          protocol: TCP
        securityContext:
          capabilities:
            add: ["SYS_ADMIN"]  
          privileged: true      
          runAsUser: 0          
        volumeMounts:
        - mountPath: /tmp
          name: tmp
        - name: private-key-volume
          mountPath: /tmp/private-key
          readOnly: true
        - name: node-exporter-metrics
          mountPath: /etc/node_exporter
        command:
          - /bin/bash
          - -c
          - |
            echo "Starting Streaming Monitoring Deployment..."
            # Copy private key to a writable location
            cp /tmp/private-key/private-key.pem /tmp/private-key.pem && 
            chmod 400 /tmp/private-key.pem

            # Ensure SSH directory exists
            mkdir -p ~/.ssh

            # Add server to known hosts
            ssh-keyscan -H ************* >> ~/.ssh/known_hosts
            chmod 600 ~/.ssh/known_hosts

            # Clean node-exporter metric directory before start
            rm -rf /etc/node_exporter/*

            # Start dependencies in the background
            /etc/cwiqfs/run-pod-with-mount.sh &
            /usr/local/bin/node_exporter --collector.textfile.directory=/etc/node_exporter/ & 
            /usr/local/bin/monitor_process.sh & 

            # Keep container running
            exec tail -f /dev/null
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 4800
      volumes:
      - name: tmp
        emptyDir: {}
      - name: private-key-volume
        secret:
          secretName: data-acc-priv-key-secret
          items:
            - key: private-key
              path: private-key.pem
      - name: node-exporter-metrics
        emptyDir: {}


---

apiVersion: v1
kind: Service
metadata:
  name: node-exporter-service
spec:
  selector:
    app: streaming-monitoring
  ports:
    - protocol: TCP
      port: 9100          # External port
      targetPort: 9100     # Internal port
  type: LoadBalancer      # Expose as LoadBalancer