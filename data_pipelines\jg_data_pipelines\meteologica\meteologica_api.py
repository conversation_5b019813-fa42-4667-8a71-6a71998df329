import os
import time
import jwt
import requests

from meteologica_config import *

class MeteologicaAPIHandler:
    """<PERSON><PERSON> for interacting with the Meteologica API.
    
    This class manages API authentication, requests, and data retrieval from the Meteologica service.
    """
    MAX_HISTORICAL_DATA_REQUESTS_PER_MINUTE = 12000

    def __init__(self):
        """Initialize the MeteologicaAPIHandler with credentials and default values."""
        logger.info("Initializing MeteologicaAPIHandler")
        self.user = self._get_user_pwd("METEOLOGICA_USERNAME")
        self.password = self._get_user_pwd("METEOLOGICA_PASSWORD")
        self.token = None
        self.historical_data_requests_made = 0
        
    def make_get_request(self, endpoint: str, query_params: dict) -> requests.Response:
        """Make a GET request to the Meteologica API.
        
        Args:
            endpoint (str): The API endpoint to call.
            query_params (dict): Query parameters to include in the request.
            
        Returns:
            requests.Response: The response from the API.
            
        Raises:
            requests.exceptions.RequestException: If the request fails.
        """
        url = f"{self._get_base_url()}/{endpoint}"
        #query_params["token"] = self._get_token()
        logger.debug(f"Making GET request to {url}")
        response = requests.get(url, params=query_params)
        response.raise_for_status()
        logger.debug(f"GET request successful with status code {response.status_code}")
        return response
 
    def make_post_request(self, endpoint: str, json_body: dict, use_token: bool = True) -> requests.Response:
        """Make a POST request to the Meteologica API.
        
        Args:
            endpoint (str): The API endpoint to call.
            json_body (dict): The JSON payload to send.
            use_token (bool, optional): Whether to include the authentication token. Defaults to True.
            
        Returns:
            requests.Response: The response from the API.
            
        Raises:
            requests.exceptions.RequestException: If the request fails.
        """
        url = f"{self._get_base_url()}/{endpoint}"
        
        if use_token:
            json_body["token"] = self._get_token()
        
        logger.debug(f"Making POST request to {url}")
        response = requests.post(url, json=json_body)
        response.raise_for_status()
        logger.debug(f"POST request successful with status code {response.status_code}")
        return response
    
    def _get_user_pwd(self, env_var: str) -> str:
        """Get user credentials from environment variables.
        
        Args:
            env_var (str): Name of the environment variable.
            
        Returns:
            str: The value of the environment variable.
            
        Raises:
            ValueError: If the environment variable is not found.
        """
        value = os.getenv(env_var)
        if not value:
            logger.error(f"Environment variable {env_var} not found")
            raise ValueError(f"Environment variable {env_var} not found")
        logger.debug(f"Successfully retrieved {env_var} from environment")
        return value
    
    def get_lastet_contents(self, latest_seconds: int) -> list:
        """Get contents updated within the specified time window.
        
        Args:
            latest_seconds (int): Number of seconds to look back for updates.
            
        Returns:
            list: List of updated content items.
        """
        logger.info(f"Fetching latest contents updated in the last {latest_seconds} seconds")
        response = self.make_get_request("latest", {"seconds": latest_seconds, "token": self._get_token()})
        if response.status_code != 200:
            logger.error(f"Error {response.status_code} response: {response.json()}")
            return
        latest_updates = response.json()["latest_updates"]
        logger.info(f"Successfully retrieved {len(latest_updates)} updates")
        return latest_updates
    
    def _get_new_token(self) -> str:
        """Get a new authentication token from the API.
        
        Returns:
            str: The new authentication token.
            
        Raises:
            RuntimeError: If token retrieval fails.
        """
        response = self.make_post_request("login", {"user": self.user, "password": self.password},use_token=False)
        try:
            return response.json()["token"]
        except (KeyError, RuntimeError) as e:
            raise RuntimeError(
                f"Could not get the token from the response: {response.text} ({response.status_code})"
            ) from e
 
    def _refresh_token(self) -> str:
        """Refresh the existing authentication token.
        
        Returns:
            str: The refreshed authentication token.
            
        Raises:
            RuntimeError: If token refresh fails.
        """
        response = self.make_get_request("keepalive", {"token": self.token})
        try:
            return response.json()["token"]
        except (KeyError, RuntimeError) as e:
            raise RuntimeError(
                f"Could not get the token from the response: {response.text} ({response.status_code})"
            ) from e
 
    def _get_token(self) -> str:
        """Get or refresh the authentication token as needed.
        
        This method handles token lifecycle management, including:
        - Getting a new token if none exists
        - Refreshing the token if it's close to expiring
        - Returning the existing valid token
        
        Returns:
            str: A valid authentication token.
        """
        self.token
 
        if not  self.token or (
             self.token is not None
            and time.time() > jwt.decode( self.token, options={"verify_signature": False})["exp"]
        ):
            logger.info("Getting new API token")

            new_token = self._get_new_token()
            self.token = new_token
            return self.token
 
        exp = jwt.decode(self.token, options={"verify_signature": False})["exp"]
        now = time.time()
 
        if exp - now < 300 and exp - now > 0:
            logger.info("Refreshing API token")
            new_token = self._refresh_token()
            self.token = new_token
            return self.token
        else:
            return self.token
    
    def get_or_refresh_stored_token(self):
        """Function to get a new token if the existing one is invalid or refresh it if it is close to expiring"""
        token = self.token

        if not token or (
            token is not None
            and time.time() > jwt.decode(token, options={"verify_signature": False})["exp"]
        ):
            logger.info("getting new token")

            if self.user is None or self.password is None:
                raise RuntimeError(
                    "User and password must be specified. Check your '.env' file."
                )

            new_token = self._get_new_token()
            self.token = new_token
            return self.token

        exp = jwt.decode(token, options={"verify_signature": False})["exp"]
        now = time.time()

        if exp - now < 300 and exp - now > 0:
            logger.info("refreshing token")
            new_token = self._refresh_token()
            self.token = new_token
            return self.token
        else:
            return token
 
    def get_historical_content_data(self, content_id: int, year: int, month: int) -> bytes:
        """Retrieve historical data for a specific content and time period.
        
        Args:
            content_id (int): The ID of the content to retrieve.
            year (int): The year of the historical data.
            month (int): The month of the historical data.
            
        Returns:
            bytes: The historical data content (typically a ZIP file).
            
        Raises:
            ValueError: If required parameters are missing.
            Exception: If historical data request limit is reached.
        """
        logger.info(f"Fetching historical data for content ID {content_id}, year {year}, month {month}")
        self.get_or_refresh_stored_token()
 
        if self.historical_data_requests_made >= self.MAX_HISTORICAL_DATA_REQUESTS_PER_MINUTE:
            logger.warning("Historical data request limit reached")
            raise Exception("Historical data request limit reached, try again later.")
 
        if not content_id or not year or not month:
            logger.error("Missing required parameters for historical data request")
            raise ValueError("Content ID, year, and month are required.")
 
        endpoint = f"contents/{content_id}/historical_data/{year}/{month}"
        params = {"token": self.token}
        url = f"{self._get_base_url()}/{endpoint}"
 
        logger.debug(f"Making request to {url}")
        response = requests.get(url, params=params)
        response.raise_for_status()
        self.historical_data_requests_made += 1
        logger.info(f"Historical data fetched successfully for content ID: {content_id}, year: {year}, month: {month}")
        return response.content  # For historical data, this is typically a ZIP file.

    @staticmethod
    def _get_base_url() -> str:
        """Get the base URL for the Meteologica API.
        
        Returns:
            str: The base URL of the API.
        """
        return "https://api-markets.meteologica.com/api/v1"
    
    @staticmethod
    def _get_max_historical_data_requests_per_minute() -> int:
        """Get the maximum number of historical data requests allowed per minute.
        
        Returns:
            int: The maximum number of requests allowed.
        """
        return 12000