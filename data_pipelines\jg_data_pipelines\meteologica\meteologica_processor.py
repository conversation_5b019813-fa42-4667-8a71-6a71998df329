#!/usr/bin/env python3 
#-*- coding: utf-8 -*- 


import os
import sys
import json
import shutil
import argparse
import uuid
import tempfile
import time
import requests
from zipfile import ZipFile
from pathlib import Path
import polars as pl

import meteologica_api
from meteologica_config import * 

# Add the parent directory to the sys path to be able to import the utils and run from project root path aka python jg_data_pipelines/meteologica/meteologica_processor.py
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.date_utils import get_now
from utils.snowflake.bulk_loader import SnowflakeBulkLoader
from utils.snowflake.adaptor import SnowflakeAdaptor

HISTORICAL = "HISTORICAL"
INCREMENTAL = "INCREMENTAL"
HISTORICAL_GAP_FILL = "HISTORICAL_GAP_FILL"


class MeteologicaProcessor:
    """Process and manage Meteologica API data.
    
    This class handles fetching, processing, and loading Meteologica data into Snowflake.
    It manages different types of data including forecasts, observations, and reanalysis data.
    """

    def __init__(self, process_mode):
        """Initialize the MeteologicaProcessor with required handlers and data structures."""
        self.process_mode = process_mode
        self.schema = os.getenv('METEOLOGICA_HISTORIC_SCHEMA') if process_mode == HISTORICAL else os.getenv('METEOLOGICA_ONGOING_SCHEMA')
        self.metodogia_api_handler = meteologica_api.MeteologicaAPIHandler()
        self.now = get_now(tz_str='UTC').strftime("%Y%m%d%H%M%S")
        self.params_df = self._get_sf_params_df()
        self.json_tables = None
        self.parquet_tables = None
        self.id_tracker = None
        self.reset_trackers()
        self.sf_adaptor = self._get_sf_adaptor()
        

    def reset_trackers(self):
        self.json_tables = {
                            'NORMAL':[],
                            'OBSERVATION':[],
                            'REANALYSIS':[], 
                            'FORECAST_DETERMINISTIC':[],
                            'FORECAST_ENSEMBLE':[],
                            'FORECAST_EXTENDED':[]
        }
        self.parquet_tables = {
                            'NORMAL':[],
                            'OBSERVATION':[],
                            'REANALYSIS':[], 
                            'FORECAST_DETERMINISTIC':[],
                            'FORECAST_ENSEMBLE':[],
                            'FORECAST_EXTENDED':[]
        }
        self.id_tracker = {
                            'NORMAL':[],
                            'OBSERVATION':[],
                            'REANALYSIS':[], 
                            'FORECAST_DETERMINISTIC':[],
                            'FORECAST_ENSEMBLE':[],
                            'FORECAST_EXTENDED':[]
        }

    def _lastet_contents_from_api(self, lookback_seconds):
        """Fetch the latest contents from Meteologica API.
        
        Returns:
            list: Latest content updates from the API.
        """
        return self.metodogia_api_handler.get_lastet_contents(lookback_seconds)
    
    def _lastet_json_content_from_api(self, id, update_id):
        """Fetch specific content data from the API.
        
        Args:
            id (int): Content ID to fetch.
            update_id (str): Update ID for the content.
            
        Returns:
            dict: JSON response containing the content data.
        """
        # logger.info(f"Received latest update for content ID {id} with update ID {update_id}")
        # try:
        #     content_data = self.metodogia_api_handler.make_get_request(
        #         f"contents/{id}/data",
        #         {"update_id": update_id, "token": self.metodogia_api_handler._get_token()},
        #     )
        # except Exception as e:
        #     logger.error(f'FAILURE-METEOLOGICA_REQUEST-CONTENT_ID:{id}-UPDATE_ID:{update_id}')
        #     logger.exception(e)
        # return content_data.json()
        logger.info(f"Fetching latest update for content ID {id} with update ID {update_id}")
        content_data = self.metodogia_api_handler.make_get_request(
            f"contents/{id}/data",
            {"update_id": update_id, "token": self.metodogia_api_handler._get_token()},
        )
        return content_data.json()

    
    def _save_json_to_file(self, raw_api_response, path):
        """Save API response data to JSON files.
        
        Args:
            raw_api_response (list): List of content updates from the API.
            path (str): Directory path where files should be saved.
            
        Returns:
            str: Path where JSON files were saved.
        """
        raw_json_path = f"{BASE_DATA_DIR}/{path}"
        if os.path.exists(raw_json_path):
            shutil.rmtree(raw_json_path)
                 
        os.makedirs(raw_json_path)

        for update in raw_api_response:
            id = update["content_id"]
            update_id = update["update_id"]
            data_table = self._get_data_table(id)
            if not data_table:
                continue

            self.id_tracker[data_table].append((id, update_id))
            
            try:
                json_data = self._lastet_json_content_from_api(id,update_id)
            except Exception as e:
                logger.warning(f'FAILURE-METEOLOGICA_REQUEST-CONTENT_ID:{id}-UPDATE_ID:{update_id}')
                logger.warning(e)
                continue

            try:
                with open(f'{raw_json_path}/{id}_{update_id}.json', 'w') as json_file:
                    logger.info(f"Writing content data to file {id}_{update_id}.json")
                    json.dump(json_data, json_file)
            except Exception as e:
                logger.error(f'FAILURE-METEOLOGICA_JSON_FILE_SAVE-CONTENT_ID:{id}-UPDATE_ID:{update_id}')
                logger.exception(e)

        return raw_json_path
    

    def _get_sf_params_df(self):
        """Fetch Meteologica parameters from Snowflake.
        
        Returns:
            DataFrame: Parameters reference data from Snowflake.
        """
        snowflake_adaptor = SnowflakeAdaptor(
                                             database= os.getenv('METEOLOGICA_DATABASE'), 
                                             warehouse= os.getenv('SF_WAREHOUSE'), 
                                             role= os.getenv('SF_ROLE')
                                             )
    
        df = snowflake_adaptor.read_data(
            schema=os.getenv('METEOLOGICA_HISTORIC_SCHEMA'), 
            query="SELECT * FROM METEOLOGICA.API.METEO_PARAMS_REF")
        snowflake_adaptor.close()
        return df

    def _get_data_table(self, content_id):
        """Determine the appropriate table name for a given content ID.
        
        Args:
            content_id (int): Content ID to classify.
            
        Returns:
            str: Table name corresponding to the content type.
        """
        params_df = self.params_df.query(f"CONTENT_ID == {content_id}")
        if len(params_df['DATA_TYPE'].values) == 0:
            logger.error(f'FAILURE-METEOLOGICA_PARAMS_MISSMATCH-CONTENT_ID:{content_id}')
            return None
        is_forecast = params_df['DATA_TYPE'].values[0] == 'Forecast'
        if is_forecast:
            forecast_type = params_df['FORECAST_TYPE'].values[0]
            forecast_source = params_df['FORECAST_SOURCE'].values[0]
            if forecast_type == 'Ensemble':
                if forecast_source != 'ECMWF-ENSEXT':
                    data_table = 'FORECAST_ENSEMBLE'
                else:
                    data_table = 'FORECAST_EXTENDED'
            else:
                data_table = 'FORECAST_DETERMINISTIC'
        else:
            data_type = params_df['DATA_TYPE'].values[0]
            if data_type == 'Normal':
                data_table = 'NORMAL'
            elif data_type == 'Observation':
                data_table = 'OBSERVATION'
            elif data_type == 'Reanalysis':
                data_table = 'REANALYSIS'
        return data_table  


    def _examine_json(self, file_path, data_table):
        """Validate JSON file structure and content.
        
        Args:
            file_path (str): Path to the JSON file.
            data_table (str): Type of data table being examined.
            
        Returns:
            tuple: Contains:
                - list: Missing required keys
                - list: Extra unexpected keys
                - list: Keys with mismatched data types
                - bool: True if no data present
        """
        with open(file_path, 'r') as f:
            data = json.load(f)
        if not data['data']:
            logger.info(f"No data for {file_path}")
            return [], [], [], True
    
        json_keys = data.keys()

        expected_elements = {
            "content_id": int,
            "content_name": str,
            "data": list,
            # "installed_capacity": str,
            "issue_date": str,
            "timezone": str,
            "unit": str,
            "update_id": str,
        }  
        missing_keys = set(expected_elements.keys()) - set(json_keys)
        extra_keys = set(json_keys) - set(expected_elements.keys())

        mismatched_data_types = []
        logger.debug(f"Examining {data_table} format")
        for key, expected_type in expected_elements.items():
            if key in json_keys:
                if type(data[key]) != expected_type:
                    mismatched_data_types.append(key)
                if key == "data":
                    data_val = data[key]
                    for i, data_point in enumerate(data_val):
                        if type(data_point) != dict:
                            mismatched_data_types.append(f"data[{i}]")
        
        return list(missing_keys), list(extra_keys), mismatched_data_types, False


    def _generate_parquet_files(self, raw_json_path):
        """Generate Parquet files from JSON data.

        Args:
            raw_json_path (str): Path to the directory containing raw JSON files.
        """

        for json_path in Path(raw_json_path).rglob('*.json'):
            content_id = json_path.stem.split('_')[0]
            data_table = self._get_data_table(content_id)
            if not data_table:
                continue
            missing_keys, extra_keys, mismatched_data_types, no_data = self._examine_json(json_path, data_table)
            if no_data:
                continue

            if missing_keys or extra_keys or mismatched_data_types:
                logger.debug(f"Unexpected file format for {data_table}: {json_path} - Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_data_types}")

            self.json_tables[data_table].append(json_path)


        for data_table, json_paths in self.json_tables.items():
            if not json_paths:
                logger.info(f"No files for {data_table}")
                continue
            try:
                df = pl.scan_ndjson(json_paths, include_file_paths='source_file')
                if self.process_mode == INCREMENTAL:
                    df = df.with_columns(
                        source_file = pl.col('source_file').str.split('/').list.last(),
                        issue_date = pl.col('issue_date').str.to_datetime("%Y-%m-%d %H:%M:%S UTC").dt.replace_time_zone("UTC")
                    )
                else:
                    zipfile = f"{raw_json_path.split('/')[-1]}.zip"
                    df = df.with_columns(
                        source_file = pl.concat_str(
                            pl.lit(f"{zipfile}/"),
                            pl.col('source_file').str.split('/').list.last()
                        ),
                        issue_date = pl.col('issue_date').str.to_datetime("%Y-%m-%d %H:%M:%S UTC").dt.replace_time_zone("UTC")
                    )
                df = df.with_columns(
                    issue_date = pl.col('issue_date').dt.cast_time_unit('ms')
                )
                # Track combinations of content_id, update_id for eliminating overlaps
                if self.process_mode != HISTORICAL:
                    unique = df.select('content_id','update_id').unique()
                    for row in unique.collect().rows(named=True):
                        self.id_tracker[data_table].append((row['content_id'], row['update_id']))
                df = df.explode(pl.col("data")).collect()
                zipfile_id = raw_json_path.split('/')[-1]
                parquet_file = f"{BASE_DATA_DIR}/latest/processed/{self.now}_{zipfile_id}_{data_table}.parquet"
                df.write_parquet(parquet_file)
                self.parquet_tables[data_table].append(parquet_file)
                logger.info(f"Parquet file generated: {parquet_file}")
            except Exception as e:
                logger.error(f'FAILURE-METEOLOGICA_PARQUET_FILE_SAVE-DATA_TABLE:{data_table}')
                logger.exception(e)

    def _drop_overlap(self, sf_table_name):
        where_clauses = [f"(content_id = '{x[0]}' AND update_id = '{x[1]}')" for x in self.id_tracker[sf_table_name]]
        where_statetment = " OR ".join(where_clauses)
        delete_statement = f"DELETE FROM {sf_table_name} WHERE {where_statetment}"
        try:
            self.sf_adaptor.execute_query(delete_statement)
        except Exception as e:
            logger.error(f'FAILURE-METEOLOGICA_SNOWFLAKE_CLEANUP-DATA_TABLE:{sf_table_name}')
            logger.exception(e)
        
    
    def _put_load_parquet_files_to_sf(self, stage_path=None):
        """Upload and load Parquet files to Snowflake.
        
        Handles the process of:
        1. Putting files into Snowflake stage
        2. Loading data into appropriate tables
        3. Cleaning up staged files
        """
        sf_stage_name =  os.getenv('METEOLOGICA_STAGE')
        for sf_table_name, parquet_files_path in self.parquet_tables.items():
            if not parquet_files_path:
                logger.info(f"No files for {sf_table_name}")
                continue
            if self.process_mode != HISTORICAL:
                self._drop_overlap(sf_table_name)

            for parquet_file in parquet_files_path:
                parquet_file_name = os.path.basename(parquet_file)
                try:
                    if stage_path:
                        self.sf_adaptor.put_file(parquet_file, sf_stage_name, stage_path=f"{stage_path}/{sf_table_name}")
                    else:
                        self.sf_adaptor.put_file(parquet_file, sf_stage_name)
                    # Not much data, can insert one by one
                    if self.process_mode != HISTORICAL:
                        self.sf_adaptor.load_generic(
                                sf_table_name, 
                                sf_stage_name,
                                parquet_file_name,
                                "(TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE'"
                            )
                        self.sf_adaptor.cleanup_stg(sf_stage_name, parquet_file_name)
                        logger.info(f"Data from {parquet_file_name} loaded to Snowflake table {sf_table_name}")
                except Exception as e:
                    logger.error(f'FAILURE-METEOLOGICA_SNOWFLAKE_SAVE-DATA_TABLE:{sf_table_name}-PARQUET_FILE:{parquet_file}')
                    logger.exception(e)

    def _get_sf_adaptor(self):
        """Create a new Snowflake bulk loader instance.
        
        Returns:
            SnowflakeBulkLoader: Configured bulk loader for Snowflake operations.
        """
        return SnowflakeBulkLoader(
            database=os.getenv('METEOLOGICA_DATABASE'), 
            schema=self.schema,
            warehouse=os.getenv('SF_WAREHOUSE'), 
            role=os.getenv('SF_ROLE')
            )
    
    def extract_from_zip(self, source_path, zipfile, target_path):
        zip_path = os.path.join(source_path, zipfile)
        files_cnt = 0
        df_list = []
        content_id = int(zipfile.split('_')[0])
        result = f"{target_path}/{zipfile.replace('.zip', '')}"
        try:
            with ZipFile(zip_path, 'r') as zObject:   
                zObject.extractall( 
                    path=result)
        except Exception as e:
            logger.error(f'FAILURE-METEOLOGICA_CORRUPT_ZIPFILE-ZIPFILE:{zip_path}')
            logger.exception(e)
            return None
        return result

    def _process_json_folder(self, raw_json_path, stage_path=None):
        self._generate_parquet_files(raw_json_path)
        self._put_load_parquet_files_to_sf(stage_path)

    def _fetch_with_rate_limit(self, content_id, year, month):
        for i in range(5):
            try:
                return self.metodogia_api_handler.get_historical_content_data(content_id, year, month)
            except Exception as e:
                    logger.warning(e)
                    wait_time = 60
                    logger.warning(f"Error while calling API. Retrying in {wait_time} seconds...")
                    time.sleep(wait_time)
        logger.error(f'API_RETRIES_FAILURE-METEOLOGICA_HISTORIC_GAP-CONTENT_ID:{content_id}-YEAR:{year}-MONTH:{month}')
        return None

    def _save_result(self, result, content_id, year, month, save_dir):
        filename = f"{content_id}_{year}_{month}.zip"
        filepath = os.path.join(save_dir, filename)
        with open(filepath, 'wb') as f:
            f.write(result)
        logger.info(f"Saved {filename} to {filepath}")
        return filename

    def _calculate_historical_gaps_trackers(self, zipfile):
        pass
        #self.id_tracker[data_table].append((id, update_id))

    def _load_gap(self, content_id, year, month, stage_path):
        param = f"{content_id}_{year}_{month}"
        result = self._fetch_with_rate_limit(content_id, year, month)
        if result:
            with tempfile.TemporaryDirectory() as tmpdirname:
                zipfile = self._save_result(result, content_id, year, month, tmpdirname)
                #self._calculate_historical_gaps_trackers(zipfile)
                self._process_zip_file(tmpdirname, None, zipfile)

    def _process_zip_file(self, historical_raw_path, stage_path, zipfile):
        logger.info(f"Processing file {zipfile}")
        with tempfile.TemporaryDirectory() as tmpdirname:
            raw_json_path = self.extract_from_zip(historical_raw_path, zipfile, tmpdirname)
            if raw_json_path:
                self._process_json_folder(raw_json_path, stage_path=stage_path)
                # Each zipfile will have it's own new files
                self.reset_trackers()
                return True
            else:
                return False

    def _run_copy_into(self, sf_stage_name, stage_path):
        # Load all files in the stage in parallel to each table
        for target_table in self.json_tables.keys():
            logger.info(f"Copying into table {target_table} from @{sf_stage_name}/{stage_path}/{target_table}")
            self.sf_adaptor.load_generic(
                target_table, 
                sf_stage_name,
                f"{stage_path}/{target_table}",
                "(TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE'"
            )

    def process_historical_data(self):
        historical_raw_path = os.getenv('HISTORICAL_RAW_PATH')
        sf_stage_name =  os.getenv('METEOLOGICA_STAGE')
        stage_path = uuid.uuid4().hex
        files = os.listdir(historical_raw_path)
        sorted_files = sorted(files, key=lambda x: (int(x.split('_')[1]), int(x.split('_')[2].replace('.zip', ''))), reverse=True)
        # Process each zipfile into a parquet file to the stage
        sample = 10
        c = 0
        init = time.time()
        for zipfile in files:
            logger.info(f"Processing file {zipfile}")
            if self._process_zip_file(historical_raw_path, stage_path, zipfile):
                c += 1
                if c > sample:
                    break
        end = time.time()
        logger.info(f"All Zipfiles uploaded to Snowflake stage. Took: {end - init}")
        init = time.time()
        self._run_copy_into(sf_stage_name, stage_path)
        end = time.time()
        logger.info(f"All Files inserted into Snowflake tables. Took: {end - init}")
        logger.info(f"Cleaning up stage @{sf_stage_name}/{stage_path}")
        self.sf_adaptor.cleanup_stg(sf_stage_name, stage_path)

    def process_historical_interval(self, initial_year_month, end_year_month):
        #content_ids = ['1014']
        content_ids = self.params_df['CONTENT_ID'].unique()
        stage_path = uuid.uuid4().hex
        initial_year, initial_month = initial_year_month.split('-')
        end_year, end_month = end_year_month.split('-')
        initial_year, initial_month = int(initial_year), int(initial_month)
        end_year, end_month = int(end_year), int(end_month)
        for content_id in content_ids:
            for year in range(initial_year, end_year + 1):
                for month in range(initial_month, end_month + 1):
                    self._load_gap(content_id, year, month, stage_path)

    def process_lastet_meteologica_data(self, lookback_seconds=500):
        """Main process to fetch, transform, and load latest Meteologica data.
        
        Process flow:
        1. Fetch latest contents from API
        2. Save raw JSON responses
        3. Generate Parquet files from JSON data
        4. Load Parquet files into Snowflake
        """
        latest_contents = self._lastet_contents_from_api(lookback_seconds)
        raw_json_path = self._save_json_to_file(latest_contents, f"latest/raw_json/{self.now}")
        self._process_json_folder(raw_json_path)
                 
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Process Meteologica data")
    parser.add_argument("--process_mode", type=str, nargs='?', const=INCREMENTAL, default=INCREMENTAL, help="One of [INCREMENTAL, HISTORICAL, HISTORICAL_GAP_FILL]")
    parser.add_argument("--lookback_seconds", type=int, nargs='?', const=500, default=500, help="Number of seconds for lookback in API INCREMENTAL mode")
    parser.add_argument("--initial_year_month", type=str, nargs='?', const="", default="", help="Starting YYYY-MM for historical gap fill")
    parser.add_argument("--end_year_month", type=str, nargs='?', const="", default="", help="End YYYY-MM for historical gap fill")
    args = parser.parse_args()
    assert args.process_mode in [INCREMENTAL, HISTORICAL, HISTORICAL_GAP_FILL], f"--process_mode must be one of: {[INCREMENTAL, HISTORICAL, HISTORICAL_GAP_FILL]}"
    meteologica = MeteologicaProcessor(args.process_mode)
    logger.info(f"Processing with {args.process_mode} mode")
    if args.process_mode == INCREMENTAL:
        logger.info(f"Looking back {args.lookback_seconds} seconds")
        meteologica.process_lastet_meteologica_data(lookback_seconds=int(args.lookback_seconds))
    elif args.process_mode == HISTORICAL_GAP_FILL:
        logger.info(f"Processing interval: {args.initial_year_month} - {args.end_year_month}")
        meteologica.process_historical_interval(args.initial_year_month, args.end_year_month)
    else:
        meteologica.process_historical_data()
        
    logger.info(f"Successfully ran Meteologica process.")
