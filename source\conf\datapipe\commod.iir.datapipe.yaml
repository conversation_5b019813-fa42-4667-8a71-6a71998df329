raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/iirftp"
  s3_bucket: "jg-tech1dev-dp-snowflake-poc-data"
  s3_prefix: "iir"
  include_prefix: true

structure: ' {
  "PLANT_**$DATE$**.zip":["PLANT.CSV"],
  "UNIT_**$DATE$**.zip":["UNIT.CSV"]
 }'



snowflake:
  db_name: "COMMOD_UAT"
  schema_name: "iir"

  table_map:
  
    plants:
      pattern: "^.*PLANT.CSV" 
      col_num: 112
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "iir/"
      file_format: "ff_commod_iir"     
    
    units:
      pattern: "^.*UNIT.CSV" 
      col_num: 54
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "iir/"
      file_format: "ff_commod_iir" 
   
