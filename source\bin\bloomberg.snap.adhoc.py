import sys, os, glob, argparse
from strunner import *
setupEnvironment() # sets up environment
from jgdata import *
import stcommon
from stcommon.infra.python.module import function_from_path

parser = argparse.ArgumentParser()
parser.add_argument("--snap_configs", help="Please enter list of tickers separated by comma", required=True, type=str)
options = parser.parse_args()

if options.snap_configs:
   tickers=options.snap_configs
   print(f"Parameter passed are: {tickers}")
else:
   print("Missing Arguments..")

dataset="bloomberg.snap"
import jgdata.datasets.bloomberg.snap


prefix = 'jgdata.datasets'
funct = function_from_path(f"{prefix}.{dataset}","buildBloombergSnaploadDatasetAdhoc")

funct(tickers)
