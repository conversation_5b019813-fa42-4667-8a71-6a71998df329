import glob
import logging
from enum import Enum
from utils.compress_tools.uncompressor import Uncompressor


logging.basicConfig(level=logging.INFO)

class UploadType(Enum):
    QUOTES = 1

class RecursiveUncompressor:
    def __init__(self, root_folder, structure: dict):
        self.structure = structure
        self.root_folder = root_folder
        self.uncompressor = None

    def unpack_file(self, file):
        return Uncompressor(file, self.tmp).uncompress()

    def unnest(self, folder, file_pattern, remaining_structure):
        logging.info(f'Uncompressing file pattern "{file_pattern}"...')
        if type(remaining_structure) is list:
            full_pattern = f'{folder}/{file_pattern}'
            for inner in glob.glob(full_pattern):
                logging.info(f'Adding file "{inner} to final result"...')
                self.uncompressor.finalize(inner)
                yield inner
        else:
            for next_unnest in remaining_structure[file_pattern]:   
                full_pattern = f'{folder}/{file_pattern}'
                for inner in glob.glob(full_pattern):
                    logging.info(f'Uncompressing file "{inner}"...')
                    unpacked_folder = self.uncompressor.uncompress(inner, next_unnest)
                    yield from self.unnest(unpacked_folder, next_unnest, remaining_structure[file_pattern])
                a = 1
        
    def run(self, tmp):
        self.uncompressor = Uncompressor(tmp)
        for root_pattern in self.structure:
            for chunk in self.unnest(self.root_folder, root_pattern, self.structure):
                continue
        return self.uncompressor.target
    
    def run_generator(self, tmp):
        self.uncompressor = Uncompressor(tmp)
        for root_pattern in self.structure:
            yield from self.unnest(self.root_folder, root_pattern, self.structure)
                
    
    def select_upload_strategy(self):
        if self.upload_type == UploadType.QUOTES:
            return
        else:
            raise ValueError(f'Upload type {self.upload_type} not supported')
