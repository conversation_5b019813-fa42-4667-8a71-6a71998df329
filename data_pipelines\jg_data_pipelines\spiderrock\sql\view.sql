-- eqvol.v_securities source

CREATE OR REPLACE VIEW eqvol.v_securities
AS SELECT std.ticker AS sr_ticker,
    std.symbol,
    std.assettype,
    std.symboltype,
    std.name,
    std.numoptions AS approx_num_options,
        CASE
            WHEN std.symboltype::text = ANY (ARRAY['Equity'::character varying::text, 'ETF'::character varying::text, 'ADR'::character varying::text]) THEN TRIM(BOTH FROM std.bbgcompositeticker) || ' Equity'::text
            WHEN std.symboltype::text = 'CashIndex'::text THEN TRIM(BOTH FROM std.ticker) || ' Index'::text
            ELSE NULL::text
        END AS bbg_comp_ticker,
    std.bbgcompositeglobalid AS bbg_comp_figi,
    std.primaryexch,
    std.mic,
        CASE
            WHEN std.symboltype::text = ANY (ARRAY['Equity'::character varying::text, 'ETF'::character varying::text, 'ADR'::character varying::text]) THEN TRIM(BOTH FROM std.bbgexchangeticker) || ' Equity'::text
            WHEN std.symboltype::text = 'CashIndex'::text THEN TRIM(BOTH FROM std.ticker) || ' Index'::text
            ELSE NULL::text
        END AS bbg_exch_ticker,
    std.figi,
    std.bbgglobalid
   FROM eqvol.sr_ticker_definition std;

-- Permissions

ALTER TABLE eqvol.v_securities OWNER TO external_write;
GRANT ALL ON TABLE eqvol.v_securities TO external_write;
GRANT SELECT ON TABLE eqvol.v_securities TO eqvol_read;
GRANT SELECT ON TABLE eqvol.v_securities TO external_read;
GRANT SELECT ON TABLE eqvol.v_securities TO fe_risk;
GRANT SELECT ON TABLE eqvol.v_securities TO fe_risk_ro;
GRANT SELECT ON TABLE eqvol.v_securities TO mds_read;
GRANT SELECT ON TABLE eqvol.v_securities TO tech_fe_risk_ro;