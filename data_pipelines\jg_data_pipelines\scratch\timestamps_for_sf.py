import pandas as pd
from datetime import datetime
import pyarrow as pa
import pyarrow.parquet as pq

# Define start and end dates
start_date = '2000-01-01'
end_date = '2030-12-31'

def create_timestamp_series(freq, output_file):
    """
    Create timestamp series with additional columns and save to parquet
    
    Args:
        freq (str): Frequency for date_range ('T' for minute, 'H' for hour, 'D' for day)
        output_file (str): Output parquet file name
    """
    # Create timestamp series
    dates = pd.date_range(start=start_date, end=end_date, freq=freq)
    
    # Create DataFrame
    df = pd.DataFrame({
        'timestamp': pd.to_datetime(dates),  # Convert to datetime
        'date': dates.date,
        'day_of_week': dates.dayofweek,
        'day_name': dates.day_name().str.upper(),  # Convert to uppercase
        'is_first_business_day': False,  # Will update this
        'is_last_business_day': False    # Will update this
    })

    # Convert timestamp to string representation up to second interval
    df['date_only'] = df['timestamp'].dt.date
    df['timestamp'] = df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
    
    # Calculate business days
    business_dates = pd.date_range(start=start_date, end=end_date, freq='BME')
    first_business_dates = pd.date_range(start=start_date, end=end_date, freq='BMS')
    
    # Convert to date for comparison
    business_dates = business_dates.date
    first_business_dates = first_business_dates.date
    
    # Mark first and last business days
    df.loc[df['date_only'].isin(first_business_dates), 'is_first_business_day'] = True
    df.loc[df['date_only'].isin(business_dates), 'is_last_business_day'] = True
    
    # Drop temporary column
    df = df.drop('date_only', axis=1)
    # print(df.head())
    # Save to parquet
    df.to_parquet(output_file)
    
    print(f"Created {output_file} with {len(df)} rows")

if __name__ == "__main__":
    # create_timestamp_series('T', 'minute_timestamps.parquet')  # 1-minute intervals
    # create_timestamp_series('H', 'hourly_timestamps.parquet')  # 1-hour intervals
    create_timestamp_series('D', 'daily_timestamps.parquet')   # 1-day intervals
