# Source: k8s-monitoring/charts/grafana-agent-logs/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: grafana-k8s-monitoring-grafana-agent-logs
  labels:
    helm.sh/chart: grafana-agent-logs-0.36.0
    app.kubernetes.io/name: grafana-agent-logs
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
rules:
  # Rules which allow discovery.kubernetes to function.
  - apiGroups:
      - ""
      - "discovery.k8s.io"
      - "networking.k8s.io"
    resources:
      - endpoints
      - endpointslices
      - ingresses
      - nodes
      - nodes/proxy
      - nodes/metrics
      - pods
      - services
    verbs:
      - get
      - list
      - watch
  # Rules which allow loki.source.kubernetes and loki.source.podlogs to work.
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/log
      - namespaces
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "monitoring.grafana.com"
    resources:
      - podlogs
    verbs:
      - get
      - list
      - watch
  # Rules which allow mimir.rules.kubernetes to work.
  - apiGroups: ["monitoring.coreos.com"]
    resources:
      - prometheusrules
    verbs:
      - get
      - list
      - watch
  - nonResourceURLs:
      - /metrics
    verbs:
      - get
  # Rules for prometheus.kubernetes.*
  - apiGroups: ["monitoring.coreos.com"]
    resources:
      - podmonitors
      - servicemonitors
      - probes
    verbs:
      - get
      - list
      - watch
  # Rules which allow eventhandler to work.
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - get
      - list
      - watch
  # needed for remote.kubernetes.*
  - apiGroups: [""]
    resources:
      - "configmaps"
      - "secrets"
    verbs:
      - get
      - list
      - watch
  # needed for otelcol.processor.k8sattributes
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["extensions"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
---
# Source: k8s-monitoring/charts/grafana-agent/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: grafana-k8s-monitoring-grafana-agent
  labels:
    helm.sh/chart: grafana-agent-0.36.0
    app.kubernetes.io/name: grafana-agent
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
rules:
  # Rules which allow discovery.kubernetes to function.
  - apiGroups:
      - ""
      - "discovery.k8s.io"
      - "networking.k8s.io"
    resources:
      - endpoints
      - endpointslices
      - ingresses
      - nodes
      - nodes/proxy
      - nodes/metrics
      - pods
      - services
    verbs:
      - get
      - list
      - watch
  # Rules which allow loki.source.kubernetes and loki.source.podlogs to work.
  - apiGroups:
      - ""
    resources:
      - pods
      - pods/log
      - namespaces
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - "monitoring.grafana.com"
    resources:
      - podlogs
    verbs:
      - get
      - list
      - watch
  # Rules which allow mimir.rules.kubernetes to work.
  - apiGroups: ["monitoring.coreos.com"]
    resources:
      - prometheusrules
    verbs:
      - get
      - list
      - watch
  - nonResourceURLs:
      - /metrics
    verbs:
      - get
  # Rules for prometheus.kubernetes.*
  - apiGroups: ["monitoring.coreos.com"]
    resources:
      - podmonitors
      - servicemonitors
      - probes
    verbs:
      - get
      - list
      - watch
  # Rules which allow eventhandler to work.
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - get
      - list
      - watch
  # needed for remote.kubernetes.*
  - apiGroups: [""]
    resources:
      - "configmaps"
      - "secrets"
    verbs:
      - get
      - list
      - watch
  # needed for otelcol.processor.k8sattributes
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["extensions"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
---
# Source: k8s-monitoring/charts/kube-state-metrics/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:    
    helm.sh/chart: kube-state-metrics-5.15.3
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: kube-state-metrics
    app.kubernetes.io/name: kube-state-metrics
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "2.10.1"
  name: grafana-k8s-monitoring-kube-state-metrics
rules:

- apiGroups: ["certificates.k8s.io"]
  resources:
  - certificatesigningrequests
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - configmaps
  verbs: ["list", "watch"]

- apiGroups: ["batch"]
  resources:
  - cronjobs
  verbs: ["list", "watch"]

- apiGroups: ["extensions", "apps"]
  resources:
  - daemonsets
  verbs: ["list", "watch"]

- apiGroups: ["extensions", "apps"]
  resources:
  - deployments
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - endpoints
  verbs: ["list", "watch"]

- apiGroups: ["autoscaling"]
  resources:
  - horizontalpodautoscalers
  verbs: ["list", "watch"]

- apiGroups: ["extensions", "networking.k8s.io"]
  resources:
  - ingresses
  verbs: ["list", "watch"]

- apiGroups: ["batch"]
  resources:
  - jobs
  verbs: ["list", "watch"]

- apiGroups: ["coordination.k8s.io"]
  resources:
  - leases
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - limitranges
  verbs: ["list", "watch"]

- apiGroups: ["admissionregistration.k8s.io"]
  resources:
    - mutatingwebhookconfigurations
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - namespaces
  verbs: ["list", "watch"]

- apiGroups: ["networking.k8s.io"]
  resources:
  - networkpolicies
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - nodes
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - persistentvolumeclaims
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - persistentvolumes
  verbs: ["list", "watch"]

- apiGroups: ["policy"]
  resources:
    - poddisruptionbudgets
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - pods
  verbs: ["list", "watch"]

- apiGroups: ["extensions", "apps"]
  resources:
  - replicasets
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - replicationcontrollers
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - resourcequotas
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - secrets
  verbs: ["list", "watch"]

- apiGroups: [""]
  resources:
  - services
  verbs: ["list", "watch"]

- apiGroups: ["apps"]
  resources:
  - statefulsets
  verbs: ["list", "watch"]

- apiGroups: ["storage.k8s.io"]
  resources:
    - storageclasses
  verbs: ["list", "watch"]

- apiGroups: ["admissionregistration.k8s.io"]
  resources:
    - validatingwebhookconfigurations
  verbs: ["list", "watch"]

- apiGroups: ["storage.k8s.io"]
  resources:
    - volumeattachments
  verbs: ["list", "watch"]
---
# Source: k8s-monitoring/charts/opencost/templates/clusterrole.yaml
# Cluster role giving opencost to get, list, watch required resources
# No write permissions are required
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: grafana-k8s-monitoring-opencost
  labels:
    helm.sh/chart: opencost-1.28.0
    app.kubernetes.io/name: opencost
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "1.108.0"
    app.kubernetes.io/part-of: opencost
    app.kubernetes.io/managed-by: Helm
rules:
  - apiGroups: [""]
    resources:
      - configmaps
      - deployments
      - nodes
      - pods
      - services
      - resourcequotas
      - replicationcontrollers
      - limitranges
      - persistentvolumeclaims
      - persistentvolumes
      - namespaces
      - endpoints
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - extensions
    resources:
      - daemonsets
      - deployments
      - replicasets
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - apps
    resources:
      - statefulsets
      - deployments
      - daemonsets
      - replicasets
    verbs:
      - list
      - watch
  - apiGroups:
      - batch
    resources:
      - cronjobs
      - jobs
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - autoscaling
    resources:
      - horizontalpodautoscalers
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - policy
    resources:
      - poddisruptionbudgets
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
    verbs:
      - get
      - list
      - watch
---
# Source: k8s-monitoring/charts/grafana-agent-events/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: grafana-k8s-monitoring-grafana-agent-events
  labels:
    helm.sh/chart: grafana-agent-events-0.36.0
    app.kubernetes.io/name: grafana-agent-events
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: grafana-k8s-monitoring-grafana-agent-events
subjects:
  - kind: ServiceAccount
    name: grafana-k8s-monitoring-grafana-agent-events
    namespace: monitoring
---
# Source: k8s-monitoring/charts/grafana-agent-logs/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: grafana-k8s-monitoring-grafana-agent-logs
  labels:
    helm.sh/chart: grafana-agent-logs-0.36.0
    app.kubernetes.io/name: grafana-agent-logs
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: grafana-k8s-monitoring-grafana-agent-logs
subjects:
  - kind: ServiceAccount
    name: grafana-k8s-monitoring-grafana-agent-logs
    namespace: monitoring
---
# Source: k8s-monitoring/charts/grafana-agent/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: grafana-k8s-monitoring-grafana-agent
  labels:
    helm.sh/chart: grafana-agent-0.36.0
    app.kubernetes.io/name: grafana-agent
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: grafana-k8s-monitoring-grafana-agent
subjects:
  - kind: ServiceAccount
    name: grafana-k8s-monitoring-grafana-agent
    namespace: monitoring
---
# Source: k8s-monitoring/charts/kube-state-metrics/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:    
    helm.sh/chart: kube-state-metrics-5.15.3
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: kube-state-metrics
    app.kubernetes.io/name: kube-state-metrics
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "2.10.1"
  name: grafana-k8s-monitoring-kube-state-metrics
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: grafana-k8s-monitoring-kube-state-metrics
subjects:
- kind: ServiceAccount
  name: grafana-k8s-monitoring-kube-state-metrics
  namespace: monitoring
---
# Source: k8s-monitoring/charts/opencost/templates/clusterrolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: grafana-k8s-monitoring-opencost
  labels:
    helm.sh/chart: opencost-1.28.0
    app.kubernetes.io/name: opencost
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "1.108.0"
    app.kubernetes.io/part-of: opencost
    app.kubernetes.io/managed-by: Helm
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: grafana-k8s-monitoring-opencost
subjects:
  - kind: ServiceAccount
    name: grafana-k8s-monitoring-opencost
    namespace: monitoring
---
# Source: k8s-monitoring/charts/grafana-agent-events/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana-k8s-monitoring-grafana-agent-events
  labels:
    helm.sh/chart: grafana-agent-events-0.36.0
    app.kubernetes.io/name: grafana-agent-events
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  selector:
    app.kubernetes.io/name: grafana-agent-events
    app.kubernetes.io/instance: grafana-k8s-monitoring
  internalTrafficPolicy: Cluster
  ports:
    - name: http-metrics
      port: 80
      targetPort: 80
      protocol: "TCP"
---
# Source: k8s-monitoring/charts/grafana-agent-logs/templates/cluster_service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana-k8s-monitoring-grafana-agent-logs-cluster
  labels:
    helm.sh/chart: grafana-agent-logs-0.36.0
    app.kubernetes.io/name: grafana-agent-logs
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  clusterIP: 'None'
  selector:
    app.kubernetes.io/name: grafana-agent-logs
    app.kubernetes.io/instance: grafana-k8s-monitoring
  ports:
    # Do not include the -metrics suffix in the port name, otherwise metrics
    # can be double-collected with the non-headless Service if it's also
    # enabled.
    #
    # This service should only be used for clustering, and not metric
    # collection.
    - name: http
      port: 80
      targetPort: 80
      protocol: "TCP"
---
# Source: k8s-monitoring/charts/grafana-agent-logs/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana-k8s-monitoring-grafana-agent-logs
  labels:
    helm.sh/chart: grafana-agent-logs-0.36.0
    app.kubernetes.io/name: grafana-agent-logs
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  selector:
    app.kubernetes.io/name: grafana-agent-logs
    app.kubernetes.io/instance: grafana-k8s-monitoring
  internalTrafficPolicy: Cluster
  ports:
    - name: http-metrics
      port: 80
      targetPort: 80
      protocol: "TCP"
---
# Source: k8s-monitoring/charts/grafana-agent/templates/cluster_service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana-k8s-monitoring-grafana-agent-cluster
  labels:
    helm.sh/chart: grafana-agent-0.36.0
    app.kubernetes.io/name: grafana-agent
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  clusterIP: 'None'
  selector:
    app.kubernetes.io/name: grafana-agent
    app.kubernetes.io/instance: grafana-k8s-monitoring
  ports:
    # Do not include the -metrics suffix in the port name, otherwise metrics
    # can be double-collected with the non-headless Service if it's also
    # enabled.
    #
    # This service should only be used for clustering, and not metric
    # collection.
    - name: http
      port: 80
      targetPort: 80
      protocol: "TCP"
    - name: otlp-grpc
      port: 4317
      targetPort: 4317
      protocol: TCP
    - name: otlp-http
      port: 4318
      targetPort: 4318
      protocol: TCP
    - name: prometheus
      port: 9999
      targetPort: 9999
      protocol: TCP
    - name: jaeger-grpc
      port: 14250
      targetPort: 14250
      protocol: TCP
    - name: jaeger-binary
      port: 6832
      targetPort: 6832
      protocol: TCP
    - name: jaeger-compact
      port: 6831
      targetPort: 6831
      protocol: TCP
    - name: jaeger-http
      port: 14268
      targetPort: 14268
      protocol: TCP
    - name: zipkin
      port: 9411
      targetPort: 9411
      protocol: TCP
---
# Source: k8s-monitoring/charts/grafana-agent/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana-k8s-monitoring-grafana-agent
  labels:
    helm.sh/chart: grafana-agent-0.36.0
    app.kubernetes.io/name: grafana-agent
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
spec:
  type: ClusterIP
  selector:
    app.kubernetes.io/name: grafana-agent
    app.kubernetes.io/instance: grafana-k8s-monitoring
  internalTrafficPolicy: Cluster
  ports:
    - name: http-metrics
      port: 80
      targetPort: 80
      protocol: "TCP"
    - name: otlp-grpc
      port: 4317
      targetPort: 4317
      protocol: TCP
    - name: otlp-http
      port: 4318
      targetPort: 4318
      protocol: TCP
    - name: prometheus
      port: 9999
      targetPort: 9999
      protocol: TCP
    - name: jaeger-grpc
      port: 14250
      targetPort: 14250
      protocol: TCP
    - name: jaeger-binary
      port: 6832
      targetPort: 6832
      protocol: TCP
    - name: jaeger-compact
      port: 6831
      targetPort: 6831
      protocol: TCP
    - name: jaeger-http
      port: 14268
      targetPort: 14268
      protocol: TCP
    - name: zipkin
      port: 9411
      targetPort: 9411
      protocol: TCP
---
# Source: k8s-monitoring/charts/kube-state-metrics/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana-k8s-monitoring-kube-state-metrics
  namespace: monitoring
  labels:    
    helm.sh/chart: kube-state-metrics-5.15.3
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: kube-state-metrics
    app.kubernetes.io/name: kube-state-metrics
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "2.10.1"
  annotations:
spec:
  type: "ClusterIP"
  ports:
  - name: "http"
    protocol: TCP
    port: 8080
    targetPort: 8080
  
  selector:    
    app.kubernetes.io/name: kube-state-metrics
    app.kubernetes.io/instance: grafana-k8s-monitoring
---
# Source: k8s-monitoring/charts/opencost/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana-k8s-monitoring-opencost
  labels:
    helm.sh/chart: opencost-1.28.0
    app.kubernetes.io/name: opencost
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "1.108.0"
    app.kubernetes.io/part-of: opencost
    app.kubernetes.io/managed-by: Helm
spec:
  selector:
    app.kubernetes.io/name: opencost
    app.kubernetes.io/instance: grafana-k8s-monitoring
  type: ClusterIP
  ports:
    - name: http
      port: 9003
      targetPort: 9003
---
# Source: k8s-monitoring/charts/prometheus-node-exporter/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana-k8s-monitoring-prometheus-node-exporter
  namespace: monitoring
  labels:
    helm.sh/chart: prometheus-node-exporter-4.25.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-node-exporter
    app.kubernetes.io/name: prometheus-node-exporter
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "1.7.0"
spec:
  type: ClusterIP
  ports:
    - port: 9100
      targetPort: 9100
      protocol: TCP
      name: metrics
  selector:
    app.kubernetes.io/name: prometheus-node-exporter
    app.kubernetes.io/instance: grafana-k8s-monitoring
---
# Source: k8s-monitoring/charts/grafana-agent-logs/templates/controllers/daemonset.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: grafana-k8s-monitoring-grafana-agent-logs
  labels:
    helm.sh/chart: grafana-agent-logs-0.36.0
    app.kubernetes.io/name: grafana-agent-logs
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: grafana-agent-logs
      app.kubernetes.io/instance: grafana-k8s-monitoring
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: grafana-agent
      labels:
        app.kubernetes.io/name: grafana-agent-logs
        app.kubernetes.io/instance: grafana-k8s-monitoring
    spec:
      serviceAccountName: grafana-k8s-monitoring-grafana-agent-logs
      containers:
        - name: grafana-agent
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-grafanaagent:latest
          imagePullPolicy: IfNotPresent
          args:
            - run
            - /etc/agent/config.river
            - --storage.path=/tmp/agent
            - --server.http.listen-addr=0.0.0.0:80
            - --server.http.ui-path-prefix=/
            - --cluster.enabled=true
            - --cluster.join-addresses=grafana-k8s-monitoring-grafana-agent-logs-cluster
          env:
            - name: AGENT_MODE
              value: flow
            - name: AGENT_DEPLOY_MODE
              value: "helm"
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          ports:
            - containerPort: 80
              name: http-metrics
          readinessProbe:
            httpGet:
              path: /-/ready
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            timeoutSeconds: 1
          volumeMounts:
            - name: config
              mountPath: /etc/agent
            - name: varlog
              mountPath: /var/log
              readOnly: true
        - name: config-reloader
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/config-reloader:latest
          args:
            - --volume-dir=/etc/agent
            - --webhook-url=http://localhost:80/-/reload
          volumeMounts:
            - name: config
              mountPath: /etc/agent
          resources:
            requests:
              cpu: 1m
              memory: 5Mi
      dnsPolicy: ClusterFirst
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
        - effect: NoSchedule
          operator: Exists
      volumes:
        - name: config
          configMap:
            name: grafana-k8s-monitoring-grafana-agent-logs
        - name: varlog
          hostPath:
            path: /var/log
---
# Source: k8s-monitoring/charts/prometheus-node-exporter/templates/daemonset.yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: grafana-k8s-monitoring-prometheus-node-exporter
  namespace: monitoring
  labels:
    helm.sh/chart: prometheus-node-exporter-4.25.0
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: prometheus-node-exporter
    app.kubernetes.io/name: prometheus-node-exporter
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "1.7.0"
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: prometheus-node-exporter
      app.kubernetes.io/instance: grafana-k8s-monitoring
  revisionHistoryLimit: 10
  updateStrategy:
    rollingUpdate:
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      annotations:
        cluster-autoscaler.kubernetes.io/safe-to-evict: "true"
      labels:
        helm.sh/chart: prometheus-node-exporter-4.25.0
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/component: metrics
        app.kubernetes.io/part-of: prometheus-node-exporter
        app.kubernetes.io/name: prometheus-node-exporter
        app.kubernetes.io/instance: grafana-k8s-monitoring
        app.kubernetes.io/version: "1.7.0"
    spec:
      automountServiceAccountToken: false
      securityContext:
        fsGroup: 65534
        runAsGroup: 65534
        runAsNonRoot: true
        runAsUser: 65534
      serviceAccountName: grafana-k8s-monitoring-prometheus-node-exporter
      containers:
        - name: node-exporter
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/prometheus-node-exporter:latest
          imagePullPolicy: IfNotPresent
          args:
            - --path.procfs=/host/proc
            - --path.sysfs=/host/sys
            - --path.rootfs=/host/root
            - --path.udev.data=/host/root/run/udev/data
            - --web.listen-address=[$(HOST_IP)]:9100
          securityContext:
            readOnlyRootFilesystem: true
          env:
            - name: HOST_IP
              value: 0.0.0.0
          ports:
            - name: metrics
              containerPort: 9100
              protocol: TCP
          livenessProbe:
            failureThreshold: 3
            httpGet:
              httpHeaders:
              path: /
              port: 9100
              scheme: HTTP
            initialDelaySeconds: 0
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            httpGet:
              httpHeaders:
              path: /
              port: 9100
              scheme: HTTP
            initialDelaySeconds: 0
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          volumeMounts:
            - name: proc
              mountPath: /host/proc
              readOnly:  true
            - name: sys
              mountPath: /host/sys
              readOnly: true
            - name: root
              mountPath: /host/root
              mountPropagation: HostToContainer
              readOnly: true
      hostNetwork: true
      hostPID: true
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: eks.amazonaws.com/compute-type
                operator: NotIn
                values:
                - fargate
      nodeSelector:
        kubernetes.io/os: linux
      tolerations:
        - effect: NoSchedule
          operator: Exists
      volumes:
        - name: proc
          hostPath:
            path: /proc
        - name: sys
          hostPath:
            path: /sys
        - name: root
          hostPath:
            path: /
---
# Source: k8s-monitoring/charts/grafana-agent-events/templates/controllers/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana-k8s-monitoring-grafana-agent-events
  labels:
    helm.sh/chart: grafana-agent-events-0.36.0
    app.kubernetes.io/name: grafana-agent-events
    app.kubernetes.io/instance: grafana-k8s-monitoring
    
    app.kubernetes.io/version: "v0.40.2"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: grafana-agent-events
      app.kubernetes.io/instance: grafana-k8s-monitoring
  template:
    metadata:
      annotations:
        kubectl.kubernetes.io/default-container: grafana-agent
      labels:
        app.kubernetes.io/name: grafana-agent-events
        app.kubernetes.io/instance: grafana-k8s-monitoring
    spec:
      serviceAccountName: grafana-k8s-monitoring-grafana-agent-events
      containers:
        - name: grafana-agent
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-grafanaagent:latest
          imagePullPolicy: IfNotPresent
          args:
            - run
            - /etc/agent/config.river
            - --storage.path=/tmp/agent
            - --server.http.listen-addr=0.0.0.0:80
            - --server.http.ui-path-prefix=/
          env:
            - name: AGENT_MODE
              value: flow
            - name: AGENT_DEPLOY_MODE
              value: "helm"
            - name: HOSTNAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          ports:
            - containerPort: 80
              name: http-metrics
          readinessProbe:
            httpGet:
              path: /-/ready
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            timeoutSeconds: 1
          volumeMounts:
            - name: config
              mountPath: /etc/agent
        - name: config-reloader
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/config-reloader:latest
          args:
            - --volume-dir=/etc/agent
            - --webhook-url=http://localhost:80/-/reload
          volumeMounts:
            - name: config
              mountPath: /etc/agent
          resources:
            requests:
              cpu: 1m
              memory: 5Mi
      dnsPolicy: ClusterFirst
      nodeSelector:
        kubernetes.io/os: linux
      volumes:
        - name: config
          configMap:
            name: grafana-k8s-monitoring-grafana-agent-events
---
# Source: k8s-monitoring/charts/kube-state-metrics/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana-k8s-monitoring-kube-state-metrics
  namespace: monitoring
  labels:    
    helm.sh/chart: kube-state-metrics-5.15.3
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: metrics
    app.kubernetes.io/part-of: kube-state-metrics
    app.kubernetes.io/name: kube-state-metrics
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "2.10.1"
spec:
  selector:
    matchLabels:      
      app.kubernetes.io/name: kube-state-metrics
      app.kubernetes.io/instance: grafana-k8s-monitoring
  replicas: 1
  strategy:
    type: Recreate
  revisionHistoryLimit: 10
  template:
    metadata:
      labels:        
        helm.sh/chart: kube-state-metrics-5.15.3
        app.kubernetes.io/managed-by: Helm
        app.kubernetes.io/component: metrics
        app.kubernetes.io/part-of: kube-state-metrics
        app.kubernetes.io/name: kube-state-metrics
        app.kubernetes.io/instance: grafana-k8s-monitoring
        app.kubernetes.io/version: "2.10.1"
    spec:
      hostNetwork: false
      serviceAccountName: grafana-k8s-monitoring-kube-state-metrics
      securityContext:
        fsGroup: 65534
        runAsGroup: 65534
        runAsNonRoot: true
        runAsUser: 65534
        seccompProfile:
          type: RuntimeDefault
      containers:
      - name: kube-state-metrics
        args:
        - --port=8080
        - --resources=certificatesigningrequests,configmaps,cronjobs,daemonsets,deployments,endpoints,horizontalpodautoscalers,ingresses,jobs,leases,limitranges,mutatingwebhookconfigurations,namespaces,networkpolicies,nodes,persistentvolumeclaims,persistentvolumes,poddisruptionbudgets,pods,replicasets,replicationcontrollers,resourcequotas,secrets,services,statefulsets,storageclasses,validatingwebhookconfigurations,volumeattachments
        imagePullPolicy: IfNotPresent
        image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/kube-state-metrics:latest
        ports:
        - containerPort: 8080
          name: "http"
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 5
          timeoutSeconds: 5
        readinessProbe:
          httpGet:
            path: /
            port: 8080
          initialDelaySeconds: 5
          timeoutSeconds: 5
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      nodeSelector:
        kubernetes.io/os: linux
---
# Source: k8s-monitoring/charts/opencost/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana-k8s-monitoring-opencost
  labels:
    helm.sh/chart: opencost-1.28.0
    app.kubernetes.io/name: opencost
    app.kubernetes.io/instance: grafana-k8s-monitoring
    app.kubernetes.io/version: "1.108.0"
    app.kubernetes.io/part-of: opencost
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: opencost
      app.kubernetes.io/instance: grafana-k8s-monitoring
  strategy: 
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/name: opencost
        app.kubernetes.io/instance: grafana-k8s-monitoring
    spec:
      serviceAccountName: grafana-k8s-monitoring-opencost
      nodeSelector:
        kubernetes.io/os: linux
      containers:
        - name: grafana-k8s-monitoring-opencost
          image: "${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/kube-cost:latest"
          imagePullPolicy: IfNotPresent
          args:
          ports:
            - containerPort: 9003
              name: http
          resources:
            limits:
              cpu: 999m
              memory: 1Gi
            requests:
              cpu: 10m
              memory: 55Mi
          livenessProbe:
            httpGet:
              path: /healthz
              port: 9003
            initialDelaySeconds: 120
            periodSeconds: 10
            failureThreshold: 3
          readinessProbe:
            httpGet:
              path: /healthz
              port: 9003
            initialDelaySeconds: 120
            periodSeconds: 10
            failureThreshold: 3
          env:
            - name: API_PORT
              value: "9003"
            - name: PROMETHEUS_SERVER_ENDPOINT
              value: "https://prometheus-prod-13-prod-us-east-0.grafana.net/api/prom"
            - name: CLUSTER_ID
              value: "$EKS_CLUSTER_NAME"
            # If username, password or bearer_token are defined, pull from secrets
            - name: DB_BASIC_AUTH_USERNAME
              valueFrom:
                secretKeyRef:
                  name: prometheus-k8s-monitoring
                  key: username
            - name: DB_BASIC_AUTH_PW
              valueFrom:
                secretKeyRef:
                  name: prometheus-k8s-monitoring
                  key: password
            - name: DATA_RETENTION_DAILY_RESOLUTION_DAYS
              value: "15"
            - name: CLOUD_COST_ENABLED
              value: "false"
            - name: CLOUD_COST_MONTH_TO_DATE_INTERVAL
              value: "6"
            - name: CLOUD_COST_REFRESH_RATE_HOURS
              value: "6"
            - name: CLOUD_COST_QUERY_WINDOW_DAYS
              value: "7"
            - name: CLOUD_COST_RUN_WINDOW_DAYS
              value: "3"
            # Add any additional provided variables
            - name: CLOUD_PROVIDER_API_KEY
              value: "AIzaSyD29bGxmHAVEOBYtgd8sYM2gM2ekfxQX4U"
            - name: CURRENT_CLUSTER_ID_FILTER_ENABLED
              value: "true"
            - name: EMIT_KSM_V1_METRICS
              value: "false"
            - name: EMIT_KSM_V1_METRICS_ONLY
              value: "true"
            - name: PROM_CLUSTER_ID_LABEL
              value: "cluster"
---
# Source: k8s-monitoring/charts/grafana-agent/templates/controllers/statefulset.yaml
apiVersion: v1
items:
- apiVersion: apps/v1
  kind: StatefulSet
  metadata:
    annotations:
      meta.helm.sh/release-name: grafana-k8s-monitoring
      meta.helm.sh/release-namespace: monitoring
    generation: 1
    labels:
      app.kubernetes.io/instance: grafana-k8s-monitoring
      app.kubernetes.io/name: grafana-agent
    name: grafana-k8s-monitoring-grafana-agent
    namespace: monitoring
  spec:
    persistentVolumeClaimRetentionPolicy:
      whenDeleted: Retain
      whenScaled: Retain
    podManagementPolicy: Parallel
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app.kubernetes.io/instance: grafana-k8s-monitoring
        app.kubernetes.io/name: grafana-agent
    serviceName: grafana-k8s-monitoring-grafana-agent
    template:
      metadata:
        creationTimestamp: null
        labels:
          app.kubernetes.io/instance: grafana-k8s-monitoring
          app.kubernetes.io/name: grafana-agent
      spec:
        containers:
        - args:
          - run
          - /etc/agent/config.river
          - --storage.path=/tmp/agent
          - --server.http.listen-addr=0.0.0.0:80
          - --server.http.ui-path-prefix=/
          - --cluster.enabled=true
          - --cluster.join-addresses=grafana-k8s-monitoring-grafana-agent-cluster
          env:
          - name: AGENT_MODE
            value: flow
          - name: AGENT_DEPLOY_MODE
            value: helm
          - name: HOSTNAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-grafanaagent:latest
          imagePullPolicy: IfNotPresent
          name: grafana-agent
          ports:
          - containerPort: 80
            name: http-metrics
            protocol: TCP
          - containerPort: 4317
            name: otlp-grpc
            protocol: TCP
          - containerPort: 4318
            name: otlp-http
            protocol: TCP
          - containerPort: 9999
            name: prometheus
            protocol: TCP
          - containerPort: 9411
            name: zipkin
            protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /-/ready
              port: 80
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources: {}
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/agent
            name: config
          - mountPath: /etc/kubernetes-monitoring-telemetry
            name: kubernetes-monitoring-telemetry
        - args:
          - --volume-dir=/etc/agent
          - --webhook-url=http://localhost:80/-/reload
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/config-reloader:latest
          imagePullPolicy: IfNotPresent
          name: config-reloader
          resources:
            requests:
              cpu: 1m
              memory: 5Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /etc/agent
            name: config
        dnsPolicy: ClusterFirst
        nodeSelector:
          kubernetes.io/os: linux
        restartPolicy: Always
        schedulerName: default-scheduler
        securityContext: {}
        serviceAccount: grafana-k8s-monitoring-grafana-agent
        serviceAccountName: grafana-k8s-monitoring-grafana-agent
        terminationGracePeriodSeconds: 30
        volumes:
        - configMap:
            defaultMode: 420
            name: grafana-k8s-monitoring-grafana-agent
          name: config
        - configMap:
            defaultMode: 420
            name: kubernetes-monitoring-telemetry
          name: kubernetes-monitoring-telemetry
    updateStrategy:
      rollingUpdate:
        partition: 0
      type: RollingUpdate
kind: List
metadata:
  resourceVersion: ""
