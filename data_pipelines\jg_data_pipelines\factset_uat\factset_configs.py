from enum import Enum
import os
class FactsetDataType(Enum):
    FUNDAMENTAL_BASIC = "fundamental_basic"
    FUNDAMENTAL_ADVANCED = "fundamental_advanced"
    FUNDAMENTAL_BASIC_DERIVED = "fundamental_basic_derived"
    FUNDAMENTAL_ADVANCED_DERIVED = "fundamental_advanced_derived"
    FUNDAMENTAL_SEC_HUB = "ff_sec_hub"
    SYM_COVERAGE = 'sym_coverage'
    SYMBOL_BBG = "symbol_bbg"
    SYMBOL_SEDOL_HIST = "sym_sedol_hist"
    SYMBOL_TICKER_REGION_HIST = "sym_ticker_region_hist"
    ESTIMATES_QF_ADVANCED_CON = "fe_advanced_conh_qf"
    ESTIMATES_AF_ADVANCED_CON = "fe_advanced_conh_af"
    ESTIMATES_SAF_ADVANCED_CON = "fe_advanced_conh_saf"
    ESTIMATES_SEC_HUB = "fe_sec_hub"
    
class FilenamePattern(Enum):
    ESTIMATES_QF_ADVANCED = "fe_advanced_conh_qf_am_v4_"
    ESTIMATES_AF_ADVANCED = "fe_advanced_conh_af_am_v4_"
    ESTIMATES_SAF_ADVANCED = "fe_advanced_conh_saf_am_v4_"
    FUNDAMENTALS_BASIC = "ff_basic_am_v3"
    FUNDAMENTALS_BASIC_DERIVED = "ff_basic_der_am_v3_"
    FUNDAMENTALS_ADVANCED = "ff_advanced_am_v3_"
    FUNDAMENTALS_ADVANCED_DERIVED = "ff_advanced_der_am_v3_"
    FUNDAMENTAL_SEC_HUB = "ff_sec_hub_v3_"
    SYMBOLOGY = "sym_bbg_v1_"
    SYMBOL_SEDOL_HIST="sym_sedol_hist_v1_"
    SYMBOL_TICKER_REGION_HIST="sym_ticker_hist_global_v1_"
    SYM_COVERAGE="sym_hub_v1_"
    ESTIMATES_BASIC_CON = "fe_basic_conh_am_v4_"
    ESTIMATES_BASIC_ACT = "fe_basic_act_am_v4_"
    ESTIMATES_BASIC_GUID = "fe_basic_guid_am_v4_"
    ESTIMATES_ADVANCED_ACT = "fe_advanced_act_am_v4_"
    ESTIMATES_ADVANCED_GUID = "fe_advanced_guid_am_v4_"
    ESTIMATES_SEC_HUB = "fe_sec_hub_v4_"
    ALL="*"

JFS_FACTSET_BASIC_DIR = "/jfs/tech1/apps/rawdata/factset/fundamentals/ff_basic_am_v3"
FACTSET_BASIC_DIR = "datafeeds/fundamentals/ff_basic_am_v3"

JFS_FACTSET_ADVANCED_DIR = "/jfs/tech1/apps/rawdata/factset/fundamentals/ff_advanced_am_v3/"
FACTSET_ADVANCED_DIR = "datafeeds/fundamentals/ff_advanced_am_v3"

JFS_FACTSET_BASIC_DER_DIR = "/jfs/tech1/apps/rawdata/factset/fundamentals/ff_basic_der_am_v3"
FACTSET_BASIC_DER_DIR = "datafeeds/fundamentals/ff_basic_am_v3"

JFS_FACTSET_ADVANCED_DER_DIR = "/jfs/tech1/apps/rawdata/factset/fundamentals/ff_advanced_der_am_v3/"
FACTSET_ADVANCED_DER_DIR = "datafeeds/fundamentals/ff_advanced_am_v3"

JFS_FUNDAMENTAL_SEC_HUB_DIR = "/jfs/tech1/apps/rawdata/factset/fundamentals/ff_sec_hub_v3"
FUNDAMENTAL_SEC_HUB_DIR = "/datafeeds/fundamentals/ff_sec_hub_v3"

JFS_FACTSET_SYM_BBG = "/jfs/tech1/apps/rawdata/factset/symbology/sym_bbg/"
FACTSET_SYM_BBG = "datafeeds/symbology/sym_bbg"

JFS_FACTSET_SYMBOL_SEDOL_HIST = "/jfs/tech1/apps/rawdata/factset/symbology/sym_sedol_hist/"
FACTSET_SYMBOL_SEDOL_HIST = "datafeeds/symbology/sym_sedol_hist"

JFS_FACTSET_SYMBOL_TICKER_REGION_HIST = "/jfs/tech1/apps/rawdata/factset/symbology/sym_ticker_hist_global/"
FACTSET_SYMBOL_TICKER_REGION_HIST = "datafeeds/symbology/sym_ticker_hist_global"

JFS_FACTSET_SYM_COVERAGE = "/jfs/tech1/apps/rawdata/factset/symbology/sym_hub/"
FACTSET_SYM_COVERAGE = "datafeeds/symbology/sym_hub"

OVERRIDES = {
    "sym_coverage":{"encoding":"windows-1252","num_columns_sym_coverage":14},
    "fundamental_basic":{"num_columns_ff_basic_af":136,"num_columns_ff_basic_qf":132},
    "fundamental_basic_derived":{"num_columns_ff_basic_der_af":74,"num_columns_ff_basic_der_qf":75},
    "fundamental_advanced":{"num_columns_ff_advanced_af":498,"num_columns_ff_advanced_qf":226},
    "fundamental_advanced_derived":{"num_columns_ff_advanced_der_af":234,"num_columns_ff_advanced_der_qf":186}
}

JFS_ESTIMATES_ADVANCED_CON_DIR = "/jfs/tech1/apps/rawdata/factset/estimates/fe_advanced_con_am"
ESTIMATES_ADVANCED_CON_DIR = "datafeeds/estimates/fe_advanced_con_am"

HISTORICAL_ESTIMATES_AF_ADVANCED_DIR = "datafeeds/delta/estimates/fe_advanced_conh_af_am"
HISTORICAL_ESTIMATES_QF_ADVANCED_DIR = "datafeeds/delta/estimates/fe_advanced_conh_qf_am"
HISTORICAL_ESTIMATES_SAF_ADVANCED_DIR = "/datafeeds/delta/estimates/fe_advanced_conh_saf_am"

JFS_ESTIMATES_BASIC_CON_DIR = "/jfs/tech1/apps/rawdata/factset/estimates/fe_basic_con_am"
ESTIMATES_BASIC_CON_DIR = "/datafeeds/estimates/fe_basic_con_am"
HISTORICAL_ESTIMATES_BASIC_CON_DIR = "/datafeeds/delta/estimates/fe_basic_conh_am"

JFS_ESTIMATES_BASIC_ACT_DIR = "/jfs/tech1/apps/rawdata/factset/estimates/fe_basic_act_am"
ESTIMATES_BASIC_ACT_DIR = "/datafeeds/estimates/fe_basic_act_am"
HISTORICAL_ESTIMATES_BASIC_ACT_DIR = "/datafeeds/delta/estimates/fe_basic_act_am"

JFS_ESTIMATES_BASIC_GUID_DIR = "/jfs/tech1/apps/rawdata/factset/estimates/fe_basic_guid_am"
ESTIMATES_BASIC_GUID_DIR = "/datafeeds/estimates/fe_basic_guid_am"
HISTORICAL_ESTIMATES_BASIC_GUID_DIR = "/datafeeds/delta/estimates/fe_basic_guid_am"

JFS_ESTIMATES_ADVANCED_ACT_DIR = "/jfs/tech1/apps/rawdata/factset/estimates/fe_advanced_act_am"
ESTIMATES_ADVANCED_ACT_DIR = "/datafeeds/estimates/fe_advanced_act_am"
HISTORICAL_ESTIMATES_ADVANCED_ACT_DIR = "/datafeeds/delta/estimates/fe_advanced_act_am"

JFS_ESTIMATES_ADVANCED_GUID_DIR = "/jfs/tech1/apps/rawdata/factset/estimates/fe_advanced_guid_am"
ESTIMATES_ADVANCED_GUID_DIR = "/datafeeds/estimates/fe_advanced_guid_am"
HISTORICAL_ESTIMATES_ADVANCED_GUID_DIR = "/datafeeds/delta/estimates/fe_advanced_guid_am"

JFS_ESTIMATES_SEC_HUB_DIR = "/jfs/tech1/apps/rawdata/factset/estimates/fe_sec_hub"
ESTIMATES_SEC_HUB_DIR = "/datafeeds/estimates/fe_sec_hub"

JFS_ESTIMATES_QF_BASIC_DIR = "/jfs/tech1/apps/rawdata/factset/estimates/fe_basic_con_am"
ESTIMATES_QF_BASIC_DIR = "datafeeds/estimates/fe_basic_con_am"
HISTORICAL_ESTIMATES_QF_BASIC_DIR = "datafeeds/delta/estimates/fe_basic_con_am"

JFS_ESTIMATES_SAF_BASIC_DIR = "/jfs/tech1/apps/rawdata/factset/estimates/fe_basic_con_am"
ESTIMATES_SAF_BASIC_DIR = "/datafeeds/estimates/fe_basic_con_am"
HISTORICAL_ESTIMATES_SAF_BASIC_DIR = "datafeeds/delta/estimates/fe_basic_con_am"

FULL_FILE_CHUNK_SIZE = 1000000

SF_USERNAME=os.getenv("SF_USERNAME")
SF_PASSWORD=os.getenv("SF_PASSWORD")
SF_DATABASE=os.getenv("SF_DATABASE_UAT")
SF_WAREHOUSE=os.getenv("SF_WAREHOUSE_UAT")
SF_ROLE=os.getenv("SF_ROLE_UAT")

SNOWFLAKE_CONN = {
    "database": SF_DATABASE, 
    "schema": "CORE",
    "warehouse": SF_WAREHOUSE, 
    "role": SF_ROLE,
}

SNOWFLAKE_BULK_LOADER_CON = {
    "database": SF_DATABASE, 
    "schema": "STAGING",
    "warehouse": SF_WAREHOUSE, 
    "role": SF_ROLE,
}

FUNDAMENTAL_ADVANCED_VARS = {
    "full_file_regex": r"ff_advanced_am_v3_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"ff_advanced_am_v3_(?P<version>\d+)\.zip",
}

FUNDAMENTAL_ADVANCED_DER_VARS = {
    "full_file_regex": r"ff_advanced_der_am_v3_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"ff_advanced_der_am_v3_(?P<version>\d+)\.zip",
}

FUNDAMENTAL_BASIC_VARS = {
    "full_file_regex": r"ff_basic_am_v3_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"ff_basic_am_v3_(?P<version>\d+)\.zip",
}

FUNDAMENTAL_BASIC_DER_VARS = {
    "full_file_regex": r"ff_basic_der_am_v3_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"ff_basic_der_am_v3_(?P<version>\d+)\.zip",
}

FUNDAMENTAL_SEC_HUB_VARS = {
    "full_file_regex": r"ff_sec_hub_v3_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"ff_sec_hub_v3_(?P<version>\d+)\.zip",
}

SYM_BBG_VARS = {
    "full_file_regex": r"sym_bbg_v1_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"sym_bbg_v1_(?P<version>\d+)\.zip",
}

SYMBOL_SEDOL_HIST_VARS = {
    "full_file_regex": r"sym_sedol_hist_v1_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"sym_sedol_hist_v1_(?P<version>\d+)\.zip",
}

SYMBOL_TICKER_REGION_HIST_VARS = {
    "full_file_regex": r"sym_ticker_hist_global_v1_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"sym_ticker_hist_global_v1_(?P<version>\d+)\.zip",
}

SYM_COVERAGE_VARS = {
    "full_file_regex": r"sym_hub_v1_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"sym_hub_v1_(?P<version>\d+)\.zip",
}

ESTIMATES_ADVANCED_QF_VARS = {
    "full_file_regex": r"fe_advanced_conh_qf_am_v4_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"fe_advanced_conh_qf_am_v4_(?P<version>\d+)\.zip",
}

ESTIMATES_ADVANCED_AF_VARS = {
    "full_file_regex": r"fe_advanced_conh_af_am_v4_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"fe_advanced_conh_af_am_v4_(?P<version>\d+)\.zip",
}

ESTIMATES_ADVANCED_SAF_VARS = {
    "full_file_regex": r"fe_advanced_conh_saf_am_v4_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"fe_advanced_conh_saf_am_v4_(?P<version>\d+)\.zip",
}

ESTIMATES_SEC_HUB_VARS = {
    "full_file_regex": r"fe_sec_hub_v4_full_(?P<version>\d+)\.zip",
    "incremental_file_regex": r"fe_sec_hub_v4_(?P<version>\d+)\.zip",
}


FILE_TABLE_MAP = {
"ff_basic_qf_am.txt": "ff_basic_qf",
"ff_basic_af_am.txt": "ff_basic_af",

"ff_basic_der_qf_am.txt": "ff_basic_der_qf",
"ff_basic_der_af_am.txt": "ff_basic_der_af",

"ff_advanced_qf_am.txt": "ff_advanced_qf",
"ff_advanced_af_am.txt": "ff_advanced_af",

"ff_advanced_der_qf_am.txt": "ff_advanced_der_qf",
"ff_advanced_der_af_am.txt": "ff_advanced_der_af",

"ff_sec_coverage.txt": "FF_SEC_COVERAGE",

"sym_bbg.txt": "sym_bbg",
"sym_sedol_hist.txt": "sym_sedol_hist",
"sym_ticker_region_hist.txt":"sym_ticker_region_hist",
"sym_coverage.txt":"sym_coverage",

"fe_advanced_conh_qf_am.txt": "FE_ADVANCED_CONH_QF",
"fe_advanced_conh_af_am.txt": "FE_ADVANCED_CONH_AF",
"fe_advanced_conh_saf_am.txt": "FE_ADVANCED_CONH_SAF",
"fe_sec_coverage.txt": "FE_SEC_COVERAGE"
}