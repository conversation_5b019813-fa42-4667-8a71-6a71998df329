class DataTable extends ModelBase {
    private tableElement: HTMLTableElement;
    public data: any[];
    public filteredData: any[];
    private observers: Array<(data: any[]) => void> = [];
    private rowClickHandler: (rowId: string, rowIndex: number, colIndex: number) => void;
    private baseUrl =  `${window.location.protocol}//${window.location.host}`;
    private currentUser: any
    private table_headers: { [key: string]: string };

    constructor(tableElementId: string, data: any[], rowClickHandler: (rowId: string, rowIndex: number, colIndex: number) => void, currrentUser: any, table_headers: { [key: string]: string }) {
        super();
        this.tableElement = document.getElementById(tableElementId) as HTMLTableElement;
        this.data = data;
        this.filteredData = data;
        this.currentUser = currrentUser;
        this.table_headers = table_headers
        
        this.rowClickHandler = rowClickHandler;
        this.handleTabClick = this.handleTabClick.bind(this)
    }

    handleTabClick(tab_name: string, newData: any[], table_headers: { [key: string]: string }) {
        this.data = newData;
        this.initializeSearch(tab_name, this.table_headers)
        this.renderTable(this.data, tab_name)
    }

    renderTable(data: any[], tab_name?: string): void {
        this.clearTable();
        data = [data[0], ...data.slice(1).sort((a, b) => a.id - b.id)]
        const thead = this.tableElement.createTHead();
        const headerRow = thead.insertRow();

        const columnsNotToBeDisplayed = ["id", "owner_id"]

        // Create headers        
        Object.keys(this.table_headers).forEach((value) => {
                const th = document.createElement("th");
                th.style.width = "10px"
                th.textContent = value as string;
                headerRow.appendChild(th);

        });
        // Create data rows
        const tbody = this.tableElement.createTBody();
        data.slice(1).forEach((rowData, rowIndex) => {
            const row = tbody.insertRow();
            row.setAttribute("data-row-id", rowData.rowId);
            columnsNotToBeDisplayed.forEach(col => delete rowData[col])
            rowData['date_last_updated'] = new Date(rowData['date_last_updated']).toLocaleDateString("en-us");

            let filteredRowData:string[] = []
            Object.values(this.table_headers).forEach(key => { 
                if (key as string in rowData) {
                    filteredRowData.push(rowData[key]); 
                } 
            });
            

            filteredRowData.forEach((cellData, colIndex) => {
                    const cell = row.insertCell();
                    cell.innerHTML = cellData as string;
                    // Apply background color based on value
                    if (cell.textContent === 'Green' || cell.textContent === 'green') {
                        cell.classList.add('green');
                    } else if (cell.textContent === "Amber" || cell.textContent === 'amber') {
                        cell.classList.add('amber');
                    } else if (cell.textContent === "Red" || cell.textContent === 'red') {
                        cell.classList.add('red');
                    } else if (cell.textContent === "In Prod") {
                        cell.classList.add('green');
                    } else if (cell.textContent === "In Progress") {
                        cell.classList.add('blue');
                    } else if (cell.textContent === "Deferred") {
                        cell.classList.add('grey');
                    }

                if(this.currentUser?.isAdmin){
                    const edit_btn = document.getElementById('edit-row')! as HTMLButtonElement;
                    const delete_btn = document.getElementById('delete-row')! as HTMLButtonElement;
                    document.querySelectorAll('[data-action="edit"]').forEach(button => {
                        // button.classList.toggle('hidden')
                        // if(button.classList.contains('hidden')) {
                            button.classList.remove('hidden')
                        // }
                    })
                    document.querySelectorAll('[data-action="delete"]').forEach(button => {
                        // button.classList.toggle('hidden')
                        // if(button.classList.contains('hidden')) {
                            button.classList.remove('hidden')
                        // }
                    })
    
                    // edit_btn.style.display = 'inline'
                    // delete_btn.style.display = 'inline'
                } else {
                    document.querySelectorAll('[data-action="edit"]').forEach(button => {
                        // button.classList.toggle('hidden')
                        // if(button.classList.contains('hidden')) {
                            button.classList.add('hide')
                        // }
                    })
                    document.querySelectorAll('[data-action="delete"]').forEach(button => {
                        // button.classList.toggle('hidden')
                        // if(button.classList.contains('hidden')) {
                            button.classList.add('hide')
                        // }
                    })

                }

            });
        });

        document.querySelectorAll('.action').forEach(button => {
          button.addEventListener('click', event => {
            const target = event.target as HTMLElement;
            const id = target.getAttribute('data-id')!;
            const action = target.getAttribute('data-action');
            
            switch(action) {
              case 'view':
                this.viewRow(id);
                break;
              case 'view-hist':
                DataCatalogController.openTab(event, 'ETLstatus')
                break;                
              case 'edit':
                this.editRow(id,tab_name!);
                break;
              case 'delete':
                this.deleteRow(id);
                break;
            }
          });
        });
    }

    private async viewRow(rowId: string) {
        if (rowId === "header") {   // Skip header row
            Logger.info("Header row clicked, skipping...");
            return;
        }
        const currentRowData = this.getRowDataById(rowId);

        const save_dataset_btn = document.getElementById('save-btn')! as HTMLButtonElement;
        save_dataset_btn.hidden = true;
        const tableElement = document.getElementById('detailsTable') as HTMLTableElement;
        tableElement.innerHTML = ''; // Clear existing content
        if(currentRowData["historical_data_start_date"] === "1700-01-01") currentRowData["historical_data_start_date"] = '';
        const columnsToBeSkipped = ['Action',"Download Status", "ETL Status", "Historical Load Status", "DQ Check", "jfs_rag_status", "etl_rag_status", "whdv_rag_status", "vendor_account_manager", "vendor_account_manager_work_phone", "vendor_account_manager_mobile", "vendor_account_manager_email", "vendor_sales_specialist", "vendor_sales_specialist_work_phone", "vendor_sales_specialist_mobile", "vendor_sales_specialist_email", "vendor_technical_account_manager", "vendor_technical_account_manager_work_phone", "vendor_technical_account_manager_mobile", "vendor_technical_account_manager_email", "customer_success_manager_product", "customer_success_manager_product_work_phone", "customer_success_manager_product_mobile", "customer_success_manager_product_email", "teams_channel_name", "sftp_details", "airflow_dag_names","rowId"]
        for(const key in currentRowData) {
            if(!columnsToBeSkipped.includes(key)){
                const row = document.createElement('tr');
                const cellKey = document.createElement('th');
                cellKey.setAttribute("style","text-wrap:wrap; border:1px solid #ddd;text-align:left;max-width:800px;padding:5px")
                cellKey.textContent = key;
                row.appendChild(cellKey);

                const cellValue = document.createElement('td');
                cellValue.setAttribute("style","text-wrap:wrap; border:1px solid #ddd;text-align:left;max-width:800px;padding:5px;white-space: break-spaces")
                cellValue.textContent = currentRowData[key];
                row.appendChild(cellValue);

                tableElement.appendChild(row);
            }
                
            DataCatalogController.openTab(new Event('click'), 'Details');
        }
    }

    private async editRow(rowId: string, tab_name:string) {
        if (rowId === "header") {   // Skip header row
            Logger.info("Header row clicked, skipping...");
            return;
        }
        // Add additional logic to handle row click, such as loading detailed data for the clicked row
        const currentRowData = this.getRowDataById(rowId);

        const save_dataset_btn = document.getElementById('save-btn')! as HTMLButtonElement;
        save_dataset_btn.hidden = false;
        const tableElement = document.getElementById('detailsTable') as HTMLTableElement;
        tableElement.innerHTML = ''; // Clear existing content
        if(currentRowData["historical_data_start_date"] === "1700-01-01") currentRowData["historical_data_start_date"] = '';
        for (const key in currentRowData) {
            const columnsToBeSkipped = ['id', 'Action', 'rowId', 'owner_id', "Download Status", "ETL Status", "Historical Load Status", "DQ Check", "jfs_rag_status", "etl_rag_status", "whdv_rag_status", "vendor_account_manager", "vendor_account_manager_work_phone", "vendor_account_manager_mobile", "vendor_account_manager_email", "vendor_sales_specialist", "vendor_sales_specialist_work_phone", "vendor_sales_specialist_mobile", "vendor_sales_specialist_email", "vendor_technical_account_manager", "vendor_technical_account_manager_work_phone", "vendor_technical_account_manager_mobile", "vendor_technical_account_manager_email", "customer_success_manager_product", "customer_success_manager_product_work_phone", "customer_success_manager_product_mobile", "customer_success_manager_product_email", "teams_channel_name", "sftp_details", "airflow_dag_names", "rowId"]
            if (!columnsToBeSkipped.includes(key)) {
                const row = document.createElement('tr');
                const cellKey = document.createElement('th');
                cellKey.setAttribute("style", "text-wrap:wrap; border:1px solid #ddd;text-align:left;max-width:800px;padding:10px")
                cellKey.textContent = key;
                row.appendChild(cellKey);
                const cellValue = document.createElement('td');
                cellValue.setAttribute("style", "text-wrap:wrap; border:1px solid #ddd;text-align:left;max-width:800px;padding:10px;white-space: break-spaces;min-width: 500px")
                cellValue.textContent = currentRowData[key];

                // Add event listener to make cell editable
                cellValue.addEventListener('click', function () {
                    cellValue.contentEditable = "true";
                    cellValue.focus();
                    // Save the new value on blur or enter key press
                    const saveValue = () => {
                        cellValue.contentEditable = "false";
                        const newValue = cellValue.textContent || ''
                        cellValue.textContent = newValue;
                        currentRowData[key] = newValue;
                    };
                    cellValue.addEventListener('blur', saveValue);
                    cellValue.addEventListener('keydown', function (event) {
                        if (event.key === 'Enter') {
                            saveValue();
                        }
                    });
                });
                row.appendChild(cellValue);
                tableElement.appendChild(row);
            }
            DataCatalogController.openTab(new Event('click'), 'Details');
        }
        // onclick of Save button
        save_dataset_btn.addEventListener('click', async () => {
            const modal = document.getElementById("modal")!;
            modal.style.display = "flex";
            const yesButton = document.getElementById("yesButton")!;
            const noButton = document.getElementById("noButton")!;
            // saveButton.addEventListener("click", () => {
            //     modal.style.display = "flex";
            // });
            yesButton.addEventListener("click", () => {
                modal.style.display = "none";
            });
            noButton.addEventListener("click", () => {
                modal.style.display = "none";
            });
            yesButton.addEventListener("click", async () => {

            const rowData = currentRowData;
            
            if(tab_name === 'datacatalog') {
                ['Action', 'rowId', 'date_last_updated'].forEach((key) => delete rowData[key])
                const req:DataCatalog = rowData
                req.historical_data_start_date = new Date(req.historical_data_start_date)
                const request = {
                    dataset: req
                }
                try {
                    const response = await fetch(`${this.baseUrl}/api/rows/${currentRowData.dataset_id}`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            Authorization: "Bearer " + localStorage.getItem("userToken")
                        },
                        body: JSON.stringify(req)
                    });
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    } else {
                        alert("row edited successfully");
                        location.reload();
                    }
                    const result = await response.json();
                    console.log("Dataset editted successful:", result);
                } catch (error) {
                    console.error("Error editing row:", error);
                }
            } else {
                ['Action', 'rowId', 'date_last_updated'].forEach((key) => delete rowData[key])
                const req = rowData
                try {
                    const response = await fetch(`${this.baseUrl}/api/updateApp`, {
                        method: "PUT",
                        headers: {
                            "Content-Type": "application/json",
                            Authorization: "Bearer " + localStorage.getItem("userToken")
                        },
                        body: JSON.stringify(req)
                    });
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    } else {
                        alert("row edited successfully");
                        location.reload();
                    }
                    const result = await response.json();
                    console.log("Dataset editted successful:", result);
                } catch (error) {
                    console.error("Error editing row:", error);
                }

            }
            })
    })
    }

    private async deleteRow(rowId: string) {
        const modal = document.getElementById("modal")!;
        modal.style.display = "flex";
        const yesButton = document.getElementById("yesButton")!;
        const noButton = document.getElementById("noButton")!;

        yesButton.addEventListener("click", (event) => {
            event.preventDefault()
            modal.style.display = "none";
        });

        noButton.addEventListener("click", () => {
            modal.style.display = "none";
        });

        yesButton.addEventListener("click", async () => {
            const currentRowData = this.getRowDataById(rowId);
            const requestOptions = {
                method: "DELETE",
                headers: {
                  "Content-Type": "application/json",
                  Authorization: "Bearer " + localStorage.getItem("userToken"),
                },
              };
            const response = await fetch(`${this.baseUrl}/api/rows/${currentRowData.dataset_id}`, requestOptions);
            if (!response.ok) {
              console.log("Failed to delete lead");
            } else {
              alert("Record deleted successfully");
            }
            this.data.splice(this.data.findIndex(row => row.dataset_id == currentRowData.dataset_id), 1)
            this.renderTable(this.data);
        });
    }

    private loadData(dataSetId: string): void {
        fetch(`/api/data_catalog/${dataSetId}`)
                .then((response) => response.json())
                .then((data) => {
                    // this.populateTabs(data);
                    // this.updateTable(data);  // Make sure to update the table
                })
                .catch((error) => Logger.error("Error loading data: " + error));
        }
        private populateTabs(data: any): void {
            this.model.summaryContent = data.summary;
            this.model.preparationStepsContent = data.preparationSteps;
            this.model.dataSampleContent = data.dataSample;
            this.model.dependenciesContent = data.dependencies;
            this.model.supportContactsContent = data.supportContacts;
            this.model.supportNotesContent = data.supportNotes;
        }

    initializeSearch(tab_name: string, table_headers: { [key: string]: string }): void {
        const columnDropdown = document.getElementById(tab_name+'_columnDropdown') as HTMLSelectElement;
        const searchInput = document.getElementById(tab_name+'_searchInput') as HTMLInputElement;
        const searchButton = document.getElementById('searchButton') as HTMLButtonElement;

        columnDropdown.innerHTML = ''
        const table_headers_list = Object.keys(table_headers);
        table_headers_list.unshift('ALL')
        const columnsToBeSkipped = ["id", "Action", "dataset_id", "rowId", "owner_id"]
        // Populate the dropdown with column names
        table_headers_list.forEach((key) => {
            if (!columnsToBeSkipped.includes(key as string)) {
                const option = document.createElement("option");
                option.value = key as string;
                option.textContent = key as string;
                columnDropdown.appendChild(option);
            }
        });
        searchInput.addEventListener('input', () => {
            if (searchInput.value == '') {
                this.renderTable(this.data)
            } else {
                const selectedColumn = columnDropdown.value;
                const searchText = searchInput.value;
                this.filterTable(selectedColumn, searchText);
            }
        })
    }

    private filterTable(column: string, text: string): void {
        if (column && text) {
            let filtereddata;
            if (column === 'ALL') {
                filtereddata = this.data.slice(1).filter(row => {
                    return Object.values(row).some(value => String(value).toLowerCase().includes(text.toLowerCase())
                    );
                });
            } else {
                const col = this.table_headers[column]
                console.log(col)
                filtereddata = this.data.slice(1).filter(row => row[col].toString().toLowerCase().includes(text.toLowerCase()));
            }
            const header = this.table_headers
            this.filteredData = [header, ...filtereddata]
        } else {
            this.filteredData = this.data;
        }
        this.renderTable(this.filteredData);
    }

    private clearTable(): void {
        this.tableElement.innerHTML = ''; // Clear existing content
    }

    private makeCellEditable(cell: HTMLTableCellElement, rowIndex: number, colIndex: number): void {
        const originalValue = cell.textContent || '';
        const editDisabledColumns = ['dataset_name', 'dataset_id', 'rowId', 'last_updated_by', 'date_last_updated']
        if (editDisabledColumns.includes(Object.keys(this.data[0])[colIndex])) {
            alert(Object.keys(this.data[0])[colIndex] + " column cannot be edited")
        } else {
            cell.contentEditable = "true";
            cell.focus();

            const onEnter = (event: KeyboardEvent) => {
                if (event.key === "Enter") {
                    cell.contentEditable = "false";
                    const newValue = cell.textContent || '';
                    this.setCell(rowIndex, colIndex, newValue);
                    const changes = [{
                        rowId: this.data[rowIndex].rowId,
                        dataset_Id: this.data[rowIndex].id,
                        [Object.keys(this.data[0])[colIndex]]: newValue,
                    }];
                    // Trigger API call
                    this.syncChangesToExcel(changes);

                    this.notifyObservers();
                    cell.removeEventListener("keydown", onEnter);
                }
            };

            cell.addEventListener("keydown", onEnter);
        }
    }

    private async syncChangesToExcel(changes: any[]): Promise<void> {
        try {
            const response = await fetch(`${this.baseUrl}/api/sync`, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ changes }),
            });
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const result = await response.json();
            console.log("Sync successful:", result);
        } catch (error) {
            console.error("Error syncing changes:", error);
        }
    }


    public getCell(row: number, col: number): string {
        return this.data[row][Object.keys(this.data[0])[col]] as string;
    }

    public setCell(row: number, col: number, value: string): void {
        this.data[row][Object.keys(this.data[0])[col]] = value;
        this.renderTable(this.data);
    }

    public getCellByRef(ref: string): string {
        const col = ref.charCodeAt(0) - 65; // 'A' -> 0, 'B' -> 1, etc.
        const row = parseInt(ref.substring(1)) - 1; // '1' -> 0, '2' -> 1, etc.
        return this.getCell(row, col);
    }

    public setCellByRef(ref: string, value: string): void {
        const col = ref.charCodeAt(0) - 65;
        const row = parseInt(ref.substring(1)) - 1;
        this.setCell(row, col, value);
    }

    public getRowDataById(rowId: string): any | null {
        return this.data.find(row => row.rowId === rowId) || null;
    }

    public addObserver(observer: (data: any[]) => void): void {
        this.observers.push(observer);
    }

    private notifyObservers(): void {
        this.observers.forEach(observer => observer(this.data));
    }

    public attachTableEventListeners(eventType: string, handler: (rowId: string, rowIndex: number, colIndex: number) => void): void {
        const rows = this.tableElement.querySelectorAll("[data-row-id]");
        rows.forEach((row, rowIndex) => {
            const rowId = row.getAttribute("data-row-id");
            row.querySelectorAll('td').forEach((cell, colIndex) => {
                cell.addEventListener(eventType, () => {
                    if (rowId) {
                        handler(rowId, rowIndex + 1, colIndex);
                    }
                });
            });
        });
    }
}
