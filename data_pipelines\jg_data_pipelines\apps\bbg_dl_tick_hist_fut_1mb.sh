echo "Downloading Futures 1 min bars using DL Tick Hist."

source $HOME/.bashrc

set -e

python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type TRADE >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_trd.log 2>&1
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/send_notification.py "Future 1min bars of type TRADE done" "<EMAIL>"

python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type BID >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_bid.log 2>&1
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/send_notification.py "Future 1min bars of type BID done" "<EMAIL>"

python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type ASK >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_ask.log 2>&1
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/send_notification.py "Future 1min bars of type ASK done" "<EMAIL>"

echo "Downloaded Futures 1 min bars using DL Tick Hist."
