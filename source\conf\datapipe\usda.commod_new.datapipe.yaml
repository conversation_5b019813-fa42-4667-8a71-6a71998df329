raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/usda_agri"
  s3_bucket: "jg-data-dp-vendor-data"
  s3_prefix: "usda-agri"
  include_prefix: true

  structure: '[
  "esr_exports_$DATE$.csv",
  "gats_census_import_$DATE$.csv",
  "gats_census_export_$DATE$.csv" ,
  "gats_census_reexport_$DATE$.csv", 
  "gats_untrade_import_$DATE$.csv" ,
  "gats_untrade_export_$DATE$.csv" ,
  "gats_untrade_reexport_$DATE$.csv",
  "gats_customs_district_import_$DATE$.csv" ,
  "gats_customs_district_export_$DATE$.csv" ,
  "gats_customs_district_reexport_$DATE$.csv" ,
  "psd_country_snd_$DATE$.csv" ,
  "psd_world_snd_$DATE$.csv" 
  ]'

  
snowflake:
  db_name: "Vendor_raw"
  schema_name: "USDA_COMMOD"

  table_map:
  
        
    ESR_EXPORTS_RAW:
        pattern: "^esr_exports_$DATE$.csv" 
        col_num: 13
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    GATS_CENSUS_IMPORT_raw:
        pattern: "^gats_census_import_$DATE$.csv" 
        col_num: 15
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    GATS_CENSUS_EXPORT_raw:
        pattern: "^gats_census_export_$DATE$.csv" 
        col_num: 10
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    GATS_CENSUS_REEXPORT_raw:
        pattern: "^gats_census_reexport_$DATE$.csv" 
        col_num: 10
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 


    GATS_UNTRADE_IMPORT_raw:
        pattern: "^gats_untrade_import_$DATE$.csv" 
        col_num: 11
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    GATS_UNTRADE_EXPORT_raw:
        pattern: "^gats_untrade_export_$DATE$.csv" 
        col_num: 11
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 


    GATS_UNTRADE_REEXPORT_raw:
        pattern: "^gats_untrade_reexport_$DATE$.csv" 
        col_num: 11
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    GATS_CUSTOM_DISTRICT_IMPORT_raw:
        pattern: "^gats_customs_district_import_$DATE$.csv" 
        col_num: 16
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 
    
    GATS_CUSTOM_DISTRICT_EXPORT_raw:
        pattern: "^gats_customs_district_export_$DATE$.csv" 
        col_num: 11
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 


    GATS_CUSTOM_DISTRICT_REEXPORT_raw:
        pattern: "^gats_customs_district_reexport_$DATE$.csv" 
        col_num: 11
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    PSD_COUNTRY_SND_raw:
        pattern: "^psd_country_snd_$DATE$.csv" 
        col_num: 8
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 

    PSD_World_SND_Raw:
        pattern: "^psd_world_snd_$DATE$.csv" 
        col_num: 8
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI" 
