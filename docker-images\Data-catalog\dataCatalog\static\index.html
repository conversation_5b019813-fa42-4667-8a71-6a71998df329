<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Data Catalog</title>
    <link rel="stylesheet" type="text/css" href="/static/css/data_catalog.css?v=20241025043954">
    <script src="/static/ts/data_catalog_controller.js?v=20241025043954" defer></script>
    <script src="/static/ts/model_base.js?v=20241025043954" defer></script>
    <script src="/static/ts/web_socket_client.js?v=20241025043954" defer></script>
    <script src="/static/ts/logger.js?v=20241025043954" defer></script>
    <script src="/static/ts/config.js?v=20241025043954" defer></script>
    <script src="/static/ts/data_catalog_model.js?v=20241025043954" defer></script>
    <script src="/static/ts/data_table.js?v=20241025043954" defer></script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            const dataCatalogController = new DataCatalogController();
        });
    </script>
</head>

<body>
    <div class="tabs">
        <button class="tablinks" id="datacatalog_tab" onclick="DataCatalogController.openTab(event, 'MarketData')">Data Catalog</button>
        <button class="tablinks" onclick="DataCatalogController.openTab(event, 'ETLstatus')">ETL Sanity Check</button>
        <!-- <button class="tablinks" onclick="DataCatalogController.openTab(event, 'OnboardingDatasetStatus')">Dataset Onboarding Progress</button> -->
        <button class="tablinks"id="appcatalog_tab" onclick="DataCatalogController.openTab(event, 'AppCatalog')">App Catalog</button>


        <button id="login-button">Login</button>
        <button id="logout-button">Logout</button>

    </div>

    <div id="MarketData" class="tabcontent">
        <div class="header">
            <div class="input-op">

                <div>
                    <select class="columnDropdown" id="datacatalog_columnDropdown"></select>
                    <input type="text" id="datacatalog_searchInput" placeholder="Search..."
                        style="width: 200px; margin-bottom: 10px; font-size: 10px">
                </div>
                <div class="add-btn">
                    <input type="text" id="add_input" placeholder="Add Dataset name" value='' style="font-size: 10px">
                    <button id="add_dataset">Add Dataset</button>
                </div>
            </div>

        </div>
        <table id="datacatalogTable"></table>
    </div>

    <div id="Details" class="tabcontent">
        <div class="details_header">
            <span class="header">Details</span>
            <button id="save-btn" class="save-btn">Save</button>
        </div>
        <table id="detailsTable"></table>
    </div>

    <div id="statusBar" class="status-bar" style="display: none"></div>

    <div id="modal" class="modal">
        <div class="modal-content">
            <p class="message">Are you sure you want to proceed?</p>
            <button id="yesButton">Yes</button>
            <button id="noButton">No</button>
        </div>
    </div>

    <div id="ETLstatus" class="tabcontent">
        <div style = "display:flex; ">
        <h2>ETL Status Tracker</h2>
        <div style="margin-left: auto">
        <label id='date_input' for="datePicker" style="font-family:Arial, Helvetica, sans-serif; font-size:12px; font-weight:bold">Select Date:</label>
        <input type="date" id="datePicker" max="" style="font-family:Arial, Helvetica, sans-serif; font-size:10px">
        <button id="getDataBtn">Get Data</button>
        <p id="selectedDate" style="font-family:Arial, Helvetica, sans-serif; font-size: 12px; font-weight:bold"></p>
        </div>
    </div>
    <div style="display:flex; width:100%; margin-bottom: 10px">
        <input type="text" id="ETLsearchInput" placeholder="Search..." style="width: 200px; font-size: 10px">
        <div style="display:flex; flex-direction: row; gap:10px; margin-left:auto;">
            <p id="refreshedAt" style="font-family:Arial, Helvetica, sans-serif; font-size: 10px; font-weight: bold; margin-bottom: 0px; margin-top: 6px"></p>
            <button id="refresh-btn">Refresh</button>
        </div>
    </div>
    <table id="statusTable"></table>
    </div>

    <!-- Tooltip -->
    <div id="tooltip" class="tooltip"></div>
    <!-- Modal -->
    <div id="etl_modal" class="etl_modal">
    <div id="etl_modalContent" class="etl_modal-content">
    </div>
</div>

<!-- <div id="OnboardingDatasetStatus" class="tabcontent">
    <div style = "display:flex; ">
    <h2>Onboarding Datasets Tracker</h2>

    <div style="margin-left: auto">
    <label for="onboarding_datePicker" style="font-family:Arial, Helvetica, sans-serif; font-size:12px; font-weight:bold">Select Date:</label>
    <input type="date" id="onboarding_datePicker" style="font-family:Arial, Helvetica, sans-serif; font-size:10px">
    <button id="onboarding_getDataBtn">Get Data</button>
    <p id="onboarding_selectedDate" style="font-family:Arial, Helvetica, sans-serif; font-size: 12px; font-weight:bold"></p>
    </div>
</div>
<button id = "edit_status">Edit</button>
<button id="submit_status" style="display:none;">Submit</button>
    <table id="onboardingDatasetTable"></table>
</div> -->

<div id="AppCatalog" class="tabcontent">
    <div class="header">
        <div class="input-op">

            <div>
                <select class="columnDropdown" id="appcatalog_columnDropdown"></select>
                <input type="text" id="appcatalog_searchInput" placeholder="Search..."
                    style="width: 200px; margin-bottom: 10px; font-size: 10px">
            </div>
        </div>

    </div>
    <table id="appcatalogTable"></table>
</div>

</body>

</html>