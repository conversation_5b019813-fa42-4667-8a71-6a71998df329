apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "openmetadata-ingress"
  annotations:
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/certificate-arn: ${SSL_CERTIFICATE_ARN}
  labels:
    app: openmetadata-nginx-ingress
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: "openmetadata"
              port:
                number: 8585
        - path: /admin
          pathType: Prefix
          backend:
            service:
              name: "openmetadata"
              port:
                number: 8586