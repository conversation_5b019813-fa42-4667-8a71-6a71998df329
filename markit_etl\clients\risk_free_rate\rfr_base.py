from typing import Literal, List
import requests
import zipfile
import io
import os
import datetime
import pathlib
import pandas as pd
from utils.logger import logger, log_execution_time


def load_calendar(calendar_name: Literal['NYM', 'TYO']) -> List[datetime.date]:
    """Load a holiday calendar by name."""

    if calendar_name == 'NYM':
        current_dir = pathlib.Path(__file__).parent
        calendar_dir = current_dir / '..' / '..' / 'config'
        with open(calendar_dir / 'nym_calendar.csv', 'r') as file:
            return [datetime.datetime.strptime(line.strip(), '%Y%m%d').date() for line in file.readlines()]
    elif calendar_name == 'TYO':
        current_dir = pathlib.Path(__file__).parent
        calendar_dir = current_dir / '..' / '..' / 'config'
        with open(calendar_dir / 'tyo_calendar.csv', 'r') as file:
            return [datetime.datetime.strptime(line.strip(), '%Y%m%d').date() for line in file.readlines()]


class MarkitRfrClient:
    def __init__(self):
        # Load environment variables from .env file
        self.email = os.getenv('MARKIT_EMAIL')
        self.attestation_date = os.getenv('MARKIT_EMAIL_ATTESTATION_DATE')

        if not self.email or not self.attestation_date:
            raise ValueError("MARKIT_EMAIL not found in environment variables")

    def check_attestation_date(self) -> None:
        "Checks if the attestation date is older than 10 months and logs an error if it is. Used to send warning logs to grafana alerts to refresh the attested email."
        attestataion_date_obj = datetime.datetime.strptime(self.attestation_date, '%Y-%m-%d')
        if attestataion_date_obj + datetime.timedelta(days=300) < datetime.datetime.now():
            warning_string = f"""
            Markit API email attestation date is older than 10 months: Last attestation date {self.attestation_date}.
            The email used to authenticate with Markit will expire on {(attestataion_date_obj + datetime.timedelta(days=364)).date()}.
            Please visit https://rfr.ihsmarkit.com/, refresh the attestation in the user dropdown and update the MARKIT_EMAIL_ATTESTATION_DATE environmental variable
            in the .env with the new attestation date.
            """
            logger.warning(warning_string)

    @log_execution_time
    def fetch_xml(self, currency: str, date: str) -> str | None:
        """Fetch the RFR XML file for the specified currency and date."""

        self.check_attestation_date()

        if self.check_if_holiday(date, currency):
            logger.info(f"Skipping non-trading day: {date}")
            return None

        # Format the URL with the provided date and email
        url = f"https://rfr.ihsmarkit.com/InterestRates_{currency}_{date}.zip?email={self.email}"

        # Send a GET request to the URL
        logger.info(f"Downloading ZIP file from: {url}")
        response = requests.get(url)

        if response.status_code == 404 and f'{self.email} Specified file ({currency},{date}) is not available' == response.content.decode('utf-8'):
            logger.info(f"Specified file ({currency},{date}) is not available yet : {response.status_code} {response.content.decode('utf-8')}")
            return 'Not available'

        # Check if the request was successful
        if response.status_code != 200:
            raise Exception(f"Failed to download ZIP file: {response.status_code} {response.content}")

        # Create a BytesIO object from the response content
        zip_file = zipfile.ZipFile(io.BytesIO(response.content))

        # Extract the XML file from the ZIP archive
        xml_file_name = [name for name in zip_file.namelist() if name.endswith('.xml')][0]
        xml_content = zip_file.read(xml_file_name)

        # Decode the XML content (assuming it's UTF-8 encoded)
        logger.info(f"Downloaded XML file: {xml_file_name}")
        return xml_content.decode('utf-8')

    def check_if_holiday(self, date: str, currency: str) -> bool:
        """Check if the specified date is a holiday for the given currency."""

        # Parse the date string into a datetime object
        date_obj = pd.to_datetime(date, format='%Y%m%d')

        # Define the appropriate holiday calendar
        if currency == 'USD':
            holidays = load_calendar('NYM')
        elif currency == 'JPY':
            holidays = load_calendar('TYO')
        else:
            # For unsupported currencies, return True if the day is a weekend
            return date_obj.weekday() >= 5  # 5 = Saturday, 6 = Sunday

        # Check if the date is a holiday or weekend
        return (date_obj in holidays) or (date_obj.weekday() >= 5)
