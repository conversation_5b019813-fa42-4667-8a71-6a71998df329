import re
from typing import List, Optional
from pydantic import BaseModel
from enum import Enum
from datetime import datetime

EMPTY_FIELD = "PLACEHOLER_EMPTY_FIELD"


class PerSecurityRequestType(Enum):
    gethistory = "gethistory"
    getdata = "getdata"
    gettickhistory = "gettickhistory"


PerSecurityResponseOutExtn = {
    PerSecurityRequestType.gethistory.value: ".out.gz",
    PerSecurityRequestType.getdata.value: ".out",
    PerSecurityRequestType.gettickhistory.value: ".out.tar",
}

class RequestConfig(BaseModel):
    firm_name: Optional[str] = EMPTY_FIELD
    program_flag: Optional[str] = EMPTY_FIELD
    date_time_range: Optional[str] = EMPTY_FIELD
    date_range: Optional[str] = EMPTY_FIELD
    content_type: Optional[str] = EMPTY_FIELD
    event_type: Optional[str] = EMPTY_FIELD
    bar_interval: Optional[str] = EMPTY_FIELD
    sec_id: Optional[str] = EMPTY_FIELD
    output_format: Optional[str] = EMPTY_FIELD
    user_number: Optional[str] = EMPTY_FIELD
    bval_tier: Optional[str] = EMPTY_FIELD
    bval_snapshot: Optional[str] = EMPTY_FIELD
    bval_snapshot_date: Optional[str] = EMPTY_FIELD
    closing_values: Optional[str] = EMPTY_FIELD
    derived: Optional[str] = EMPTY_FIELD
    fields: List[str]
    securities: List[str]


_TEMPLATE = """START-OF-FILE
FIRMNAME={firm_name}
PROGRAMNAME={program_name}
PROGRAMFLAG={program_flag}
DATERANGE={date_range}
SECID={sec_id}
OUTPUTFORMAT={output_format}
USERNUMBER={user_number}
CLOSINGVALUES={closing_values}
DERIVED={derived}
BVALTIER={bval_tier}
BVALSNAPSHOT={bval_snapshot}
BVALSNAPSHOTDATE={bval_snapshot_date}
START-OF-FIELDS
{fields}
END-OF-FIELDS
START-OF-DATA
{securities}
END-OF-DATA
END-OF-FILE"""

_TICK_HIST_TEMPLATE = """START-OF-FILE
FIRMNAME={firm_name}
PROGRAMNAME={program_name}
PROGRAMFLAG={program_flag}
DATERANGE={date_range}
DATETIMERANGE={date_time_range}
CONTENTTYPE={content_type}
EVENT_TYPE={event_type}
BAR_INTERVAL={bar_interval}
OUTPUT_MEDIA_TYPE=application/x-tar;archive-media-type=gzip-csv
SECID={sec_id}
START-OF-DATA
{securities}
END-OF-DATA
END-OF-FILE"""


class RequestBuilder:
    def __init__(self, per_security_req_type: PerSecurityRequestType, target_path: str):
        self.target_path = target_path
        self.per_security_req_type = per_security_req_type

    def _format(self, request_config: RequestConfig):
        if request_config.fields:
            fields = "\n".join(request_config.fields)
        else:
            fields = None

        securities = "\n".join(request_config.securities)

        if self.per_security_req_type in [
            PerSecurityRequestType.gethistory,
            PerSecurityRequestType.getdata,
        ]:
            if not fields:
                raise ValueError("Fields are required for gethistory and getdata requests")
             
            template = _TEMPLATE
            filled = template.format(
                firm_name=request_config.firm_name,
                program_name=self.per_security_req_type.value,
                program_flag=request_config.program_flag,
                output_format=request_config.output_format,
                user_number=request_config.user_number,
                date_range=request_config.date_range,
                sec_id=request_config.sec_id,
                bval_tier=request_config.bval_tier,
                bval_snapshot=request_config.bval_snapshot,
                bval_snapshot_date=request_config.bval_snapshot_date,
                closing_values=request_config.closing_values,
                derived=request_config.derived,
                fields=fields,
                securities=securities,
            )
        elif self.per_security_req_type == PerSecurityRequestType.gettickhistory:
            template = _TICK_HIST_TEMPLATE
            filled = template.format(
                firm_name=request_config.firm_name,
                program_flag=request_config.program_flag,
                program_name=self.per_security_req_type.value,
                date_range=request_config.date_range,
                date_time_range=request_config.date_time_range,
                content_type=request_config.content_type,
                event_type=request_config.event_type,
                bar_interval=request_config.bar_interval,
                sec_id=request_config.sec_id,
                securities=securities,
            )
        else:
            raise ValueError(f"Unsupported request type: {self.per_security_req_type}")

        clean = re.sub(rf"\n.*={EMPTY_FIELD}", "", filled)
        return clean

    def _write(self, result, target_file):
        with open(target_file, "w") as f:
            f.write(result)

    def build(self, request_name: str, request_config: RequestConfig, append_datetimestamp: bool = False):
        if append_datetimestamp:
            request_name = f"{request_name}_{datetime.now().strftime('%y%m%d%H%M')}"
        
        if len(f"{request_name}.req") > 25:
            print(f"{request_name}.req")
            raise ValueError("Request file name cannot be more than 25 characters!")

        target_file = f"{self.target_path}/{request_name}.req"
        result = self._format(request_config)
        self._write(result, target_file)
        return target_file


if __name__ == "__main__":
    request_config = RequestConfig(
        firm_name="dl47544",
        program_flag="adhoc",
        sec_id="TICKER",
        output_format="bulklist",
        # date_range="20210101|20210102",
        securities=["AAPL US EQUITY", "MSFT US EQUITY"],
        fields=["PX_LAST"],
    )

    request_builder = RequestBuilder(PerSecurityRequestType.getdata, ".")
    print(request_builder.build("hist_1234", request_config))
