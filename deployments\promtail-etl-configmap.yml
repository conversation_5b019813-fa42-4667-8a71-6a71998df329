apiVersion: v1
kind: ConfigMap
metadata:
  name: promtail-config
data:
  promtail.yaml: |
    server:
      http_listen_port: 9080
     
    positions:
      filename: /tmp/positions.yaml

    clients:
      - url: http://internal-k8s-default-lokiingr-05bd7f4ed4-454744129.us-east-1.elb.amazonaws.com/loki/api/v1/push

    scrape_configs:
      - job_name: kubernetes
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name

      - job_name: integrations/apache-airflow
        static_configs:
          - targets: [localhost]
            labels:
              job: integrations/apache-airflow
              instance: 'jg-tech1prod-airflow'
              __path__: /logs/scheduler/latest/*.py.log

      - job_name: integrations/apache-airflow-dag-id
        static_configs:
          - targets: [localhost]
            labels:
              job: integrations/apache-airflow-dag-id
              instance: 'jg-tech1prod-airflow_dag_id'
              __path__: '/logs/dag_id=jg-etl-*/**/**/*.log'
