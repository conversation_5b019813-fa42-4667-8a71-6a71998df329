raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/ice/mft/1.0/"  ## Location of Raw Files
  s3_bucket: "jg-data-dp-vendor-data" ## S3 with Snowflake Acess
  s3_prefix: "ice/mft" ## Internal S3path to files
  # include_prefix: true

  structure: '[
    "ICE_Futures_Global_Oil/**/icefutures_EODhistory_S2FOil_futures_$DATE$.csv.gz",
    "ICE_Futures_Global_Oil/**/icefutures_EODhistory_S2FOil_options_$DATE$.csv.gz",
    "ICE_Futures_US-Gas/**/icefutures_EODhistory_S2FGas_futures_$DATE$.csv.gz",
    "ICE_Futures_US-Gas/**/icefutures_EODhistory_S2FGas_options_$DATE$.csv.gz",
    "ICE_Futures_US-Power/**/icefutures_EODhistory_S2FPower_futures_$DATE$.csv.gz",
    "ICE_Futures_US-Power/**/icefutures_EODhistory_S2FPower_options_$DATE$.csv.gz",
    
    "ICE_Endex/**/icefutures_EODhistory_endex_futures_$DATE$.csv.gz",
    "ICE_Endex/**/icefutures_EODhistory_endex_options_$DATE$.csv.gz",
    "ICE_Futures_EU-Commodities/**/icefutures_EODhistory_EUcmdty_futures_$DATE$.csv.gz",
    "ICE_Futures_EU-Commodities/**/icefutures_EODhistory_EUcmdty_options_$DATE$.csv.gz",
    "ICE_Futures_LNG/**/icefutures_EODhistory_LNG_futures_$DATE$.csv.gz",
    "ICE_Futures_LNG/**/icefutures_EODhistory_LNG_options_$DATE$.csv.gz"
  ]'


snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "ICE_MFT"

  table_map:
    FUTURES_RAW:
      pattern: ".*icefutures_EODhistory_.*futures_\\\\d{4,6}(Q[1-4])?\\\\.csv\\\\.gz$" ## Need to be a regex format
      col_num: 18
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/mft/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT"

    OPTIONS_RAW:
      pattern: ".*icefutures_EODhistory_.*options_\\\\d{4,6}(Q[1-4])?\\\\.csv\\\\.gz$" ## Need to be a regex format
      col_num: 22
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/mft/" ##<stage name>/<stage path>
      file_format: "FF_ICE_MFT"