import asyncio, os, time, json
import pandas as pd
import logging
from spiderrock.spiderrock_api import SpiderRock<PERSON>lient, SpiderRockMessageType
from utils.postgres.adaptor import PostgresAdaptor
from utils.date_utils import get_now
from datetime import timedelta

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

base_path = os.path.dirname(__file__)
with open(f"{base_path}/conf/spiderrock_api.json", "r") as f:
    config = json.load(f)

def get_ticker_stripe_mapping():
    loader = PostgresAdaptor(
        host=config["pg_host"],
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df_securities = loader.execute_query(
        """ 
            select sc.compound_stripe, rc.ticker 
            from eqvol.ticker_request_config rc join 
            eqvol.sr_stripe_config sc on rc.stripe = sc.stripe 
            where rc.is_sr_opt_quote_enabled = true;
        """
    )

    return df_securities.groupby("compound_stripe")["ticker"].apply(list).to_dict()

async def invoke_api_async(spiderrock_client, stripe, tickers, results, api_req_time):
    start_time = time.time()
    df = await asyncio.to_thread(spiderrock_client.invoke_api, tickers=tickers, api_req_time=api_req_time)    
    results.append(df)
    end_time = time.time()
    logger.info(f"Stripe: {stripe} - Time taken: {end_time - start_time:.4f} secs")

async def main():
    start_time = time.time()
    ticker_conf = get_ticker_stripe_mapping()
    results = [] 
    api_req_time = get_now('UTC').replace(second=0, microsecond=0)
    spiderrock_client = SpiderRockClient(config, SpiderRockMessageType.OPTION_NBBO_QUOTE)
    tasks = [invoke_api_async(spiderrock_client, stripe, tickers, results, api_req_time) for stripe, tickers in ticker_conf.items()]    
    await asyncio.gather(*tasks)
    final_df = pd.concat(results, ignore_index=True)
    logger.info(f"Final DataFrame Shape: {final_df.shape}")
    spiderrock_client.load_csv_to_db(final_df, 'fe_risk.eqvol.sr_option_quotes_exp')
    end_time = time.time()
    logger.info(f"Total Time taken: {end_time - start_time:.4f} secs")

if __name__ == "__main__":
    asyncio.run(main()) 
    
    