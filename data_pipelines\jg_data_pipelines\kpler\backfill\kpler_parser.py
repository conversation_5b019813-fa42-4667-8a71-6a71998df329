from utils.snowflake.bulk_loader import SnowflakeBulkLoader
import time
import json
from datetime import datetime
from collections import defaultdict
import pandas as pd
import polars as pl
from pathlib import Path
from typing import Optional
import re
import os
import logging
import os
import uuid

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

_KPLER_AVAILABILITY_UNITS_RAW = "/jfs/tech1/apps/rawdata/kpler_power/availability_units_yearly/"
_KPLER_AVAILABILITY_UNITS_PROCESSED = "/jfs/tech1/apps/rawdata/kpler_power/availability_units_yearly_processed_jcrubioa/"

_BAD_JSON_REGEX = [r".*/DE/fossil brown coal/.*"]

def get_monthly_bins(as_of_dates: list[str]) -> dict[str, list[str]]:
    monthly_bins = defaultdict(list)

    for ts in as_of_dates:
        ts_fixed = ts[:-3] + ts[-2:]
        dt = datetime.strptime(ts_fixed, "%Y-%m-%dT%H:%M:%S%z")
        month_key = dt.strftime("%Y_%m")
        monthly_bins[month_key].append(ts)
    
    return monthly_bins

def extract_data_for_as_of_ts(as_of_ts: str, raw_data_dir: str):
    as_of_ts_path = os.path.join(raw_data_dir, as_of_ts)
    files_cnt = 0
    df_list = []
    for json_path in Path(as_of_ts_path).rglob('*.json'):
        for regex in _BAD_JSON_REGEX:
            skip_file = False
            if re.match(regex, str(json_path)):
                skip_file = True
                break
        
        if skip_file:
            logger.warning(f"Skipping bad JSON file: {json_path}")
            continue
        
        logger.debug(f"Processing file: {json_path}")
        flattened_json = flatten_json(json_path)

        if flattened_json is not None:
            logger.debug(f"Flattened JSON Data for {json_path}")
            df_list.append(flattened_json)
        else:
            logger.warning(f"No data found in {json_path}")
            
        files_cnt += 1
    data_result = None
    if df_list:
        data_result = pl.concat(df_list)
    return files_cnt, data_result

def examine_json(data: dict):
    json_keys = data.keys()

    if "detail" in json_keys:
        if type(data["detail"]) == str:
            detail_value = data["detail"].lower()
            if "no production units for" in detail_value:
                return [], [], [], True

    expected_elements = {
        "provider": str,
        "location": str,
        "timezone": str,
        "metadata": dict,
        "data": dict,
        "index": list,
    }

    missing_keys = set(expected_elements.keys()) - set(json_keys)
    extra_keys = set(json_keys) - set(expected_elements.keys())

    mismatched_data_types = []
    
    for key, expected_type in expected_elements.items():
        if key in json_keys:
            if type(data[key]) != expected_type:
                mismatched_data_types.append(key)
            if key == "data":
                data_val = data[key]
                data_val_keys = data_val.keys()
                lst_data_val_keys = list(data_val_keys)
                if len(lst_data_val_keys) == 1 and lst_data_val_keys[0] == "productions":
                    data_val_productions = data_val["productions"]
                    
                    if type(data_val_productions) != dict:
                        mismatched_data_types.append(f"{key}.productions")
                        continue
                    
                    for plant, plant_value in data_val_productions.items():
                        plant_value_keys = plant_value.keys()
                        if set(plant_value_keys) - set(["data", "generations"]):
                            mismatched_data_types.append(f"{key}.productions.{plant}.values")
                else:
                    mismatched_data_types.append(f"{key}.productions")
            
    return list(missing_keys), list(extra_keys), mismatched_data_types, False

def extract_as_of_date(filename):
    timestamp_str = filename.split('_')[0].replace('\\:', ':')
    return datetime.fromisoformat(timestamp_str)

def flatten_json(file_path) -> list[dict]:
    with open(file_path, 'r') as f:
        full_dict = json.load(f)

    full_df = []

    (missing_keys, extra_keys, mismatched_types, no_production_units) = examine_json(full_dict)
    if len(missing_keys) > 0 or len(extra_keys) > 0 or len(mismatched_types) > 0:
        logger.warning(f"Unexpected file format: {file_path} - Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_types}")
        return None
    
    elif no_production_units:
        logger.info(f"No production units for {file_path}")
        return None

    file_name = os.path.basename(file_path)
    as_of_date = extract_as_of_date(file_name)
    
    provider = full_dict['provider']
    location = full_dict['location']
    timezone = full_dict['timezone']

    metadata = full_dict['metadata']
    data = full_dict["data"]["productions"]
    index_dates = full_dict["index"]
    for key, value in metadata.items():
        plant_id = key
        plant_metadata = value
        if plant_id in data.keys():
            logger.debug(f"Productions Found for {plant_id}")
            plant_data = data[plant_id]
            n = len(index_dates)

            if "data" in plant_data.keys():
                logger.debug(f"Data Found for {plant_id}")
                production_data = plant_data["data"]
                assert len(production_data) == len(index_dates), f"Length of index dates and production data are not equal for {plant_id} in {file_name}"

                df = pl.DataFrame({
                    "timestamp": index_dates,
                    "availability_amount": production_data
                })
                df = df.with_columns(
                    timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                    type = pl.lit('production'),
                    as_of = pl.lit(as_of_date).dt.cast_time_unit('ms'),
                    provider = pl.lit(provider),
                    country = pl.lit(location),
                    unit = pl.lit(plant_id),
                    unit_name = pl.lit(plant_metadata.get('name', '')),
                    generation_code = pl.lit(''),
                    production_code = pl.lit(plant_metadata.get('production_code', plant_id)),
                    asset_type = pl.lit(plant_metadata.get('asset_type', '')),
                    fuel_type = pl.lit(plant_metadata.get('fuel_type', '')),
                    timezone = pl.lit(timezone),
                    json_file = pl.lit(file_name),
                )
                # Filter missmatched ts
                df = df.with_columns(
                    timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                    as_of_aux = pl.col('as_of').dt.date(),
                    timestamp_aux = pl.col('timestamp').dt.date(),
                )
                df = df.filter(
                    pl.col("timestamp_aux") >= pl.col("as_of_aux")
                )
                df = df.drop(["as_of_aux", "timestamp_aux"])
                df = df.with_columns(pl.col('production_code').fill_null(pl.lit('')))
                full_df.append(df)
                

            if "generations" in plant_data.keys():
                logger.debug(f"Generations Found for {plant_id}")
                generations_data_dict = plant_data["generations"]
                for power_gen_id in generations_data_dict.keys():
                    power_generation_data = generations_data_dict[power_gen_id]
                    power_gen_metadata = metadata[power_gen_id]
                    assert len(power_generation_data) == len(index_dates), f"Length of index dates and generation data are not equal for {plant_id} in {file_name}"
                    
                    df = pl.DataFrame({
                        "timestamp": index_dates,
                        "availability_amount": power_generation_data
                    })
                    df = df.with_columns(
                        timestamp = pl.col('timestamp').str.to_datetime("%Y-%m-%dT%H:%M").dt.replace_time_zone("UTC"),
                        type = pl.lit('generation'),
                        as_of = pl.lit(as_of_date).dt.cast_time_unit('ms'),
                        provider = pl.lit(provider),
                        country = pl.lit(location),
                        unit = pl.lit(power_gen_id),
                        unit_name = pl.lit(power_gen_metadata.get('name', '')),
                        generation_code = pl.lit(power_gen_metadata.get('production_code', '')),
                        production_code = pl.lit(plant_metadata.get('production_code', plant_id)),
                        asset_type = pl.lit(power_gen_metadata.get('asset_type', '')),
                        fuel_type = pl.lit(power_gen_metadata.get('fuel_type', '')),
                        timezone = pl.lit(timezone),
                        json_file = pl.lit(file_name),
                    )
                    # Filter missmatched ts
                    df = df.with_columns(
                        timestamp = pl.col('timestamp').dt.cast_time_unit('ms'),
                        as_of_aux = pl.col('as_of').dt.date(),
                        timestamp_aux = pl.col('timestamp').dt.date(),
                    )
                    df = df.filter(
                        pl.col("timestamp_aux") >= pl.col("as_of_aux")
                    )
                    df = df.drop(["as_of_aux", "timestamp_aux"])
                    df = df.with_columns(pl.col('production_code').fill_null(pl.lit('')))
                    full_df.append(df)
        else:
            logger.debug(f"No data section for plant_id: {plant_id}")
    if full_df:
        return pl.concat(full_df, how="vertical_relaxed")
    else:
        return None

if __name__  ==  '__main__':
    with open(f"{_KPLER_AVAILABILITY_UNITS_PROCESSED}availability_units_yearly.txt", "r") as file:
        as_of_dates = [line.strip().replace("availability_units_yearly/", "") for line in file]
    
    database = 'KPLER'
    schema_name = 'KPLER_POWER'
    target_table = 'AVAILABILITY_PER_UNITS_FULL'
    stage_name = 'POWER_AVAILABILITY'
    stage_path = uuid.uuid4().hex
    monthly_bins = get_monthly_bins(as_of_dates)
    total_files = 0
    sf_adaptor = SnowflakeBulkLoader(
        database=database, 
        schema=schema_name,
        warehouse="BBG_DLPLUS_WH", 
        role="FR_DATA_PLATFORM")

    for month_key, month_values in monthly_bins.items():
        
        # if month_key not in ["2025_01", "2025_02"]:
        #     continue

        monthly_total_files = 0
        for as_of_ts in month_values:
            overall_start = time.time()
            #file_name = f"{as_of_ts[:10].replace('-', '_')}"
            parquet_file = f"{_KPLER_AVAILABILITY_UNITS_PROCESSED}{as_of_ts}.parquet"
            if os.path.exists(parquet_file):
                logger.info(f"File already exists: {parquet_file}")
                continue

            all_dfs = []
            start_time = time.time()
            (files_for_as_of, as_of_ts_data) = extract_data_for_as_of_ts(as_of_ts, _KPLER_AVAILABILITY_UNITS_RAW)
            end_time = time.time()

            logger.info(f"Time taken to read JSONs {as_of_ts}: {end_time - start_time:.4f} secs, Number of files: {files_for_as_of}")

            start_time = time.time()

            end_time = time.time()
            logger.info(f"Time taken to flatten as df for {as_of_ts}: {end_time - start_time:.4f} secs")

            start_time = time.time()

            if as_of_ts_data is not None:
                file_path = f"{_KPLER_AVAILABILITY_UNITS_PROCESSED}{as_of_ts}.parquet"
                as_of_ts_data.write_parquet(file_path)

                # Upload file to stage
                sf_adaptor.put_file(file_path, stage_name, stage_path=stage_path)
            end_time = time.time()
            logger.info(f"Time taken to save df for {as_of_ts}: {end_time - start_time:.4f} secs")
            monthly_total_files += files_for_as_of
            overall_end = time.time()
            logger.info(f"Total time taken {as_of_ts}: {overall_end - overall_start:.4f} secs")

        
        total_files += monthly_total_files
        logger.info(f"Processed for Month: {month_key} - {len(month_values)} as_of_ts, Monthly Total: {monthly_total_files}, Running Total: {total_files} JSON files")
    
    sf_adaptor.load_generic(
                    target_table, 
                    stage_name,
                    stage_path,
                    "(TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE'"
                )
    sf_adaptor.cleanup_stg(stage_name, stage_path)
    print(f"Total JSON Files processed: {total_files}")