from meteologica_processor import MeteologicaProcessor, HISTORICAL
import uuid
import os 
import time
import functools
import multiprocessing
import logging


stage_path = uuid.uuid4().hex



def process_file(zipfile):
    historical_raw_path = os.getenv('HISTORICAL_RAW_PATH')
    sf_stage_name =  os.getenv('METEOLOGICA_STAGE')
    meteologica = MeteologicaProcessor(HISTORICAL)
    meteologica._process_zip_file(historical_raw_path, stage_path, zipfile)
    

if __name__ == "__main__":
    historical_raw_path = os.getenv('HISTORICAL_RAW_PATH')
    sf_stage_name =  os.getenv('METEOLOGICA_STAGE')
    n_processes = 10
    logging.info(f"Processing folder {historical_raw_path} with {n_processes} processes")
    logging.info(f"Processing stage path: {stage_path}")
    files = os.listdir(historical_raw_path)
    sorted_files = sorted(
        files, 
        key=lambda x: (int(x.split('_')[1]), int(x.split('_')[2].replace('.zip', ''))), 
        reverse=True)
    # Process each zipfile into a parquet file to the stage
    init = time.time()
    with multiprocessing.Pool(processes=n_processes) as pool:
        results = pool.map(process_file, sorted_files)
    end = time.time()
    logging.info(f"All Zipfiles uploaded to Snowflake stage. Took: {end - init}")
    init = time.time()
    meteologica = MeteologicaProcessor(HISTORICAL)
    meteologica._run_copy_into(sf_stage_name, stage_path)
    end = time.time()
    logging.info(f"All Files inserted into Snowflake tables. Took: {end - init}")
    logging.info(f"Cleaning up stage @{sf_stage_name}/{stage_path}")
    meteologica.sf_adaptor.cleanup_stg(sf_stage_name, stage_path)

    