#!/bin/sh

sudo modprobe fuse
sudo /usr/bin/cwiqfs -f /jfs -o cache_size_mb=10000 -o allow_other -o force_allow_non_admin_allow_other -o cwiqfs_yaml=/etc/cwiq/cwiqfs/config.yaml -o log_file=/var/log/cwiq/cwiqfs/cwiqfs_v2.log -o tmpdir=/var/tmp/cache -o audit_log_file=/var/log/cwiq/cwiqfs/audit_cwiqfs_v2.log &
sudo export SPARK_DIST_CLASSPATH="/jfs/tech1/apps/datait/source/jars/iceberg-spark-runtime.jar"
sudo export CONFIG_PATH="/jfs/tech1/apps/datait/source"

sleep 10

chown -R airflow: /home/<USER>/spark/app /home/<USER>/set_auth.py /home/<USER>/init.sh /home/<USER>/airflow.cfg

if [ ! -e /home/<USER>/.entrypoint_check ]
then
  touch /home/<USER>/.entrypoint_check

  if [ "$DB_TYPE" = "mysql" ]; then
      echo "MySQL db detected - updating airflow.cfg file"
      sed -i -e 's/<mysql_db_connection>/sql_alchemy_conn = mysql:\/\/'$DB_USER':'$DB_PWD'@'$DB_HOST':'$DB_PORT'\/'$DB_NAME'/g' /home/<USER>/airflow.cfg
  else
      echo "Postgres db detected - updating airflow.cfg file"
      sed -i -e 's/<mysql_db_connection>/sql_alchemy_conn = postgresql+psycopg2:\/\/'$DB_USER':'$DB_PWD'@'$DB_HOST':'$DB_PORT'\/'$DB_NAME'/g' /home/<USER>/airflow.cfg
  fi

  echo "Database setup completed!"
fi

if [ "$IS_WEB" = "yes" ]; then
    echo "Starting Airflow Webserver"
    airflow db init
    airflow users create -u "$AIRFLOW_USERNAME" -p "$AIRFLOW_PASSWORD" -r Admin -e <EMAIL> -f Admin -l User
    airflow connections add 'jg_spark' --conn-type 'spark' --conn-host 'spark://spark:7077' --conn-extra '{"queue": "root.default"}'
    echo "airflow ALL=(ALL) NOPASSWD:ALL" | sudo tee /etc/sudoers.d/airflow
    exec airflow webserver
elif [ "$IS_SCHEDULER" = "yes" ]; then
    echo "Starting Airflow Scheduler"
    exec airflow scheduler
elif [ "$IS_WORKER" = "yes" ]; then
    echo "Starting Airflow Worker"
    exec airflow celery worker
else
    echo "No valid component type specified, exiting..."
    exit 1
fi
