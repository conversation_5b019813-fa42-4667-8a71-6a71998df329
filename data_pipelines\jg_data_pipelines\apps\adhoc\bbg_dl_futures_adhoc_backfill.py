import os
import gzip
import shutil
import tempfile

from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from bloomberg.utils import get_bbg_full_ticker_for_futures

from bloomberg.per_security.parser import BloombergParser
from utils.date_utils import get_now, get_n_bday_yyyymmdd


import pandas as pd

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    df_securities = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """select ref.id_bb_global As ID_BB_GLOBAL
            from VW_FUTURE_REF ref join 
            COMMON.PUBLIC.TIMESTAMP_DAILY td on td.date >= ref.fut_first_trade_dt and td.date <= ref.last_tradeable_dt left outer join 
            VW_FUTURE_PX px on ref.id_bb_global = px.id_bb_global and px.price_date = td.date
            WHERE FUTURE_ROOT = 'TU'  and 
            td.DAY_NAME not in ('SUNDAY', 'SATURDAY') and 
            px.px_settle is null and 
            td.date <= '2025-02-14'
            group by ref.bbg_full_ticker, ref.id_bb_global, ref.fut_contract_dt
            HAVING COUNT(1) > 15;
        """,
    )

    securities = df_securities["ID_BB_GLOBAL"].tolist()

    per_sec_req_type = PerSecurityRequestType.gethistory
    batch_name = "dp_futpx_adhoc_tu_bf"

    from_date = get_n_bday_yyyymmdd(6550)
    to_date = get_n_bday_yyyymmdd(0)

    request_dict = {
            "firm_name": "dl47544",
            "program_flag": "adhoc",
            "date_range": f"{from_date}|{to_date}",
            "sec_id": "BB_GLOBAL",
            "fields": [
                "PX_OPEN",
                "PX_HIGH",
                "PX_LOW",
                "PX_SETTLE",
                "PX_VOLUME",
                "OPEN_INT",
            ],
            "securities": securities,
        }
    
    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/futures_px_eod/adhoc/"
    fut_px_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run()

    fut_px_file_name = os.path.basename(fut_px_file_path)
    df_fut_px = pd.DataFrame()
    with tempfile.TemporaryDirectory() as temp_dir:
        tmp_file_path = f"{temp_dir}/{fut_px_file_name.replace('.gz', '')}"
        with gzip.open(fut_px_file_path, 'rb') as f_in:
            with open(tmp_file_path, 'wb') as f_out:
                shutil.copyfileobj(f_in, f_out)

        parser = BloombergParser(tmp_file_path, sep='|', skipinitialspace=True, on_bad_lines="error") 
        df_fut_px = parser.parse_data()
    
    if df_fut_px.shape[0] == 0:
        raise ValueError("No data returned from Bloomberg")

    df_fut_px["Date"] = pd.to_datetime(df_fut_px["Date"], format="%m/%d/%Y", errors="coerce")
    df_fut_px["Value"] = pd.to_numeric(df_fut_px["Value"], errors="coerce")

    df_fut_px_wide = df_fut_px.pivot(index=['Ticker', 'Date'], columns='Field', values='Value').reset_index()
    df_fut_px_wide.rename(columns= {"Ticker": "ID_BB_GLOBAL", "Date": "PRICE_DATE"}, inplace=True)
    
    df_fut_px_wide["UPDATE_SOURCE"] = 3
    df_fut_px_wide["LAST_UPDATED"] = get_now()
    df_fut_px_wide["UPDATED_BY"] = "adhoc_backfill"

    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_FUTURES",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
    )

    ret_val = adaptor.upsert(df_fut_px_wide, "FUTURE_PX_DL_ADHOC_BACKFILL", ["ID_BB_GLOBAL", "PRICE_DATE"])
    print(ret_val)
