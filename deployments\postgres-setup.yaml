apiVersion: v1
kind: ConfigMap
metadata:
  name: postgres-init-scripts
data:
  init.sql: |-
    CREATE USER airflow WITH PASSWORD '${AIRFLOW_POSTGRES_PASSWORD}';
    CREATE DATABASE airflow;
    ALTER DATABASE airflow OWNER TO airflow;
    GRANT ALL PRIVILEGES ON DATABASE airflow TO airflow;
  trinorestcatalog.sql: |-
    CREATE USER admin WITH PASSWORD '${TRINO_CATALOG_JDBC_PASSWORD}';
    CREATE DATABASE demo_catalog;
    ALTER DATABASE demo_catalog OWNER TO admin;
    GRANT ALL PRIVILEGES ON DATABASE demo_catalog TO admin;
    CREATE TABLE iceberg_namespace_properties(catalog_name varchar(255) NOT NULL, namespace varchar(255) NOT NULL, property_key varchar(255) NOT NULL,  property_value varchar(1000), PRIMARY KEY(catalog_name,namespace,property_key));
    INSERT INTO iceberg_namespace_properties (catalog_name, namespace, property_key, property_value) VALUES ('rest_backend', 'jg_schema', 'exists', 'true');
    CREATE TABLE iceberg_tables( catalog_name varchar(255) NOT NULL, table_namespace varchar(255) NOT NULL, table_name varchar(255) NOT NULL, metadata_location varchar(1000), previous_metadata_location varchar(1000), iceberg_type varchar(5), PRIMARY KEY(catalog_name,table_namespace,table_name));
    INSERT INTO iceberg_tables (catalog_name, table_namespace, table_name, metadata_location, previous_metadata_location, iceberg_type) VALUES ('rest_backend', 'jg_schema', 'pm_master', 's3a://jg-fo-tool/jg/jg_schema/pm_master-197a3a1fd16c466087f423c2434a982a/metadata/00072-c4560624-61b5-45c9-bc8d-a2ef197fefd7.metadata.json', NULL, 'TABLE'), ('rest_backend', 'jg_schema', 'guideline_master', 's3a://jg-fo-tool/jg/jg_schema/guideline_master-42ac32e4a8284e9fa1c8b382bb9338f2/metadata/00025-f3e6e901-f58b-40e6-8667-4357c34d0198.metadata.json', NULL, 'TABLE'), ('rest_backend', 'jg_schema', 'pm_guideline_mapper', 's3a://jg-fo-tool/jg/jg_schema/pm_guideline_mapper-1e31dae6fc72487cafc7138cea244970/metadata/00002-c0a2229a-9a0a-4488-8235-328ddb935e7b.metadata.json', NULL, 'TABLE'), ('rest_backend', 'jg_schema', 'pm_guideline_override', 's3a://jg-fo-tool/jg/jg_schema/pm_guideline_override-78e6e08b117a4eec94a54e6fd85b7b14/metadata/00000-986f2c96-63d7-461e-b964-649d0efb6d74.metadata.json', NULL, 'TABLE'), ('rest_backend', 'jg_schema', 'breach', 's3a://jg-fo-tool/jg/jg_schema/breach-0ddd68a6aed04508b4ed10927dc2c9a8/metadata/00000-ac5a5cb3-336c-45f2-8fb9-0a5b1fe18361.metadata.json', NULL, 'TABLE');


---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  labels:
    db: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      db: postgres
  template:
    metadata:
      labels:
        db: postgres
    spec:
      containers:
      - name: postgres-db
        image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-postgres-${ENV}:latest
        env:
        - name: POSTGRES_PASSWORD
          value: ${AIRFLOW_POSTGRES_PASSWORD}
        ports:
        - name: postgres-port
          containerPort: 5432
        volumeMounts:
        - mountPath: /var/lib/postgresql
          name: postgres-vol
        - mountPath: /docker-entrypoint-initdb.d
          name: init-scripts
      volumes:
      - name: postgres-vol
        persistentVolumeClaim:
          claimName: postgres-pvc
      - name: init-scripts
        configMap:
          name: postgres-init-scripts


---

apiVersion: v1
kind: Service
metadata:
  name: service-postgres
spec:
  ports:
  - port: 5432
    targetPort: postgres-port
    protocol: TCP
  selector:
    db: postgres
  type: ClusterIP
