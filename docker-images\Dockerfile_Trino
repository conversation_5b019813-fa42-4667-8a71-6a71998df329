FROM trinodb/trino:448
ARG USERNAME
ARG LOGIN_KEY
ENV USERNAME=$USERNAME
ENV LOGIN_KEY=$LOGIN_KEY
USER root

COPY docker-images/trino/run-trino-with-mount.sh /usr/lib/trino/run-trino-with-mount.sh
RUN chmod +x /usr/lib/trino/run-trino-with-mount.sh

RUN microdnf install -y dos2unix
RUN microdnf install -y less wget vim httpd-tools procps gettext kmod
RUN microdnf install -y fuse3 findutils
#RUN microdnf install -y fuse
#RUN microdnf install -y sysstemd
RUN microdnf install -y openssh-clients
COPY /docker-images/cwiqfs/fuse-libs-2.9.2-11.el7.x86_64.rpm  /tmp
COPY /docker-images/cwiqfs/fuse-sshfs-2.10-1.el7.x86_64.rpm /tmp

COPY docker-images/cwiqfs/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm /tmp
#COPY docker-images/cwiqfs/cwiq.service.j2 
RUN  rpm -i /tmp/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm
RUN ln -s /etc/ssl/certs/ca-bundle.crt /etc/ssl/certs/ca-certificates.crt
COPY docker-images/cwiqfs/cwiqfs.yaml /etc/cwiq/cwiqfs/config_temp.yaml
RUN envsubst '${USERNAME},${LOGIN_KEY}' < /etc/cwiq/cwiqfs/config_temp.yaml > /etc/cwiq/cwiqfs/config.yaml
RUN sed -i -e's/# user_allow_other/user_allow_other/g' /etc/fuse.conf
RUN mkdir /jfs
RUN useradd jsvc-tech1
RUN dos2unix /usr/lib/trino/run-trino-with-mount.sh

CMD ["/usr/lib/trino/run-trino-with-mount.sh"]
#CMD ["sleep","20000"]
HEALTHCHECK --interval=10s --timeout=5s --start-period=10s CMD /usr/lib/trino/bin/health-check
#RUN mknod -m 666 /dev/fuse c 10 229
#RUN chmod 666 /dev/fuse
#USER jsvc-tech1
#groupadd fuse
#chmod g+rw /dev/fuse
#chgrp fuse /dev/fuse
#usermod -a -G fuse root
#chgrp staff /u      Change the group of /u to "staff".
#sshfs jsvc-tech1@localhost: -o rw,nosuid,nodev,allow_other,auto_unmount,subtype=cwiqfs -- /jfs
#fusermount3 -o rw,nosuid,nodev,allow_other,auto_unmount,subtype=cwiqfs -- /jfs
#
