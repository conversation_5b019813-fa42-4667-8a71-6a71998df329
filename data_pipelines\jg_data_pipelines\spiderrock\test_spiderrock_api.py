import pytest
import pytz
from datetime import datetime
from spiderrock.spiderrock_api import is_time_in_range


def test_time_within_range_same_day():
    """Test that a time within the range returns True."""
    # 10:30 AM is between 9:00 AM and 11:00 AM
    timestamp = datetime(2024, 1, 15, 10, 30, 45, 123456)  # Including seconds/microseconds
    assert is_time_in_range(timestamp, (9, 0), (11, 0)) is True


def test_time_before_range():
    """Test that a time before the range returns False."""
    # 8:30 AM is before 9:00 AM - 11:00 AM
    timestamp = datetime(2024, 1, 15, 8, 30, 0)
    assert is_time_in_range(timestamp, (9, 0), (11, 0)) is False


def test_time_after_range():
    """Test that a time after the range returns False."""
    # 12:30 PM is after 9:00 AM - 11:00 AM
    timestamp = datetime(2024, 1, 15, 12, 30, 0)
    assert is_time_in_range(timestamp, (9, 0), (11, 0)) is <PERSON>alse


def test_time_exactly_at_start():
    """Test that a time exactly at start boundary returns True (inclusive)."""
    timestamp = datetime(2024, 1, 15, 9, 0, 0)
    assert is_time_in_range(timestamp, (9, 0), (11, 0)) is True


def test_time_exactly_at_end():
    """Test that a time exactly at end boundary returns True (inclusive)."""
    timestamp = datetime(2024, 1, 15, 11, 0, 0)
    assert is_time_in_range(timestamp, (9, 0), (11, 0)) is True


def test_same_hour_range():
    """Test range within the same hour."""
    # 10:15 AM is between 10:00 AM and 10:30 AM
    timestamp = datetime(2024, 1, 15, 10, 15, 0)
    assert is_time_in_range(timestamp, (10, 0), (10, 30)) is True


def test_same_hour_outside_range():
    """Test time outside range within the same hour."""
    # 10:45 AM is after 10:00 AM - 10:30 AM
    timestamp = datetime(2024, 1, 15, 10, 45, 0)
    assert is_time_in_range(timestamp, (10, 0), (10, 30)) is False


def test_minute_precision_ignores_seconds():
    """Test that seconds and microseconds are ignored."""
    # 10:15:59.999999 should be treated as 10:15:00
    timestamp = datetime(2024, 1, 15, 10, 15, 59, 999999)
    assert is_time_in_range(timestamp, (10, 15), (10, 16)) is True


def test_different_dates_same_time():
    """Test that different dates work correctly with same time logic."""
    timestamp1 = datetime(2024, 1, 15, 10, 30, 0)
    timestamp2 = datetime(2024, 12, 25, 10, 30, 0)
    
    # Both should be within 9:00 AM - 11:00 AM range
    assert is_time_in_range(timestamp1, (9, 0), (11, 0)) is True
    assert is_time_in_range(timestamp2, (9, 0), (11, 0)) is True


def test_market_opening_window():
    """Test typical market opening window (9:00 AM - 9:30 AM)."""
    # Test times within the market opening window
    timestamps_in_range = [
        datetime(2024, 1, 15, 9, 0, 0),   # Exactly at start
        datetime(2024, 1, 15, 9, 15, 0),  # Middle
        datetime(2024, 1, 15, 9, 29, 59), # Just before end (should be treated as 9:29)
    ]
    
    for ts in timestamps_in_range:
        assert is_time_in_range(ts, (9, 0), (9, 29)) is True, f"Failed for {ts}"

    # Test times outside the market opening window
    timestamps_outside_range = [
        datetime(2024, 1, 15, 8, 59, 0),  # Just before start
        datetime(2024, 1, 15, 9, 30, 0),  # Just after end
    ]
    
    for ts in timestamps_outside_range:
        assert is_time_in_range(ts, (9, 0), (9, 29)) is False, f"Failed for {ts}"


def test_late_night_range():
    """Test late night time ranges."""
    # 11:30 PM to 11:45 PM
    timestamp_in = datetime(2024, 1, 15, 23, 35, 0)
    timestamp_out = datetime(2024, 1, 15, 23, 50, 0)
    
    assert is_time_in_range(timestamp_in, (23, 30), (23, 45)) is True
    assert is_time_in_range(timestamp_out, (23, 30), (23, 45)) is False


def test_early_morning_range():
    """Test early morning time ranges."""
    # 1:00 AM to 3:00 AM
    timestamp_in = datetime(2024, 1, 15, 2, 0, 0)
    timestamp_out = datetime(2024, 1, 15, 4, 0, 0)
    
    assert is_time_in_range(timestamp_in, (1, 0), (3, 0)) is True
    assert is_time_in_range(timestamp_out, (1, 0), (3, 0)) is False


def test_invalid_range_same_hour():
    """Test that invalid ranges within same hour raise ValueError."""
    timestamp = datetime(2024, 1, 15, 10, 20, 0)
    
    # Start minute > end minute in same hour should raise error
    with pytest.raises(ValueError, match="Invalid time range: start time is after end time"):
        is_time_in_range(timestamp, (10, 30), (10, 15))


def test_invalid_range_different_hours():
    """Test that invalid ranges across hours raise ValueError."""
    timestamp = datetime(2024, 1, 15, 10, 20, 0)
    
    # Start hour > end hour should raise error
    with pytest.raises(ValueError, match="Invalid time range: start time is after end time"):
        is_time_in_range(timestamp, (15, 0), (14, 0))


def test_edge_case_midnight():
    """Test edge cases around midnight."""
    # Midnight timestamp
    timestamp = datetime(2024, 1, 15, 0, 0, 0)
    
    # Should work for ranges that include midnight
    assert is_time_in_range(timestamp, (0, 0), (1, 0)) is True
    assert is_time_in_range(timestamp, (23, 0), (23, 59)) is False


def test_edge_case_23_59():
    """Test edge case at 23:59."""
    timestamp = datetime(2024, 1, 15, 23, 59, 0)
    
    assert is_time_in_range(timestamp, (23, 0), (23, 59)) is True
    assert is_time_in_range(timestamp, (22, 0), (23, 58)) is False


def test_timezone_aware_datetime():
    """Test that timezone-aware datetimes work (timezone info should be ignored for time comparison)."""
    # Create timezone-aware datetime
    eastern_tz = pytz.timezone('US/Eastern')
    timestamp = datetime(2024, 1, 15, 10, 30, 0, tzinfo=eastern_tz)
    
    # Should work the same as timezone-naive
    assert is_time_in_range(timestamp, (9, 0), (11, 0)) is True


def test_boundary_hour_minute_values():
    """Test boundary values for hours and minutes."""
    # Test hour 0 and 23
    timestamp_midnight = datetime(2024, 1, 15, 0, 30, 0)
    timestamp_late = datetime(2024, 1, 15, 23, 30, 0)
    
    assert is_time_in_range(timestamp_midnight, (0, 0), (1, 0)) is True
    assert is_time_in_range(timestamp_late, (23, 0), (23, 59)) is True
    
    # Test minute 0 and 59
    timestamp_hour_start = datetime(2024, 1, 15, 12, 0, 0)
    timestamp_hour_end = datetime(2024, 1, 15, 12, 59, 0)
    
    assert is_time_in_range(timestamp_hour_start, (12, 0), (12, 30)) is True
    assert is_time_in_range(timestamp_hour_end, (12, 30), (12, 59)) is True


def test_single_minute_range():
    """Test range of exactly one minute."""
    timestamp_exact = datetime(2024, 1, 15, 15, 30, 0)
    timestamp_before = datetime(2024, 1, 15, 15, 29, 0)
    timestamp_after = datetime(2024, 1, 15, 15, 31, 0)
    
    # Range from 15:30 to 15:30 (single minute)
    assert is_time_in_range(timestamp_exact, (15, 30), (15, 30)) is True
    assert is_time_in_range(timestamp_before, (15, 30), (15, 30)) is False
    assert is_time_in_range(timestamp_after, (15, 30), (15, 30)) is False


@pytest.mark.parametrize("hour,minute", [
    (0, 0), (0, 59), (12, 30), (23, 0), (23, 59)
])
def test_various_times_within_all_day_range(hour, minute):
    """Test various times within an all-day range (0:00 to 23:59)."""
    timestamp = datetime(2024, 1, 15, hour, minute, 0)
    assert is_time_in_range(timestamp, (0, 0), (23, 59)) is True


def test_real_world_trading_hours():
    """Test real-world trading hour scenarios."""
    # Pre-market: 4:00 AM - 9:30 AM
    pre_market_time = datetime(2024, 1, 15, 8, 0, 0)
    assert is_time_in_range(pre_market_time, (4, 0), (9, 30)) is True
    
    # Regular market: 9:30 AM - 4:00 PM
    market_time = datetime(2024, 1, 15, 14, 30, 0)  # 2:30 PM
    assert is_time_in_range(market_time, (9, 30), (16, 0)) is True
    
    # After-hours: 4:00 PM - 8:00 PM
    after_hours_time = datetime(2024, 1, 15, 18, 30, 0)  # 6:30 PM
    assert is_time_in_range(after_hours_time, (16, 0), (20, 0)) is True