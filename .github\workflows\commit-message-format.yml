name: Commit Message Validator

on:
  pull_request:

permissions:
  pull-requests: write
  contents: read

jobs:
  check-commit-messages:
    runs-on: ubuntu-latest

    env:
      GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    steps:
      - name: Checkout full history
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Validate commit messages
        run: |
          echo "=========================================="
          echo "🔍 Starting commit message validation"
          echo "=========================================="

          echo "📣 Validating all PR commit messages"
          commit_shas=$(gh api repos/${{ github.repository }}/pulls/${{ github.event.pull_request.number }}/commits --jq '.[].sha')
          target_branch="${{ github.event.pull_request.base.ref }}"

          count=$(echo "$commit_shas" | wc -l)
          echo "📦 Total commits to check: $count"
          echo ""

          failed=false
          summary=""
          summary+="### 🔍 Commit Message Validation Summary"$'\n\n'
          summary+="**Expected Format:** \`<PROJECTKEY>-<number> <description>\`"$'\n'
          summary+="✅ Example: \`DATADEV-1234 Add login validation\`"$'\n\n'
          summary+="📘 For full guidelines, see the [Team Commit Message Guide](https://jainglobal.atlassian.net/wiki/spaces/Data/pages/30703807/Team+Guide)"$'\n\n'

          summary+="**Commits Checked:**"$'\n'

          for sha in $commit_shas; do
            message=$(git log -1 --format='%h %s' $sha)
            short_sha=$(echo "$message" | cut -d' ' -f1)
            msg_text=$(echo "$message" | cut -d' ' -f2-)

            echo "🔹 Checking commit $short_sha"
            echo "   Message: \"$msg_text\""

            # Allow all merge commits
            if echo "$msg_text" | grep -Eq '^Merge (branch|pull request)'; then
              echo "✅ Merge commit allowed."
              summary+="✅ $message"$'\n'
              continue
            fi

            # Validate JIRA-format messages
            if ! echo "$msg_text" | grep -Eq '^[A-Z][A-Z0-9]{1,9}-[0-9]+ .+'; then
              echo "❌ Invalid commit message"
              summary+="❌ $message"$'\n'
              failed=true
            else
              echo "✅ Commit message is valid."
              summary+="✅ $message"$'\n'
            fi
          done

          echo ""
          echo "=========================================="

          if [ "$failed" = true ]; then
            echo "💬 Posting result as a comment on PR..."

            if [ -n "$summary" ]; then
              echo "DEBUG: Comment body below ↓↓↓"
              echo "$summary"
              gh pr comment ${{ github.event.pull_request.number }} --body "$summary"
            else
              echo "⚠️ Skipping comment — no content."
            fi

            echo "❌ One or more commit messages are invalid. Failing workflow."
            exit 1
          else
            echo "✅ All commit messages are valid!"
            echo "✔️ Commit message validation completed successfully"
          fi

          echo "=========================================="
