from enum import Enum

class KplerDataType(Enum):
    ActualLoad = "actual_load"
    AvailPerFuelType = "avail_per_fuel_type"
    AvailPerUnit = "avail_per_unit"
    GenPerFuelType = "gen_per_fuel_type"
    GenPerUnit = "gen_per_unit"
    ForecastLoad = "forecast_load"
    ConsumptionPerFuelType = "consumption_per_fuel_type"
    CommercialSchedules = "commercial_schedules"

KPLER_BASE_URL = "https://api.kpler.com/power/"

REQUEST_TIMEZONE = "UTC"
REQUEST_GRANULARITY = "hourly"

API_MAX_RETRIES = 5
API_REQUEST_TIMEOUT = 60  # seconds


BASE_DATA_DIR = "/jfs/tech1/apps/rawdata/kpler_power/ongoing"


DICT_KPLER_PARAMS = {
    "actual_load": {
        "api_url": "loads/v1/series/actual",
        "start": "T-10D",
        "end": "T+1D",
        "sf_stage_name": "ACTUAL_LOAD_STAGE",
        "sf_table_name": "ACTUAL_LOAD",
        "concurrent_api_requests": 50,
        "processor": "KplerActualLoadProcessor",
        "countries": ['PL', 'ES', 'NO', 'DK', 'AT', 'SE', 'NL', 'IE', 'UK', 'CZ', 'BE', 'CH', 'FR', 'IT', 'DE', 'LU']
    },
    "avail_per_fuel_type": {    
        "api_url": "outages/v1/availability/series/fuel-types",
        "as_of": "current_hour",
        "start": "T+0D",
        "end": "T+2Y",
        "sf_stage_name": "AVAILABILITY_BY_FUELTYPE_STAGE",
        "sf_table_name": "AVAILABILITY_BY_FUELTYPE",
        "concurrent_api_requests": 300,
        "processor": "KplerAvailabilityByFuelTypeProcessor",
        "countries": ['AL','AT','BE','CH','CZ','DE','ES','FR','GR','IE','IT','NL','PL','RO','SE','UK'],
    },
    "avail_per_unit": {
        "api_url": "outages/v1/availability/series/units",
        "as_of": "current_hour",
        "start": "T+0D",
        "end": "T+2Y",
        "sf_stage_name": "AVAILABILITY_PER_UNITS_STAGE",
        "sf_table_name": "AVAILABILITY_PER_UNITS",
        "concurrent_api_requests": 200,
        "processor": "KplerAvailabilityPerUnitProcessor",
        "countries": ['UK', 'DE', 'FR', 'NL', 'BE', 'AT'],
    },
    "gen_per_fuel_type": {
        "api_url": "generations/v1/series/fuel-types",
        "start": "T-10D",
        "end": "T+1D",
        "sf_stage_name": "GENERATION_BY_FUELTYPE_STAGE",
        "sf_table_name": "GENERATION_BY_FUELTYPE",
        "processor": "KplerGenerationPerFuelTypeProcessor",
        "concurrent_api_requests": 200,
        "countries": ['AT', 'BA', 'BE', 'BG', 'CH', 'CY', 'CZ', 'DE', 'DE_50Hertz', 'DE_Amprion', 'DE_LU', 'DE_TransnetBW', 'DK', 'EE', 'ES', 'FI', 'FR', 'GE', 'GR', 'HR', 'HU', 'IE', 'IT', 'LT', 'LU', 'LV', 'MD', 'ME', 'MK', 'NL', 'NO', 'PL', 'PT', 'RO', 'RS', 'SE', 'SI', 'SK', 'UA', 'UK', 'XK'],
        # "countries": ['UK', 'AL', 'AT', 'BE', 'CH', 'CZ', 'DE', 'ES', 'FR', 'GR', 'IE', 'IT', 'NL', 'PL', 'RO', 'SE', 'BA', 'BG', 'DK', 'EE', 'FI', 'GE', 'HU', 'LT', 'LV', 'ME', 'MK', 'NO', 'PT', 'RS', 'SI', 'SK', 'UA', 'XK'],
    },
    "gen_per_unit": {
        "api_url": "generations/v1/series/units",
        "start": "T-10D",
        "end": "T+1D",
        "sf_stage_name": "GENERATION_BY_UNIT_STAGE",
        "sf_table_name": "GENERATION_BY_UNIT",
        "processor": "KplerGenerationPerUnitProcessor",
        "concurrent_api_requests": 200,
        "countries": ['UK', 'DE', 'FR', 'NL', 'BE', 'AT'],
    },
    "forecast_load": {
        "api_url": "loads/v1/series/forecasts",
        "run_date": "T+0D",
        "sf_stage_name": "FORECAST_LOAD_STAGE",
        "sf_table_name": "FORECAST_LOAD",
        "processor": "KplerForecastLoadProcessor",
        "concurrent_api_requests": 200,
        "countries": ['DK', 'PL', 'DE', 'ES', 'NO', 'UK', 'FR', 'IT', 'NL', 'AT', 'BE', 'CZ', 'CH'],
        "forecast_models": ['EC_OP', 'EC_ENS', 'EC_46', 'GFS_OP', 'GFS_ENS'],
        "demand_kind": ['demand'],
    },
    "consumption_per_fuel_type": {
        "api_url": "generations/v1/series/consumption/fuel-types/",
        "start": "T-10D",
        "end": "T+1D",
        "sf_stage_name": "CONSUMPTION_PER_FUELTYPE_STAGE",
        "sf_table_name": "CONSUMPTION_PER_FUELTYPE",
        "processor": "KplerConsumptionPerFuelTypeProcessor",
        "concurrent_api_requests": 200,
        "countries": ["AT", "BE", "CZ", "DE", "ES", "FR", "HR", "IE", "IT", "LT", "PT", "SK", "UA"],
    },
    "commercial_schedules": {
        "api_url": "interconnections/v1/series/commercial_schedules/",
        "start": "T-10D",
        "end": "T+2D",
        "sf_stage_name": "COMMERCIAL_SCHEDULES_STAGE",
        "sf_table_name": "COMMERCIAL_SCHEDULES",
        "processor": "KplerCommercialSchedulesProcessor",
        "concurrent_api_requests": 200,
        "countries": ["AT", "BE", "CH", "CZ", "DE", 'DK', "ES", "FR", 'GB', 'GR', 'HU', "IE", "IT", 'LT', 'LU', 'ME', 'MT', "NL", 'NO', "PL", 'SE', 'SI', 'SK', 'UA', 'UK'],
        "area_types": ["bidding_zone", "control_area", "country"],
        "market_types": ["day_ahead"],
    }
}


DICT_COUNTRY_FUELTYPE_PARAMS = {
    'UK': ['biomass', 'fossil gas', 'fossil hard coal', 'fossil oil', 'hydro pumped storage',
           'hydro run-of-river and poundage', 'hydro water reservoir', 'nuclear', 'other',
           'solar', 'wind offshore', 'wind onshore'],
    'AL': ['hydro water reservoir'],
    'AT': ['biomass', 'fossil gas', 'fossil hard coal', 'hydro pumped storage',
           'hydro run-of-river and poundage', 'hydro water reservoir', 
           'waste', 'wind onshore'],
    'BE': ['biomass', 'fossil gas', 'fossil hard coal',
           'hydro pumped storage', 'nuclear', 'waste', 'wind offshore'],
    'CH': ['hydro pumped storage', 'hydro run-of-river and poundage',
           'hydro water reservoir', 'nuclear'],
    'CZ': ['fossil brown coal/lignite', 'fossil gas', 'fossil hard coal',
           'hydro pumped storage', 'hydro run-of-river and poundage',
           'hydro water reservoir', 'nuclear', 'wind onshore'],
    'DE': ['biomass', 'fossil brown coal/lignite', 'fossil coal-derived gas',
           'fossil gas', 'fossil hard coal', 'fossil oil', 'hydro pumped storage',
           'hydro run-of-river and poundage', 'nuclear',
           'other', 'solar', 'waste', 'wind offshore', 'wind onshore'],
    'ES': ['biomass', 'fossil gas', 'fossil hard coal', 'fossil oil',
           'fossil peat', 'geothermal', 'hydro pumped storage', 'hydro water reservoir',
           'nuclear', 'other', 'solar', 'waste', 'wind onshore'],
    'FR': ['biomass', 'fossil gas', 'fossil hard coal', 'fossil oil',
           'hydro pumped storage', 'hydro run-of-river and poundage',
           'hydro water reservoir', 'marine', 'nuclear', 'other',
           'solar', 'wind offshore', 'wind onshore'],
    'GR': ['fossil brown coal/lignite', 'fossil gas', 'fossil oil',
           'hydro pumped storage', 'hydro run-of-river and poundage',
           'hydro water reservoir'],
    'IE': ['fossil gas', 'fossil hard coal', 'fossil oil', 'fossil peat',
           'hydro pumped storage', 'hydro run-of-river and poundage',
           'solar', 'waste', 'wind onshore'],
    'IT': ['biomass', 'fossil coal-derived gas', 'fossil gas', 'fossil hard coal',
           'fossil oil', 'geothermal', 'hydro pumped storage',
           'hydro run-of-river and poundage', 'hydro water reservoir',
           'other', 'other renewable', 'solar', 'waste', 'wind offshore', 'wind onshore'],
    'NL': ['biomass', 'fossil gas', 'fossil hard coal', 'hydro run-of-river and poundage',
           'nuclear', 'solar', 'wind offshore', 'wind onshore'],
    'PL': ['biomass', 'fossil brown coal/lignite', 'fossil coal-derived gas',
           'fossil gas', 'fossil hard coal', 'fossil oil', 'hydro pumped storage',
           'hydro run-of-river and poundage', 'hydro water reservoir', 'wind offshore'],
    'RO': ['fossil brown coal/lignite', 'fossil coal-derived gas', 'fossil gas',
           'fossil hard coal', 'hydro run-of-river and poundage',
           'hydro water reservoir', 'nuclear', 'wind offshore', 'wind onshore'],
    'SE': ['biomass', 'fossil gas', 'fossil hard coal', 'fossil oil',
           'fossil peat', 'hydro run-of-river and poundage', 'hydro water reservoir',
           'nuclear', 'other', 'other renewable', 'waste', 'wind offshore', 'wind onshore'],
    'BA': ['fossil brown coal/lignite', 'hydro pumped storage', 'hydro water reservoir'],
    'BG': ['fossil brown coal/lignite', 'fossil gas', 'fossil hard coal',
           'hydro pumped storage', 'hydro water reservoir', 'nuclear',
           'wind offshore', 'wind onshore'],
    'DK': ['biomass', 'fossil gas', 'fossil hard coal', 'fossil oil', 'solar', 'wind offshore'],
    'EE': ['fossil gas', 'fossil oil', 'fossil oil shale'],
    'FI': ['biomass', 'fossil gas', 'fossil hard coal', 'fossil oil', 'fossil peat',
           'hydro run-of-river and poundage', 'nuclear', 'waste', 'wind offshore', 'wind onshore'],
    'GE': ['fossil gas', 'hydro run-of-river and poundage', 'hydro water reservoir'],
    'HU': ['biomass', 'fossil brown coal/lignite', 'fossil gas', 'fossil hard coal',
           'fossil oil', 'nuclear', 'other', 'other renewable', 'solar', 'wind offshore'],
    'LT': ['fossil gas', 'hydro pumped storage', 'hydro run-of-river and poundage', 'wind offshore'],
    'LV': ['fossil gas', 'hydro run-of-river and poundage'],
    'ME': ['fossil brown coal/lignite', 'hydro run-of-river and poundage',
           'hydro water reservoir', 'wind offshore', 'wind onshore'],
    'MK': ['fossil brown coal/lignite', 'fossil gas', 'fossil oil',
           'hydro run-of-river and poundage', 'hydro water reservoir'],
    'NO': ['fossil gas', 'hydro pumped storage', 'hydro water reservoir',
           'wind offshore', 'wind onshore'],
    'PT': ['fossil gas', 'fossil hard coal', 'hydro pumped storage',
           'hydro run-of-river and poundage', 'hydro water reservoir'],
    'RS': ['biomass', 'fossil brown coal/lignite', 'fossil gas', 'hydro pumped storage',
           'hydro run-of-river and poundage', 'hydro water reservoir', 'waste',
           'wind offshore', 'wind onshore'],
    'SI': ['fossil brown coal/lignite', 'fossil gas', 'hydro pumped storage',
           'hydro run-of-river and poundage', 'nuclear'],
    'SK': ['fossil brown coal/lignite', 'fossil gas', 'fossil hard coal',
           'hydro pumped storage', 'hydro run-of-river and poundage',
           'hydro water reservoir', 'nuclear'],
    'UA': ['fossil gas', 'fossil hard coal', 'hydro pumped storage',
           'hydro water reservoir', 'nuclear', 'other renewable', 'wind offshore'],
    'XK': ['fossil brown coal/lignite', 'hydro run-of-river and poundage',
           'hydro water reservoir', 'wind offshore', 'wind onshore']
}

DICT_GEN_COUNTRY_FUELTYPE_PARAMS = {
    "AT": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "geothermal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind onshore",
    ],
    "BA": [
        "fossil brown coal/lignite",
        "fossil hard coal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "other renewable",
        "solar",
        "wind onshore",
    ],
    "BE": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "nuclear",
        "other",
        "solar",
        "waste",
        "wind offshore",
        "wind onshore",
    ],
    "BG": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil gas",
        "fossil hard coal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "solar",
        "waste",
        "wind onshore",
    ],
    "CH": [
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "solar",
        "wind onshore",
    ],
    "CY": ["fossil oil", "wind onshore"],
    "CZ": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil coal-derived gas",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind onshore",
    ],
    "DE": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil coal-derived gas",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "geothermal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind offshore",
        "wind onshore",
    ],
    "DE_50Hertz": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "geothermal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind offshore",
        "wind onshore",
    ],
    "DE_Amprion": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil coal-derived gas",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "geothermal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind onshore",
    ],
    "DE_LU": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil coal-derived gas",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "geothermal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind offshore",
        "wind onshore",
    ],
    "DE_TransnetBW": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind onshore",
    ],
    "DK": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "solar",
        "waste",
        "wind offshore",
        "wind onshore",
    ],
    "EE": [
        "biomass",
        "fossil coal-derived gas",
        "fossil gas",
        "fossil oil shale",
        "fossil peat",
        "hydro run-of-river and poundage",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind onshore",
    ],
    "ES": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil coal-derived gas",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "fossil oil shale",
        "fossil peat",
        "geothermal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "marine",
        "nuclear",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind offshore",
        "wind onshore",
    ],
    "FI": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "fossil peat",
        "hydro run-of-river and poundage",
        "nuclear",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind onshore",
    ],
    "FR": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "solar",
        "waste",
        "wind offshore",
        "wind onshore",
    ],
    "GE": [
        "fossil gas",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "wind onshore",
    ],
    "GR": [
        "fossil brown coal/lignite",
        "fossil gas",
        "fossil oil",
        "hydro pumped storage",
        "hydro water reservoir",
        "solar",
        "wind onshore",
    ],
    "HR": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "geothermal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "other renewable",
        "solar",
        "waste",
        "wind onshore",
    ],
    "HU": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "geothermal",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind onshore",
    ],
    "IE": [
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "fossil peat",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "other",
        "wind onshore",
    ],
    "IT": [
        "biomass",
        "fossil coal-derived gas",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "geothermal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "other",
        "solar",
        "waste",
        "wind offshore",
        "wind onshore",
    ],
    "LT": [
        "biomass",
        "fossil gas",
        "fossil oil",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "other",
        "solar",
        "waste",
        "wind onshore",
    ],
    "LU": [
        "biomass",
        "fossil gas",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "solar",
        "waste",
        "wind onshore",
    ],
    "LV": [
        "biomass",
        "fossil gas",
        "hydro run-of-river and poundage",
        "other",
        "solar",
        "wind onshore",
    ],
    "MD": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "hydro run-of-river and poundage",
        "solar",
        "wind onshore",
    ],
    "ME": [
        "fossil brown coal/lignite",
        "fossil hard coal",
        "hydro water reservoir",
        "other renewable",
        "solar",
        "wind onshore",
    ],
    "MK": [
        "fossil brown coal/lignite",
        "fossil gas",
        "fossil oil",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "wind onshore",
    ],
    "NL": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "hydro run-of-river and poundage",
        "nuclear",
        "other",
        "solar",
        "waste",
        "wind offshore",
        "wind onshore",
    ],
    "NO": [
        "biomass",
        "fossil gas",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "other",
        "other renewable",
        "solar",
        "waste",
        "wind onshore",
    ],
    "PL": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil coal-derived gas",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "other",
        "other renewable",
        "solar",
        "wind onshore",
    ],
    "PT": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "other",
        "solar",
        "wind offshore",
        "wind onshore",
    ],
    "RO": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil gas",
        "fossil hard coal",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "solar",
        "wind onshore",
    ],
    "RS": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil gas",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "other",
        "wind onshore",
    ],
    "SE": [
        "fossil gas",
        "hydro water reservoir",
        "marine",
        "nuclear",
        "other",
        "solar",
        "wind onshore",
    ],
    "SI": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil gas",
        "fossil oil",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "nuclear",
        "solar",
        "waste",
        "wind onshore",
    ],
    "SK": [
        "biomass",
        "fossil brown coal/lignite",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "nuclear",
        "other",
        "other renewable",
        "solar",
        "wind onshore",
    ],
    "UA": [
        "fossil gas",
        "fossil hard coal",
        "hydro pumped storage",
        "hydro water reservoir",
        "nuclear",
        "other",
        "solar",
        "wind onshore",
    ],
    # "UA_BEI": [
    #     "fossil hard coal",
    #     "hydro water reservoir",
    #     "other",
    #     "solar",
    #     "wind onshore",
    # ],
    # "UA_IPS": [
    #     "fossil gas",
    #     "fossil hard coal",
    #     "hydro pumped storage",
    #     "hydro water reservoir",
    #     "nuclear",
    #     "solar",
    #     "wind onshore",
    # ],
    "UK": [
        "biomass",
        "fossil gas",
        "fossil hard coal",
        "fossil oil",
        "hydro pumped storage",
        "hydro run-of-river and poundage",
        "nuclear",
        "other",
        "solar",
        "wind offshore",
        "wind onshore",
    ],
    "XK": [
        "fossil brown coal/lignite",
        "hydro run-of-river and poundage",
        "hydro water reservoir",
        "wind onshore",
    ],
}


