-- use if needed

COPY INTO NORMAL 
FROM '@SAM_TEST/' PATTERN = '.*/NORMAL/.*'
FILE_FORMAT = (TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE';

COPY INTO OBSERVATION 
FROM '@SAM_TEST/' PATTERN = '.*/OBSERVATION/.*'
FILE_FORMAT = (TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE';

COPY INTO FORECAST_DETERMINISTIC 
FROM '@SAM_TEST/' PATTERN = '.*/FORECAST_DETERMINISTIC/.*'
FILE_FORMAT = (TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE';

COPY INTO FORECAST_ENSEMBLE 
FROM '@SAM_TEST/' PATTERN = '.*/FORECAST_ENSEMBLE/.*'
FILE_FORMAT = (TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE';

COPY INTO FORECAST_EXTENDED 
FROM '@SAM_TEST/' PATTERN = '.*/FORECAST_EXTENDED/.*'
FILE_FORMAT = (TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE';
