_dict_month_code = {
    1: "F",
    2: "G",
    3: "H",
    4: "J",
    5: "K",
    6: "<PERSON>",
    7: "N",
    8: "Q",
    9: "U",
    10: "V",
    11: "X",
    12: "Z",
}


def get_bbg_full_ticker_for_futures(future_root, month_code, year):
    if len(future_root) == 1:
        future_root = future_root + " "

    year = str(year)[-2:]

    return future_root + _dict_month_code[month_code] + str(year)


if __name__ == "__main__":
    a = get_bbg_full_ticker_for_futures("C", 1, 2074)
    print(a)