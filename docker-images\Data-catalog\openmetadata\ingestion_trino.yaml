source:
  type: trino
  serviceName: trino
  serviceConnection:
    config:
      type: Trino      
      hostPort: "$TRINO_INGRESS:80"
      username: root
      catalog: iceberg
      # databaseSchema: ice
  sourceConfig:
    config:
      type: DatabaseMetadata
      markDeletedTables: true
      includeTables: true
      includeViews: true
      includeTags: true
sink:
  type: metadata-rest
  config: {}
workflowConfig:
  loggerLevel: DEBUG
  openMetadataServerConfig:
    hostPort: "http://$SERVICE_IP/api"
    authProvider: openmetadata
    securityConfig:
      jwtToken: $OPENMETADATA_JWT_TOKEN
    storeServiceConnection: true
