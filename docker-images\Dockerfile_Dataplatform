FROM python:3.12-slim

# Set environment variables
ARG USERNAME
ARG LOGIN_KEY
ENV USERNAME=$USERNAME
ENV LOGIN_KEY=$LOGIN_KEY
USER root

RUN apt-get update && \
    apt-get install -y less wget vim apache2-utils procps gettext kmod openssh-client fuse3 findutils curl alien dos2unix && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

COPY docker-images/cwiqfs/fuse-libs-2.9.2-11.el7.x86_64.rpm /tmp/
COPY docker-images/cwiqfs/fuse-sshfs-2.10-1.el7.x86_64.rpm /tmp/
COPY docker-images/cwiqfs/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm /tmp/

RUN alien -i /tmp/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm && \
    rm /tmp/*.rpm  # Clean up RPM files after installation

RUN ln -sf /etc/ssl/certs/ca-bundle.crt /etc/ssl/certs/ca-certificates.crt

COPY docker-images/cwiqfs/cwiqfs.yaml /etc/cwiq/cwiqfs/config_temp.yaml
RUN sed -e "s/\${USERNAME}/$USERNAME/g" -e "s/\${LOGIN_KEY}/$LOGIN_KEY/g" /etc/cwiq/cwiqfs/config_temp.yaml > /etc/cwiq/cwiqfs/config.yaml

RUN sed -i -e 's/# user_allow_other/user_allow_other/' /etc/fuse.conf
RUN mkdir /jfs
RUN useradd jsvc-tech1

RUN mkdir -p /var/tmp/cache /var/log/cwiq && \
    chmod 777 /var/tmp/cache && \
    chown -R root:root /var/tmp/cache /var/log/cwiq

COPY docker-images/requirements_Airflow.txt /
COPY docker-images/docker_conf/airflow/init_dataplatform.sh /

RUN pip install -r /requirements_Airflow.txt && \
    pip install "pyiceberg[pyarrow]" 
RUN pip install apache-airflow
RUN pip install apache-airflow[cncf.kubernetes]
RUN pip install virtualenv
RUN dos2unix /init_dataplatform.sh

ENV USER="jsvc-datait"
ENV JGDATA_PATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/"
ENV STCOMMON_PATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/stcommon"
ENV RAWSTORE_ROOT="/jfs/tech1/apps/rawdata/"
ENV BUILDINPARALLEL=False
ENV SPARK_DIST_CLASSPATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/jars/iceberg-spark-runtime.jar"
ENV CONFIG_PATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source"

CMD ["sh", "/init_dataplatform.sh"]
