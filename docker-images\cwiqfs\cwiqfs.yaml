# FSDB server address (with format <host>:<port>)
# multiple address are separated with comma
fsdb_addr: ************:28082,************:28082

# Use TLS in connecting to FSDB. (Optional; default value is false).
fsdb_use_tls: False

# CasFS username
fsdb_login_id: ${USERNAME}

# CasFS password
fsdb_login_key: ${LOGIN_KEY}

# CasFS admin port
admin_port: 9128

# Time before http requests to storage time out. default is 5 seconds
connection_timeout_sec: 5

# A list of prefix nodes for which audit logs are more verbose, including PID, command, and
# command line
proc_whitelist:
  - /

# An opposite of proc_whitelist. (Optional; default value is [])
#proc_excludelist:
#  []

# Override the uid/gid visible to the user
#override_ids:
#  uid:
#    - 1181:1217
#    - 1209:1271
#  gid:
#    - 3000:20

