---
# Source: aws-load-balancer-controller/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: aws-load-balancer-controller
  namespace: kube-system
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
automountServiceAccountToken: true
---
# Source: aws-load-balancer-controller/templates/webhook.yaml
apiVersion: v1
kind: Secret
metadata:
  name: aws-load-balancer-tls
  namespace: kube-system
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
type: kubernetes.io/tls
data:
  ca.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lSQVAvUjhyNU12WXFZUHdmeEd6cXhETm93RFFZSktvWklodmNOQVFFTEJRQXcKS2pFb01DWUdBMVVFQXhNZllYZHpMV3h2WVdRdFltRnNZVzVqWlhJdFkyOXVkSEp2Ykd4bGNpMWpZVEFlRncweQpOREF6TURVd09UQXhORFJhRncwek5EQXpNRE13T1RBeE5EUmFNQ294S0RBbUJnTlZCQU1USDJGM2N5MXNiMkZrCkxXSmhiR0Z1WTJWeUxXTnZiblJ5YjJ4c1pYSXRZMkV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXcKZ2dFS0FvSUJBUUREU2pkTjhwYUNVSkxZeXJXaUdxTEdTanBxTjZVa3dEYVEyN00wRnJlYmN3K0NEUkd3SVhXVgpxekJ4b2ptaW94RTJ3dEtmODFOQ0JNV0IwMUtHdlFGc0lRVjZGd0h4dlZTZ1NWS2thUVptY0kveW4zV2trWlFvCk9pY0x4STNGeklpemFJK1NnUmpmM1E3WVBNTlhac3A0b3haM3VPRElmakluVzQwMG5UdFlOVkRsek1JK1hHcXMKNy9EL216OUlYZXBxVmt3WVMzR0hTVzZ3SWxXYTBKQXdMSWcxbFRZNElFNjk3SmVNZFY0QXhqdzBXNytWZEhGbgpxbjZzSEF5SUxuZjkvYkZaVzZTWHR6R3lQcEZNOGVNVjFqOWh2SCtNaCs3Tmd1djRjcEdkdWhkVUcxTzVLMDFTCnRMRnBDeG16VnBwQ09WMDdHK2hoT1RkUVo0NVlENTBCQWdNQkFBR2pZVEJmTUE0R0ExVWREd0VCL3dRRUF3SUMKcERBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFRWUlLd1lCQlFVSEF3SXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVUWRmVFZRTDRvSTBiT2FkeEU4K1Y2aThFa3ZBd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSzNWczNLNisxS2p1QzRzWTg3Y2l2UkJPUlpQWVVjQ3FMQndOeXJObGFsVkhpVGFEcEU3VnVGckZ5dFYKc1FCRlBOYWZBKzR4MzdKQkRhL0xsMDI2R2gvY3JxSE5iSlBGZHVUZ0lpdkZqVFRQdmpSZG9QODZmQmZzM2dKeQpDTDNYalludXZnUVExdU5TVmVrL1NpSTdiVVEvR0wwdjYrSFlKRklHUVJsWW15VklJQWF2VUQzTWRKdlUwTGpYCmRkYWpidG55TWxyQWIxMEhMVHkxbDJDSjd0WFNxS1MwcW00NkthNDlKZy9DbEhWclRIbWgxdVhVTEo3dVBlQW0KSFVNa0ZHZzE4eVBCcmhPN0Zma2p3ZlJaZUlrL0NYMnllS21WbThNeUJTSnVJaUxFZ25RYUx0dFlWd3FhekFqdwpOZTB4cm9XcDI0K05HUDNJeWVCNlVNbnpQbDg9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUQ4RENDQXRpZ0F3SUJBZ0lRRmRmcTJ2b0dCanIzV0JaTkdpbldYVEFOQmdrcWhraUc5dzBCQVFzRkFEQXEKTVNnd0pnWURWUVFERXg5aGQzTXRiRzloWkMxaVlXeGhibU5sY2kxamIyNTBjbTlzYkdWeUxXTmhNQjRYRFRJMApNRE13TlRBNU1ERTBOVm9YRFRNME1ETXdNekE1TURFME5Wb3dKekVsTUNNR0ExVUVBeE1jWVhkekxXeHZZV1F0ClltRnNZVzVqWlhJdFkyOXVkSEp2Ykd4bGNqQ0NBU0l3RFFZSktvWklodmNOQVFFQkJRQURnZ0VQQURDQ0FRb0MKZ2dFQkFMeHdUNEZkQldQUXh5Vlh3QzdKUkJmRmhMTHFZM1VmK1Z4SzJ3ci9XMGpmWTFqWXpZeStYQ2R4TWxrcQo4UFhUNXY2ZWRNbFVvWUxka290RWl5YTViYklPbFJHYjBLcGhvdnRtRyswRjJreUdQaUNnYldRTDZ6eVYzS0MwCnB3MTFiSkdZWW04YWZybk1pV0hmVGtWa2l3azhXejNxUlBhU3ZwWUh6T3VUOHAya0VFVmV3M0VzcEI4UThsNUIKNytqdWQzWk9yRDAwUno4NUJSRVVJcWtlMkxIZDAwUk0yQzQrY1NmV05GMy8rd2hJT3M3YnFvV3RGY1l0Q3FuLwpPam8zYitmQzhpV3g0WWwzd04vZ1ZiaXFsenVCeHFzYWpCMEhXdG94blJxOEQweUZZZXlHTE5ZblhJdnBwbmFZCms1NWZPbkpkRDRPdmdsaU4rOVZKVUlaL2NmTUNBd0VBQWFPQ0FSTXdnZ0VQTUE0R0ExVWREd0VCL3dRRUF3SUYKb0RBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFRWUlLd1lCQlFVSEF3SXdEQVlEVlIwVEFRSC9CQUl3QURBZgpCZ05WSFNNRUdEQVdnQlJCMTlOVkF2aWdqUnM1cDNFVHo1WHFMd1NTOERDQnJnWURWUjBSQklHbU1JR2pnaTFoCmQzTXRiRzloWkMxaVlXeGhibU5sY2kxM1pXSm9iMjlyTFhObGNuWnBZMlV1YTNWaVpTMXplWE4wWlcyQ01XRjMKY3kxc2IyRmtMV0poYkdGdVkyVnlMWGRsWW1odmIyc3RjMlZ5ZG1salpTNXJkV0psTFhONWMzUmxiUzV6ZG1PQwpQMkYzY3kxc2IyRmtMV0poYkdGdVkyVnlMWGRsWW1odmIyc3RjMlZ5ZG1salpTNXJkV0psTFhONWMzUmxiUzV6CmRtTXVZMngxYzNSbGNpNXNiMk5oYkRBTkJna3Foa2lHOXcwQkFRc0ZBQU9DQVFFQWMwbW5HZVM4aWg1cUdGNEQKT3B1ZlR0OFNSQ2N2U3kwK3c1NndBZHQwRmUrTlk2ZGE1ZVZ2QlEzUFhoc21NMVlWODJhUDM2aDN0eHR6QlhmNQo5RWR3VmRmNDJyQ21PSlFxYUUxQ2lmTVlCcGFDRndtRzZDdURVYWFxMEF3WkRnZ1JIM2tuR0d2NGxDWVFPUm1lCmYzaUNhZlZBTGl1ZWVORW1uRzJRTkJFRjVXUFRlVEdvdXQ4NDNwRG4zV0hDQVQ0a1Z4YzdMbStoMVNKV1ZIVXMKZFVjMUhUYUtscHliUm9Rb3BmSi9EOXdBMHdGcWZzVS9pMXgzUGlsMzJwSFh3N0k1YjloSVZPcnFYNUpGLzQrdwozblNiamdkalVabWo2MTRQSnN6Y21GenNMcXhoYk12bGdHWGJDWjZORlZya3FFVVh5STJ2ZGhpN3d5NndUanF0CnlXdTBQQT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
  tls.key: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
---
# Source: aws-load-balancer-controller/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: aws-load-balancer-controller-role
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
rules:
- apiGroups: ["elbv2.k8s.aws"]
  resources: [targetgroupbindings]
  verbs: [create, delete, get, list, patch, update, watch]
- apiGroups: ["elbv2.k8s.aws"]
  resources: [ingressclassparams]
  verbs: [get, list, watch]
- apiGroups: [""]
  resources: [events]
  verbs: [create, patch]
- apiGroups: [""]
  resources: [pods]
  verbs: [get, list, watch]
- apiGroups: ["networking.k8s.io"]
  resources: [ingressclasses]
  verbs: [get, list, watch]
- apiGroups: ["", "extensions", "networking.k8s.io"]
  resources: [services, ingresses]
  verbs: [get, list, patch, update, watch]
- apiGroups: [""]
  resources: [nodes, namespaces, endpoints]
  verbs: [get, list, watch]
- apiGroups: ["elbv2.k8s.aws", "", "extensions", "networking.k8s.io"]
  resources: [targetgroupbindings/status, pods/status, services/status, ingresses/status]
  verbs: [update, patch]
- apiGroups: ["discovery.k8s.io"]
  resources: [endpointslices]
  verbs: [get, list, watch]
---
# Source: aws-load-balancer-controller/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: aws-load-balancer-controller-rolebinding
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: aws-load-balancer-controller-role
subjects:
- kind: ServiceAccount
  name: aws-load-balancer-controller
  namespace: kube-system
---
# Source: aws-load-balancer-controller/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: aws-load-balancer-controller-leader-election-role
  namespace: kube-system
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
rules:
- apiGroups: [""]
  resources: [configmaps]
  verbs: [create]
- apiGroups: [""]
  resources: [configmaps]
  resourceNames: [aws-load-balancer-controller-leader]
  verbs: [get, patch, update]
- apiGroups:
  - "coordination.k8s.io"
  resources:
  - leases
  verbs:
  - create
- apiGroups:
  - "coordination.k8s.io"
  resources:
  - leases
  resourceNames:
  - aws-load-balancer-controller-leader
  verbs:
  - get
  - update
  - patch
---
# Source: aws-load-balancer-controller/templates/rbac.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: aws-load-balancer-controller-leader-election-rolebinding
  namespace: kube-system
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: aws-load-balancer-controller-leader-election-role
subjects:
- kind: ServiceAccount
  name: aws-load-balancer-controller
  namespace: kube-system
---
# Source: aws-load-balancer-controller/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: aws-load-balancer-webhook-service
  namespace: kube-system
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/component: webhook
    prometheus.io/service-monitor: "false"
spec:
  ports:
  - port: 443
    name: webhook-server
    targetPort: webhook-server
  selector:
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
---
# Source: aws-load-balancer-controller/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: aws-load-balancer-controller
  namespace: kube-system
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
spec:
  replicas: 2
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: aws-load-balancer-controller
      app.kubernetes.io/instance: aws-load-balancer-controller
  template:
    metadata:
      labels:
        app.kubernetes.io/name: aws-load-balancer-controller
        app.kubernetes.io/instance: aws-load-balancer-controller
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
    spec:
      serviceAccountName: aws-load-balancer-controller
      volumes:
      - name: cert
        secret:
          defaultMode: 420
          secretName: aws-load-balancer-tls
      securityContext:
        fsGroup: 65534
      containers:
      - name: aws-load-balancer-controller
        args:
        - --cluster-name=${PREFIX}-eks-default-${ENV}
        - --ingress-class=alb
        - --enable-waf=false
        - --enable-wafv2=false
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
        image: "${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/aws-load-balancer-controller:v2.7.1"
        imagePullPolicy: IfNotPresent
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
        ports:
        - name: webhook-server
          containerPort: 9443
          protocol: TCP
        - name: metrics-server
          containerPort: 8080
          protocol: TCP
        resources:
          {}
        livenessProbe:
          failureThreshold: 2
          httpGet:
            path: /healthz
            port: 61779
            scheme: HTTP
          initialDelaySeconds: 30
          timeoutSeconds: 10
        readinessProbe:
          failureThreshold: 2
          httpGet:
            path: /readyz
            port: 61779
            scheme: HTTP
          initialDelaySeconds: 10
          successThreshold: 1
          timeoutSeconds: 10
      terminationGracePeriodSeconds: 10
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app.kubernetes.io/name
                  operator: In
                  values:
                  - aws-load-balancer-controller
              topologyKey: kubernetes.io/hostname
      priorityClassName: "system-cluster-critical"
---
# Source: aws-load-balancer-controller/templates/ingressclass.yaml
apiVersion: v1
kind: List
metadata:
  name: ingress-class
items:
- apiVersion: elbv2.k8s.aws/v1beta1
  kind: IngressClassParams
  metadata:
    name: alb
    labels:
      helm.sh/chart: aws-load-balancer-controller-1.7.1
      app.kubernetes.io/name: aws-load-balancer-controller
      app.kubernetes.io/instance: aws-load-balancer-controller
      app.kubernetes.io/version: "v2.7.1"
      app.kubernetes.io/managed-by: Helm
- apiVersion: networking.k8s.io/v1
  kind: IngressClass
  metadata:
    name: alb
    labels:
      helm.sh/chart: aws-load-balancer-controller-1.7.1
      app.kubernetes.io/name: aws-load-balancer-controller
      app.kubernetes.io/instance: aws-load-balancer-controller
      app.kubernetes.io/version: "v2.7.1"
      app.kubernetes.io/managed-by: Helm
  spec:
    controller: ingress.k8s.aws/alb
---
# Source: aws-load-balancer-controller/templates/webhook.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: aws-load-balancer-webhook
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
webhooks:
- clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lSQVAvUjhyNU12WXFZUHdmeEd6cXhETm93RFFZSktvWklodmNOQVFFTEJRQXcKS2pFb01DWUdBMVVFQXhNZllYZHpMV3h2WVdRdFltRnNZVzVqWlhJdFkyOXVkSEp2Ykd4bGNpMWpZVEFlRncweQpOREF6TURVd09UQXhORFJhRncwek5EQXpNRE13T1RBeE5EUmFNQ294S0RBbUJnTlZCQU1USDJGM2N5MXNiMkZrCkxXSmhiR0Z1WTJWeUxXTnZiblJ5YjJ4c1pYSXRZMkV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXcKZ2dFS0FvSUJBUUREU2pkTjhwYUNVSkxZeXJXaUdxTEdTanBxTjZVa3dEYVEyN00wRnJlYmN3K0NEUkd3SVhXVgpxekJ4b2ptaW94RTJ3dEtmODFOQ0JNV0IwMUtHdlFGc0lRVjZGd0h4dlZTZ1NWS2thUVptY0kveW4zV2trWlFvCk9pY0x4STNGeklpemFJK1NnUmpmM1E3WVBNTlhac3A0b3haM3VPRElmakluVzQwMG5UdFlOVkRsek1JK1hHcXMKNy9EL216OUlYZXBxVmt3WVMzR0hTVzZ3SWxXYTBKQXdMSWcxbFRZNElFNjk3SmVNZFY0QXhqdzBXNytWZEhGbgpxbjZzSEF5SUxuZjkvYkZaVzZTWHR6R3lQcEZNOGVNVjFqOWh2SCtNaCs3Tmd1djRjcEdkdWhkVUcxTzVLMDFTCnRMRnBDeG16VnBwQ09WMDdHK2hoT1RkUVo0NVlENTBCQWdNQkFBR2pZVEJmTUE0R0ExVWREd0VCL3dRRUF3SUMKcERBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFRWUlLd1lCQlFVSEF3SXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVUWRmVFZRTDRvSTBiT2FkeEU4K1Y2aThFa3ZBd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSzNWczNLNisxS2p1QzRzWTg3Y2l2UkJPUlpQWVVjQ3FMQndOeXJObGFsVkhpVGFEcEU3VnVGckZ5dFYKc1FCRlBOYWZBKzR4MzdKQkRhL0xsMDI2R2gvY3JxSE5iSlBGZHVUZ0lpdkZqVFRQdmpSZG9QODZmQmZzM2dKeQpDTDNYalludXZnUVExdU5TVmVrL1NpSTdiVVEvR0wwdjYrSFlKRklHUVJsWW15VklJQWF2VUQzTWRKdlUwTGpYCmRkYWpidG55TWxyQWIxMEhMVHkxbDJDSjd0WFNxS1MwcW00NkthNDlKZy9DbEhWclRIbWgxdVhVTEo3dVBlQW0KSFVNa0ZHZzE4eVBCcmhPN0Zma2p3ZlJaZUlrL0NYMnllS21WbThNeUJTSnVJaUxFZ25RYUx0dFlWd3FhekFqdwpOZTB4cm9XcDI0K05HUDNJeWVCNlVNbnpQbDg9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K

    service:
      name: aws-load-balancer-webhook-service
      namespace: kube-system
      path: /mutate-v1-pod
  failurePolicy: Fail
  name: mpod.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  namespaceSelector:
    matchExpressions:

    - key: elbv2.k8s.aws/pod-readiness-gate-inject
      operator: In
      values:
      - enabled

  objectSelector:
    matchExpressions:
    - key: app.kubernetes.io/name
      operator: NotIn
      values:
      - aws-load-balancer-controller
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - pods
  sideEffects: None
- clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lSQVAvUjhyNU12WXFZUHdmeEd6cXhETm93RFFZSktvWklodmNOQVFFTEJRQXcKS2pFb01DWUdBMVVFQXhNZllYZHpMV3h2WVdRdFltRnNZVzVqWlhJdFkyOXVkSEp2Ykd4bGNpMWpZVEFlRncweQpOREF6TURVd09UQXhORFJhRncwek5EQXpNRE13T1RBeE5EUmFNQ294S0RBbUJnTlZCQU1USDJGM2N5MXNiMkZrCkxXSmhiR0Z1WTJWeUxXTnZiblJ5YjJ4c1pYSXRZMkV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXcKZ2dFS0FvSUJBUUREU2pkTjhwYUNVSkxZeXJXaUdxTEdTanBxTjZVa3dEYVEyN00wRnJlYmN3K0NEUkd3SVhXVgpxekJ4b2ptaW94RTJ3dEtmODFOQ0JNV0IwMUtHdlFGc0lRVjZGd0h4dlZTZ1NWS2thUVptY0kveW4zV2trWlFvCk9pY0x4STNGeklpemFJK1NnUmpmM1E3WVBNTlhac3A0b3haM3VPRElmakluVzQwMG5UdFlOVkRsek1JK1hHcXMKNy9EL216OUlYZXBxVmt3WVMzR0hTVzZ3SWxXYTBKQXdMSWcxbFRZNElFNjk3SmVNZFY0QXhqdzBXNytWZEhGbgpxbjZzSEF5SUxuZjkvYkZaVzZTWHR6R3lQcEZNOGVNVjFqOWh2SCtNaCs3Tmd1djRjcEdkdWhkVUcxTzVLMDFTCnRMRnBDeG16VnBwQ09WMDdHK2hoT1RkUVo0NVlENTBCQWdNQkFBR2pZVEJmTUE0R0ExVWREd0VCL3dRRUF3SUMKcERBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFRWUlLd1lCQlFVSEF3SXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVUWRmVFZRTDRvSTBiT2FkeEU4K1Y2aThFa3ZBd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSzNWczNLNisxS2p1QzRzWTg3Y2l2UkJPUlpQWVVjQ3FMQndOeXJObGFsVkhpVGFEcEU3VnVGckZ5dFYKc1FCRlBOYWZBKzR4MzdKQkRhL0xsMDI2R2gvY3JxSE5iSlBGZHVUZ0lpdkZqVFRQdmpSZG9QODZmQmZzM2dKeQpDTDNYalludXZnUVExdU5TVmVrL1NpSTdiVVEvR0wwdjYrSFlKRklHUVJsWW15VklJQWF2VUQzTWRKdlUwTGpYCmRkYWpidG55TWxyQWIxMEhMVHkxbDJDSjd0WFNxS1MwcW00NkthNDlKZy9DbEhWclRIbWgxdVhVTEo3dVBlQW0KSFVNa0ZHZzE4eVBCcmhPN0Zma2p3ZlJaZUlrL0NYMnllS21WbThNeUJTSnVJaUxFZ25RYUx0dFlWd3FhekFqdwpOZTB4cm9XcDI0K05HUDNJeWVCNlVNbnpQbDg9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K

    service:
      name: aws-load-balancer-webhook-service
      namespace: kube-system
      path: /mutate-v1-service
  failurePolicy: Fail
  name: mservice.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  objectSelector:
    matchExpressions:
    - key: app.kubernetes.io/name
      operator: NotIn
      values:
      - aws-load-balancer-controller
  rules:
  - apiGroups:
    - ""
    apiVersions:
    - v1
    operations:
    - CREATE
    resources:
    - services
  sideEffects: None
- clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lSQVAvUjhyNU12WXFZUHdmeEd6cXhETm93RFFZSktvWklodmNOQVFFTEJRQXcKS2pFb01DWUdBMVVFQXhNZllYZHpMV3h2WVdRdFltRnNZVzVqWlhJdFkyOXVkSEp2Ykd4bGNpMWpZVEFlRncweQpOREF6TURVd09UQXhORFJhRncwek5EQXpNRE13T1RBeE5EUmFNQ294S0RBbUJnTlZCQU1USDJGM2N5MXNiMkZrCkxXSmhiR0Z1WTJWeUxXTnZiblJ5YjJ4c1pYSXRZMkV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXcKZ2dFS0FvSUJBUUREU2pkTjhwYUNVSkxZeXJXaUdxTEdTanBxTjZVa3dEYVEyN00wRnJlYmN3K0NEUkd3SVhXVgpxekJ4b2ptaW94RTJ3dEtmODFOQ0JNV0IwMUtHdlFGc0lRVjZGd0h4dlZTZ1NWS2thUVptY0kveW4zV2trWlFvCk9pY0x4STNGeklpemFJK1NnUmpmM1E3WVBNTlhac3A0b3haM3VPRElmakluVzQwMG5UdFlOVkRsek1JK1hHcXMKNy9EL216OUlYZXBxVmt3WVMzR0hTVzZ3SWxXYTBKQXdMSWcxbFRZNElFNjk3SmVNZFY0QXhqdzBXNytWZEhGbgpxbjZzSEF5SUxuZjkvYkZaVzZTWHR6R3lQcEZNOGVNVjFqOWh2SCtNaCs3Tmd1djRjcEdkdWhkVUcxTzVLMDFTCnRMRnBDeG16VnBwQ09WMDdHK2hoT1RkUVo0NVlENTBCQWdNQkFBR2pZVEJmTUE0R0ExVWREd0VCL3dRRUF3SUMKcERBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFRWUlLd1lCQlFVSEF3SXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVUWRmVFZRTDRvSTBiT2FkeEU4K1Y2aThFa3ZBd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSzNWczNLNisxS2p1QzRzWTg3Y2l2UkJPUlpQWVVjQ3FMQndOeXJObGFsVkhpVGFEcEU3VnVGckZ5dFYKc1FCRlBOYWZBKzR4MzdKQkRhL0xsMDI2R2gvY3JxSE5iSlBGZHVUZ0lpdkZqVFRQdmpSZG9QODZmQmZzM2dKeQpDTDNYalludXZnUVExdU5TVmVrL1NpSTdiVVEvR0wwdjYrSFlKRklHUVJsWW15VklJQWF2VUQzTWRKdlUwTGpYCmRkYWpidG55TWxyQWIxMEhMVHkxbDJDSjd0WFNxS1MwcW00NkthNDlKZy9DbEhWclRIbWgxdVhVTEo3dVBlQW0KSFVNa0ZHZzE4eVBCcmhPN0Zma2p3ZlJaZUlrL0NYMnllS21WbThNeUJTSnVJaUxFZ25RYUx0dFlWd3FhekFqdwpOZTB4cm9XcDI0K05HUDNJeWVCNlVNbnpQbDg9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K

    service:
      name: aws-load-balancer-webhook-service
      namespace: kube-system
      path: /mutate-elbv2-k8s-aws-v1beta1-targetgroupbinding
  failurePolicy: Fail
  name: mtargetgroupbinding.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  rules:
  - apiGroups:
    - elbv2.k8s.aws
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - targetgroupbindings
  sideEffects: None
---
# Source: aws-load-balancer-controller/templates/webhook.yaml
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: aws-load-balancer-webhook
  labels:
    helm.sh/chart: aws-load-balancer-controller-1.7.1
    app.kubernetes.io/name: aws-load-balancer-controller
    app.kubernetes.io/instance: aws-load-balancer-controller
    app.kubernetes.io/version: "v2.7.1"
    app.kubernetes.io/managed-by: Helm
webhooks:
- clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lSQVAvUjhyNU12WXFZUHdmeEd6cXhETm93RFFZSktvWklodmNOQVFFTEJRQXcKS2pFb01DWUdBMVVFQXhNZllYZHpMV3h2WVdRdFltRnNZVzVqWlhJdFkyOXVkSEp2Ykd4bGNpMWpZVEFlRncweQpOREF6TURVd09UQXhORFJhRncwek5EQXpNRE13T1RBeE5EUmFNQ294S0RBbUJnTlZCQU1USDJGM2N5MXNiMkZrCkxXSmhiR0Z1WTJWeUxXTnZiblJ5YjJ4c1pYSXRZMkV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXcKZ2dFS0FvSUJBUUREU2pkTjhwYUNVSkxZeXJXaUdxTEdTanBxTjZVa3dEYVEyN00wRnJlYmN3K0NEUkd3SVhXVgpxekJ4b2ptaW94RTJ3dEtmODFOQ0JNV0IwMUtHdlFGc0lRVjZGd0h4dlZTZ1NWS2thUVptY0kveW4zV2trWlFvCk9pY0x4STNGeklpemFJK1NnUmpmM1E3WVBNTlhac3A0b3haM3VPRElmakluVzQwMG5UdFlOVkRsek1JK1hHcXMKNy9EL216OUlYZXBxVmt3WVMzR0hTVzZ3SWxXYTBKQXdMSWcxbFRZNElFNjk3SmVNZFY0QXhqdzBXNytWZEhGbgpxbjZzSEF5SUxuZjkvYkZaVzZTWHR6R3lQcEZNOGVNVjFqOWh2SCtNaCs3Tmd1djRjcEdkdWhkVUcxTzVLMDFTCnRMRnBDeG16VnBwQ09WMDdHK2hoT1RkUVo0NVlENTBCQWdNQkFBR2pZVEJmTUE0R0ExVWREd0VCL3dRRUF3SUMKcERBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFRWUlLd1lCQlFVSEF3SXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVUWRmVFZRTDRvSTBiT2FkeEU4K1Y2aThFa3ZBd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSzNWczNLNisxS2p1QzRzWTg3Y2l2UkJPUlpQWVVjQ3FMQndOeXJObGFsVkhpVGFEcEU3VnVGckZ5dFYKc1FCRlBOYWZBKzR4MzdKQkRhL0xsMDI2R2gvY3JxSE5iSlBGZHVUZ0lpdkZqVFRQdmpSZG9QODZmQmZzM2dKeQpDTDNYalludXZnUVExdU5TVmVrL1NpSTdiVVEvR0wwdjYrSFlKRklHUVJsWW15VklJQWF2VUQzTWRKdlUwTGpYCmRkYWpidG55TWxyQWIxMEhMVHkxbDJDSjd0WFNxS1MwcW00NkthNDlKZy9DbEhWclRIbWgxdVhVTEo3dVBlQW0KSFVNa0ZHZzE4eVBCcmhPN0Zma2p3ZlJaZUlrL0NYMnllS21WbThNeUJTSnVJaUxFZ25RYUx0dFlWd3FhekFqdwpOZTB4cm9XcDI0K05HUDNJeWVCNlVNbnpQbDg9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K

    service:
      name: aws-load-balancer-webhook-service
      namespace: kube-system
      path: /validate-elbv2-k8s-aws-v1beta1-ingressclassparams
  failurePolicy: Fail
  name: vingressclassparams.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  objectSelector:
    matchExpressions:
    - key: app.kubernetes.io/name
      operator: NotIn
      values:
      - aws-load-balancer-controller
  rules:
  - apiGroups:
    - elbv2.k8s.aws
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - ingressclassparams
  sideEffects: None
- clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lSQVAvUjhyNU12WXFZUHdmeEd6cXhETm93RFFZSktvWklodmNOQVFFTEJRQXcKS2pFb01DWUdBMVVFQXhNZllYZHpMV3h2WVdRdFltRnNZVzVqWlhJdFkyOXVkSEp2Ykd4bGNpMWpZVEFlRncweQpOREF6TURVd09UQXhORFJhRncwek5EQXpNRE13T1RBeE5EUmFNQ294S0RBbUJnTlZCQU1USDJGM2N5MXNiMkZrCkxXSmhiR0Z1WTJWeUxXTnZiblJ5YjJ4c1pYSXRZMkV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXcKZ2dFS0FvSUJBUUREU2pkTjhwYUNVSkxZeXJXaUdxTEdTanBxTjZVa3dEYVEyN00wRnJlYmN3K0NEUkd3SVhXVgpxekJ4b2ptaW94RTJ3dEtmODFOQ0JNV0IwMUtHdlFGc0lRVjZGd0h4dlZTZ1NWS2thUVptY0kveW4zV2trWlFvCk9pY0x4STNGeklpemFJK1NnUmpmM1E3WVBNTlhac3A0b3haM3VPRElmakluVzQwMG5UdFlOVkRsek1JK1hHcXMKNy9EL216OUlYZXBxVmt3WVMzR0hTVzZ3SWxXYTBKQXdMSWcxbFRZNElFNjk3SmVNZFY0QXhqdzBXNytWZEhGbgpxbjZzSEF5SUxuZjkvYkZaVzZTWHR6R3lQcEZNOGVNVjFqOWh2SCtNaCs3Tmd1djRjcEdkdWhkVUcxTzVLMDFTCnRMRnBDeG16VnBwQ09WMDdHK2hoT1RkUVo0NVlENTBCQWdNQkFBR2pZVEJmTUE0R0ExVWREd0VCL3dRRUF3SUMKcERBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFRWUlLd1lCQlFVSEF3SXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVUWRmVFZRTDRvSTBiT2FkeEU4K1Y2aThFa3ZBd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSzNWczNLNisxS2p1QzRzWTg3Y2l2UkJPUlpQWVVjQ3FMQndOeXJObGFsVkhpVGFEcEU3VnVGckZ5dFYKc1FCRlBOYWZBKzR4MzdKQkRhL0xsMDI2R2gvY3JxSE5iSlBGZHVUZ0lpdkZqVFRQdmpSZG9QODZmQmZzM2dKeQpDTDNYalludXZnUVExdU5TVmVrL1NpSTdiVVEvR0wwdjYrSFlKRklHUVJsWW15VklJQWF2VUQzTWRKdlUwTGpYCmRkYWpidG55TWxyQWIxMEhMVHkxbDJDSjd0WFNxS1MwcW00NkthNDlKZy9DbEhWclRIbWgxdVhVTEo3dVBlQW0KSFVNa0ZHZzE4eVBCcmhPN0Zma2p3ZlJaZUlrL0NYMnllS21WbThNeUJTSnVJaUxFZ25RYUx0dFlWd3FhekFqdwpOZTB4cm9XcDI0K05HUDNJeWVCNlVNbnpQbDg9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K

    service:
      name: aws-load-balancer-webhook-service
      namespace: kube-system
      path: /validate-elbv2-k8s-aws-v1beta1-targetgroupbinding
  failurePolicy: Fail
  name: vtargetgroupbinding.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  rules:
  - apiGroups:
    - elbv2.k8s.aws
    apiVersions:
    - v1beta1
    operations:
    - CREATE
    - UPDATE
    resources:
    - targetgroupbindings
  sideEffects: None
- clientConfig:
    caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURRRENDQWlpZ0F3SUJBZ0lSQVAvUjhyNU12WXFZUHdmeEd6cXhETm93RFFZSktvWklodmNOQVFFTEJRQXcKS2pFb01DWUdBMVVFQXhNZllYZHpMV3h2WVdRdFltRnNZVzVqWlhJdFkyOXVkSEp2Ykd4bGNpMWpZVEFlRncweQpOREF6TURVd09UQXhORFJhRncwek5EQXpNRE13T1RBeE5EUmFNQ294S0RBbUJnTlZCQU1USDJGM2N5MXNiMkZrCkxXSmhiR0Z1WTJWeUxXTnZiblJ5YjJ4c1pYSXRZMkV3Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXcKZ2dFS0FvSUJBUUREU2pkTjhwYUNVSkxZeXJXaUdxTEdTanBxTjZVa3dEYVEyN00wRnJlYmN3K0NEUkd3SVhXVgpxekJ4b2ptaW94RTJ3dEtmODFOQ0JNV0IwMUtHdlFGc0lRVjZGd0h4dlZTZ1NWS2thUVptY0kveW4zV2trWlFvCk9pY0x4STNGeklpemFJK1NnUmpmM1E3WVBNTlhac3A0b3haM3VPRElmakluVzQwMG5UdFlOVkRsek1JK1hHcXMKNy9EL216OUlYZXBxVmt3WVMzR0hTVzZ3SWxXYTBKQXdMSWcxbFRZNElFNjk3SmVNZFY0QXhqdzBXNytWZEhGbgpxbjZzSEF5SUxuZjkvYkZaVzZTWHR6R3lQcEZNOGVNVjFqOWh2SCtNaCs3Tmd1djRjcEdkdWhkVUcxTzVLMDFTCnRMRnBDeG16VnBwQ09WMDdHK2hoT1RkUVo0NVlENTBCQWdNQkFBR2pZVEJmTUE0R0ExVWREd0VCL3dRRUF3SUMKcERBZEJnTlZIU1VFRmpBVUJnZ3JCZ0VGQlFjREFRWUlLd1lCQlFVSEF3SXdEd1lEVlIwVEFRSC9CQVV3QXdFQgovekFkQmdOVkhRNEVGZ1FVUWRmVFZRTDRvSTBiT2FkeEU4K1Y2aThFa3ZBd0RRWUpLb1pJaHZjTkFRRUxCUUFECmdnRUJBSzNWczNLNisxS2p1QzRzWTg3Y2l2UkJPUlpQWVVjQ3FMQndOeXJObGFsVkhpVGFEcEU3VnVGckZ5dFYKc1FCRlBOYWZBKzR4MzdKQkRhL0xsMDI2R2gvY3JxSE5iSlBGZHVUZ0lpdkZqVFRQdmpSZG9QODZmQmZzM2dKeQpDTDNYalludXZnUVExdU5TVmVrL1NpSTdiVVEvR0wwdjYrSFlKRklHUVJsWW15VklJQWF2VUQzTWRKdlUwTGpYCmRkYWpidG55TWxyQWIxMEhMVHkxbDJDSjd0WFNxS1MwcW00NkthNDlKZy9DbEhWclRIbWgxdVhVTEo3dVBlQW0KSFVNa0ZHZzE4eVBCcmhPN0Zma2p3ZlJaZUlrL0NYMnllS21WbThNeUJTSnVJaUxFZ25RYUx0dFlWd3FhekFqdwpOZTB4cm9XcDI0K05HUDNJeWVCNlVNbnpQbDg9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K

    service:
      name: aws-load-balancer-webhook-service
      namespace: kube-system
      path: /validate-networking-v1-ingress
  failurePolicy: Fail
  matchPolicy: Equivalent
  name: vingress.elbv2.k8s.aws
  admissionReviewVersions:
  - v1beta1
  rules:
  - apiGroups:
    - networking.k8s.io
    apiVersions:
    - v1
    operations:
    - CREATE
    - UPDATE
    resources:
    - ingresses
  sideEffects: None
---
#Creation of IngressClassParams CRD 
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.1
  creationTimestamp: null
  name: ingressclassparams.elbv2.k8s.aws
spec:
  group: elbv2.k8s.aws
  names:
    kind: IngressClassParams
    listKind: IngressClassParamsList
    plural: ingressclassparams
    singular: ingressclassparams
  scope: Cluster
  versions:
  - additionalPrinterColumns:
    - description: The Ingress Group name
      jsonPath: .spec.group.name
      name: GROUP-NAME
      type: string
    - description: The AWS Load Balancer scheme
      jsonPath: .spec.scheme
      name: SCHEME
      type: string
    - description: The AWS Load Balancer ipAddressType
      jsonPath: .spec.ipAddressType
      name: IP-ADDRESS-TYPE
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: IngressClassParams is the Schema for the IngressClassParams API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: IngressClassParamsSpec defines the desired state of IngressClassParams
            properties:
              group:
                description: Group defines the IngressGroup for all Ingresses that
                  belong to IngressClass with this IngressClassParams.
                properties:
                  name:
                    description: Name is the name of IngressGroup.
                    type: string
                required:
                - name
                type: object
              inboundCIDRs:
                description: InboundCIDRs specifies the CIDRs that are allowed to
                  access the Ingresses that belong to IngressClass with this IngressClassParams.
                items:
                  type: string
                type: array
              ipAddressType:
                description: IPAddressType defines the ip address type for all Ingresses
                  that belong to IngressClass with this IngressClassParams.
                enum:
                - ipv4
                - dualstack
                type: string
              loadBalancerAttributes:
                description: LoadBalancerAttributes define the custom attributes to
                  LoadBalancers for all Ingress that that belong to IngressClass with
                  this IngressClassParams.
                items:
                  description: Attributes defines custom attributes on resources.
                  properties:
                    key:
                      description: The key of the attribute.
                      type: string
                    value:
                      description: The value of the attribute.
                      type: string
                  required:
                  - key
                  - value
                  type: object
                type: array
              namespaceSelector:
                description: NamespaceSelector restrict the namespaces of Ingresses
                  that are allowed to specify the IngressClass with this IngressClassParams.
                  * if absent or present but empty, it selects all namespaces.
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              scheme:
                description: Scheme defines the scheme for all Ingresses that belong
                  to IngressClass with this IngressClassParams.
                enum:
                - internal
                - internet-facing
                type: string
              sslPolicy:
                description: SSLPolicy specifies the SSL Policy for all Ingresses
                  that belong to IngressClass with this IngressClassParams.
                type: string
              subnets:
                description: Subnets defines the subnets for all Ingresses that belong
                  to IngressClass with this IngressClassParams.
                properties:
                  ids:
                    description: IDs specify the resource IDs of subnets. Exactly
                      one of this or `tags` must be specified.
                    items:
                      description: SubnetID specifies a subnet ID.
                      pattern: subnet-[0-9a-f]+
                      type: string
                    minItems: 1
                    type: array
                  tags:
                    additionalProperties:
                      items:
                        type: string
                      type: array
                    description: Tags specifies subnets in the load balancer's VPC
                      where each tag specified in the map key contains one of the
                      values in the corresponding value list. Exactly one of this
                      or `ids` must be specified.
                    type: object
                type: object
              tags:
                description: Tags defines list of Tags on AWS resources provisioned
                  for Ingresses that belong to IngressClass with this IngressClassParams.
                items:
                  description: Tag defines a AWS Tag on resources.
                  properties:
                    key:
                      description: The key of the tag.
                      type: string
                    value:
                      description: The value of the tag.
                      type: string
                  required:
                  - key
                  - value
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources: {}
---
#Creation of TargetGroupBinding CRD
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.11.1
  creationTimestamp: null
  name: targetgroupbindings.elbv2.k8s.aws
spec:
  group: elbv2.k8s.aws
  names:
    kind: TargetGroupBinding
    listKind: TargetGroupBindingList
    plural: targetgroupbindings
    singular: targetgroupbinding
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - description: The Kubernetes Service's name
      jsonPath: .spec.serviceRef.name
      name: SERVICE-NAME
      type: string
    - description: The Kubernetes Service's port
      jsonPath: .spec.serviceRef.port
      name: SERVICE-PORT
      type: string
    - description: The AWS TargetGroup's TargetType
      jsonPath: .spec.targetType
      name: TARGET-TYPE
      type: string
    - description: The AWS TargetGroup's Amazon Resource Name
      jsonPath: .spec.targetGroupARN
      name: ARN
      priority: 1
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: TargetGroupBinding is the Schema for the TargetGroupBinding API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: TargetGroupBindingSpec defines the desired state of TargetGroupBinding
            properties:
              networking:
                description: networking provides the networking setup for ELBV2 LoadBalancer
                  to access targets in TargetGroup.
                properties:
                  ingress:
                    description: List of ingress rules to allow ELBV2 LoadBalancer
                      to access targets in TargetGroup.
                    items:
                      properties:
                        from:
                          description: List of peers which should be able to access
                            the targets in TargetGroup. At least one NetworkingPeer
                            should be specified.
                          items:
                            description: NetworkingPeer defines the source/destination
                              peer for networking rules.
                            properties:
                              ipBlock:
                                description: IPBlock defines an IPBlock peer. If specified,
                                  none of the other fields can be set.
                                properties:
                                  cidr:
                                    description: CIDR is the network CIDR. Both IPV4
                                      or IPV6 CIDR are accepted.
                                    type: string
                                required:
                                - cidr
                                type: object
                              securityGroup:
                                description: SecurityGroup defines a SecurityGroup
                                  peer. If specified, none of the other fields can
                                  be set.
                                properties:
                                  groupID:
                                    description: GroupID is the EC2 SecurityGroupID.
                                    type: string
                                required:
                                - groupID
                                type: object
                            type: object
                          type: array
                        ports:
                          description: List of ports which should be made accessible
                            on the targets in TargetGroup. If ports is empty or unspecified,
                            it defaults to all ports with TCP.
                          items:
                            properties:
                              port:
                                anyOf:
                                - type: integer
                                - type: string
                                description: The port which traffic must match. When
                                  NodePort endpoints(instance TargetType) is used,
                                  this must be a numerical port. When Port endpoints(ip
                                  TargetType) is used, this can be either numerical
                                  or named port on pods. if port is unspecified, it
                                  defaults to all ports.
                                x-kubernetes-int-or-string: true
                              protocol:
                                description: The protocol which traffic must match.
                                  If protocol is unspecified, it defaults to TCP.
                                enum:
                                - TCP
                                - UDP
                                type: string
                            type: object
                          type: array
                      required:
                      - from
                      - ports
                      type: object
                    type: array
                type: object
              serviceRef:
                description: serviceRef is a reference to a Kubernetes Service and
                  ServicePort.
                properties:
                  name:
                    description: Name is the name of the Service.
                    type: string
                  port:
                    anyOf:
                    - type: integer
                    - type: string
                    description: Port is the port of the ServicePort.
                    x-kubernetes-int-or-string: true
                required:
                - name
                - port
                type: object
              targetGroupARN:
                description: targetGroupARN is the Amazon Resource Name (ARN) for
                  the TargetGroup.
                type: string
              targetType:
                description: targetType is the TargetType of TargetGroup. If unspecified,
                  it will be automatically inferred.
                enum:
                - instance
                - ip
                type: string
            required:
            - serviceRef
            - targetGroupARN
            type: object
          status:
            description: TargetGroupBindingStatus defines the observed state of TargetGroupBinding
            properties:
              observedGeneration:
                description: The generation observed by the TargetGroupBinding controller.
                format: int64
                type: integer
            type: object
        type: object
    served: true
    storage: false
    subresources:
      status: {}
  - additionalPrinterColumns:
    - description: The Kubernetes Service's name
      jsonPath: .spec.serviceRef.name
      name: SERVICE-NAME
      type: string
    - description: The Kubernetes Service's port
      jsonPath: .spec.serviceRef.port
      name: SERVICE-PORT
      type: string
    - description: The AWS TargetGroup's TargetType
      jsonPath: .spec.targetType
      name: TARGET-TYPE
      type: string
    - description: The AWS TargetGroup's Amazon Resource Name
      jsonPath: .spec.targetGroupARN
      name: ARN
      priority: 1
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: AGE
      type: date
    name: v1beta1
    schema:
      openAPIV3Schema:
        description: TargetGroupBinding is the Schema for the TargetGroupBinding API
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
          spec:
            description: TargetGroupBindingSpec defines the desired state of TargetGroupBinding
            properties:
              ipAddressType:
                description: ipAddressType specifies whether the target group is of
                  type IPv4 or IPv6. If unspecified, it will be automatically inferred.
                enum:
                - ipv4
                - ipv6
                type: string
              networking:
                description: networking defines the networking rules to allow ELBV2
                  LoadBalancer to access targets in TargetGroup.
                properties:
                  ingress:
                    description: List of ingress rules to allow ELBV2 LoadBalancer
                      to access targets in TargetGroup.
                    items:
                      description: NetworkingIngressRule defines a particular set
                        of traffic that is allowed to access TargetGroup's targets.
                      properties:
                        from:
                          description: List of peers which should be able to access
                            the targets in TargetGroup. At least one NetworkingPeer
                            should be specified.
                          items:
                            description: NetworkingPeer defines the source/destination
                              peer for networking rules.
                            properties:
                              ipBlock:
                                description: IPBlock defines an IPBlock peer. If specified,
                                  none of the other fields can be set.
                                properties:
                                  cidr:
                                    description: CIDR is the network CIDR. Both IPV4
                                      or IPV6 CIDR are accepted.
                                    type: string
                                required:
                                - cidr
                                type: object
                              securityGroup:
                                description: SecurityGroup defines a SecurityGroup
                                  peer. If specified, none of the other fields can
                                  be set.
                                properties:
                                  groupID:
                                    description: GroupID is the EC2 SecurityGroupID.
                                    type: string
                                required:
                                - groupID
                                type: object
                            type: object
                          type: array
                        ports:
                          description: List of ports which should be made accessible
                            on the targets in TargetGroup. If ports is empty or unspecified,
                            it defaults to all ports with TCP.
                          items:
                            description: NetworkingPort defines the port and protocol
                              for networking rules.
                            properties:
                              port:
                                anyOf:
                                - type: integer
                                - type: string
                                description: The port which traffic must match. When
                                  NodePort endpoints(instance TargetType) is used,
                                  this must be a numerical port. When Port endpoints(ip
                                  TargetType) is used, this can be either numerical
                                  or named port on pods. if port is unspecified, it
                                  defaults to all ports.
                                x-kubernetes-int-or-string: true
                              protocol:
                                description: The protocol which traffic must match.
                                  If protocol is unspecified, it defaults to TCP.
                                enum:
                                - TCP
                                - UDP
                                type: string
                            type: object
                          type: array
                      required:
                      - from
                      - ports
                      type: object
                    type: array
                type: object
              nodeSelector:
                description: node selector for instance type target groups to only
                  register certain nodes
                properties:
                  matchExpressions:
                    description: matchExpressions is a list of label selector requirements.
                      The requirements are ANDed.
                    items:
                      description: A label selector requirement is a selector that
                        contains values, a key, and an operator that relates the key
                        and values.
                      properties:
                        key:
                          description: key is the label key that the selector applies
                            to.
                          type: string
                        operator:
                          description: operator represents a key's relationship to
                            a set of values. Valid operators are In, NotIn, Exists
                            and DoesNotExist.
                          type: string
                        values:
                          description: values is an array of string values. If the
                            operator is In or NotIn, the values array must be non-empty.
                            If the operator is Exists or DoesNotExist, the values
                            array must be empty. This array is replaced during a strategic
                            merge patch.
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    description: matchLabels is a map of {key,value} pairs. A single
                      {key,value} in the matchLabels map is equivalent to an element
                      of matchExpressions, whose key field is "key", the operator
                      is "In", and the values array contains only "value". The requirements
                      are ANDed.
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              serviceRef:
                description: serviceRef is a reference to a Kubernetes Service and
                  ServicePort.
                properties:
                  name:
                    description: Name is the name of the Service.
                    type: string
                  port:
                    anyOf:
                    - type: integer
                    - type: string
                    description: Port is the port of the ServicePort.
                    x-kubernetes-int-or-string: true
                required:
                - name
                - port
                type: object
              targetGroupARN:
                description: targetGroupARN is the Amazon Resource Name (ARN) for
                  the TargetGroup.
                minLength: 1
                type: string
              targetType:
                description: targetType is the TargetType of TargetGroup. If unspecified,
                  it will be automatically inferred.
                enum:
                - instance
                - ip
                type: string
            required:
            - serviceRef
            - targetGroupARN
            type: object
          status:
            description: TargetGroupBindingStatus defines the observed state of TargetGroupBinding
            properties:
              observedGeneration:
                description: The generation observed by the TargetGroupBinding controller.
                format: int64
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}