import os
import pandas as pd
from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from utils.postgres.adaptor import PostgresAdaptor

from bloomberg.per_security.parser import BloombergParser
from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.date_utils import get_today

if __name__ == "__main__":
    pg_adaptor = PostgresAdaptor(
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df_etfs = pg_adaptor.execute_query(
        """select distinct bbg_full_ticker from eqvol.sr_security_ref where security_type  IN ('ETF', 'STOCK')""",
    )

    securities = df_etfs["bbg_full_ticker"].tolist()
    per_sec_req_type = PerSecurityRequestType.getdata
    batch_name = "dp_eqvol2"
    request_dict = {
        "firm_name": "dl47544",
        "program_flag": "adhoc",
        "sec_id": "TICKER",
        "output_format": "bulklist",
        "fields": ["DVD_HIST_ALL"],
        "securities": securities,
    }
    target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/eqvol/dvd_hist/"
    dvd_hist_file_path = PerSecurityRequestRunner(
        batch_name, per_sec_req_type, request_dict, target_folder
    ).run(True)
    print(dvd_hist_file_path)

    as_of_date = get_today()
    parser = BloombergParser(
        dvd_hist_file_path, sep="|", skipinitialspace=True, on_bad_lines="error"
    )
    df_data = parser.parse_data()
    df_data["BC_EQY_DVD_HIST_ALL_ANN_DT"] = pd.to_datetime(
        df_data["BC_EQY_DVD_HIST_ALL_ANN_DT"], format="%m/%d/%Y", errors="coerce"
    )
    df_data["BC_EQY_DVD_HIST_ALL_EX_DT"] = pd.to_datetime(
        df_data["BC_EQY_DVD_HIST_ALL_EX_DT"], format="%m/%d/%Y", errors="coerce"
    )
    df_data["BC_EQY_DVD_HIST_ALL_REC_DT"] = pd.to_datetime(
        df_data["BC_EQY_DVD_HIST_ALL_REC_DT"], format="%m/%d/%Y", errors="coerce"
    )
    df_data["BC_EQY_DVD_HIST_ALL_PAY_DT"] = pd.to_datetime(
        df_data["BC_EQY_DVD_HIST_ALL_PAY_DT"], format="%m/%d/%Y", errors="coerce"
    )
    df_data["BC_EQY_DVD_HIST_ALL_AMT"] = pd.to_numeric(
        df_data["BC_EQY_DVD_HIST_ALL_AMT"], errors="coerce"
    )
    df_data["SECURITY"] = df_data["SECURITY"].str.strip()
    df_data["AS_OF_DATE"] = as_of_date
    df_data.rename(columns={"SECURITY": "BBG_TICKER"}, inplace=True)

    for col in df_data.columns:
        if pd.api.types.is_datetime64_any_dtype(df_data[col]):
            df_data[col] = df_data[col].apply(
                lambda x: x.strftime("%Y-%m-%d") if pd.notna(x) else None
            )

    target_schema = "BBGH_ONDEMAND"
    target_table = "BBG_EQVOL_DVD_HIST"
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    as_of_date = as_of_date.strftime("%Y-%m-%d")
    sf_adaptor.execute_query(
        "BBGH_ONDEMAND",
        f"delete from {target_schema}.{target_table} where AS_OF_DATE = '{as_of_date}'",
    )
    sf_adaptor.write_pandas_dataframe(target_schema, df_data, target_table)

    df_data.rename(
        columns={
            "BBG_TICKER": "bbg_ticker",
            "AS_OF_DATE": "as_of_date",
            "BC_EQY_DVD_HIST_ALL_ANN_DT": "bc_eqy_dvd_hist_all_ann_dt",
            "BC_EQY_DVD_HIST_ALL_EX_DT": "bc_eqy_dvd_hist_all_ex_dt",
            "BC_EQY_DVD_HIST_ALL_REC_DT": "bc_eqy_dvd_hist_all_rec_dt",
            "BC_EQY_DVD_HIST_ALL_PAY_DT": "bc_eqy_dvd_hist_all_pay_dt",
            "BC_EQY_DVD_HIST_ALL_AMT": "bc_eqy_dvd_hist_all_amt",
            "CP_DVD_TYP": "cp_dvd_typ",
            "DVD_FREQ": "dvd_freq",
        },
        inplace=True,
    )

    pg_adaptor.execute_query_no_ret(
        f"delete from eqvol.bbg_eqvol_dvd_hist_staging where as_of_date = '{as_of_date}'"
    )
    success = pg_adaptor.load_dataframe(
        df_data, "bbg_eqvol_dvd_hist_staging", if_exists="append"
    )

    if success:
        print("Successfully loaded data to Postgres eqvol.bbg_eqvol_dvd_hist_staging.")
    else:
        raise ValueError(
            "Failed to load data to Postgres eqvol.bbg_eqvol_dvd_hist_staging."
        )