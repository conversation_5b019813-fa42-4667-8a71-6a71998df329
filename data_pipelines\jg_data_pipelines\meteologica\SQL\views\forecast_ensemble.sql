create or replace view vw_forecast_ensemble as
with consolidated as (
    SELECT * FROM meteologica.api.forecast_ensemble
    WHERE issue_date < DATE('2025-01-01')
    UNION ALL 
    SELECT * FROM meteologica.ongoing.forecast_ensemble
    WHERE issue_date >= DATE('2025-01-01')
), forecast_ensemble as(
    select content_id, content_name, issue_date, timezone, unit, installed_capacity, update_id, source_file,
        to_varchar(data:"From yyyy-mm-dd hh:mm") from_datetime,
        to_varchar(data:"To yyyy-mm-dd hh:mm") to_datetime,
        to_varchar(data:"UTC offset from (UTC+/-hhmm)") utc_offset_from,
        to_varchar(data:"UTC offset to (UTC+/-hhmm)") utc_offset_to,

        to_varchar(data:"Average") average,
        to_varchar(data:"Bottom") bottom,
        to_varchar(data:"Top") top,
        to_varchar(data:"ENS00") ENS00,
        to_varchar(data:"ENS01") ENS01,
        to_varchar(data:"ENS02") ENS02,
        to_varchar(data:"ENS03") ENS03,
        to_varchar(data:"ENS04") ENS04,
        to_varchar(data:"ENS05") ENS05,
        to_varchar(data:"ENS06") ENS06,
        to_varchar(data:"ENS07") ENS07,
        to_varchar(data:"ENS08") ENS08,
        to_varchar(data:"ENS09") ENS09,
        to_varchar(data:"ENS10") ENS10,
        to_varchar(data:"ENS11") ENS11,
        to_varchar(data:"ENS12") ENS12,
        to_varchar(data:"ENS13") ENS13,
        to_varchar(data:"ENS14") ENS14,
        to_varchar(data:"ENS15") ENS15,
        to_varchar(data:"ENS16") ENS16,
        to_varchar(data:"ENS17") ENS17,
        to_varchar(data:"ENS18") ENS18,
        to_varchar(data:"ENS19") ENS19,
        to_varchar(data:"ENS20") ENS20,
        to_varchar(data:"ENS21") ENS21,
        to_varchar(data:"ENS22") ENS22,
        to_varchar(data:"ENS23") ENS23,
        to_varchar(data:"ENS24") ENS24,
        to_varchar(data:"ENS25") ENS25,
        to_varchar(data:"ENS26") ENS26,
        to_varchar(data:"ENS27") ENS27,
        to_varchar(data:"ENS28") ENS28,
        to_varchar(data:"ENS29") ENS29,
        to_varchar(data:"ENS30") ENS30,
        to_varchar(data:"ENS31") ENS31,
        to_varchar(data:"ENS32") ENS32,
        to_varchar(data:"ENS33") ENS33,
        to_varchar(data:"ENS34") ENS34,
        to_varchar(data:"ENS35") ENS35,
        to_varchar(data:"ENS36") ENS36,
        to_varchar(data:"ENS37") ENS37,
        to_varchar(data:"ENS38") ENS38,
        to_varchar(data:"ENS39") ENS39,
        to_varchar(data:"ENS40") ENS40,
        to_varchar(data:"ENS41") ENS41,
        to_varchar(data:"ENS42") ENS42,
        to_varchar(data:"ENS43") ENS43,
        to_varchar(data:"ENS44") ENS44,
        to_varchar(data:"ENS45") ENS45,
        to_varchar(data:"ENS46") ENS46,
        to_varchar(data:"ENS47") ENS47,
        to_varchar(data:"ENS48") ENS48,
        to_varchar(data:"ENS49") ENS49,
        to_varchar(data:"ENS50") ENS50
        from consolidated
), forecast_ensemble_utc as (
    select content_id, content_name, 
    issue_date, 
    unit, installed_capacity, update_id, source_file,
    to_timestamp_ntz(convert_timezone(
            'UTC', 
            TO_TIMESTAMP_TZ(FROM_DATETIME || ' ' || split(utc_offset_from, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
        )) FROM_DATETIME,
    to_timestamp_ntz(convert_timezone(
            'UTC', 
            TO_TIMESTAMP_TZ(TO_DATETIME || ' ' || split(utc_offset_to, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
        )) TO_DATETIME,
        average, bottom, top,
        ENS00,
        ENS01,
        ENS02,
        ENS03,
        ENS04,
        ENS05,
        ENS06,
        ENS07,
        ENS08,
        ENS09,
        ENS10,
        ENS11,
        ENS12,
        ENS13,
        ENS14,
        ENS15,
        ENS16,
        ENS17,
        ENS18,
        ENS19,
        ENS20,
        ENS21,
        ENS22,
        ENS23,
        ENS24,
        ENS25,
        ENS26,
        ENS27,
        ENS28,
        ENS29,
        ENS30,
        ENS31,
        ENS32,
        ENS33,
        ENS34,
        ENS35,
        ENS36,
        ENS37,
        ENS38,
        ENS39,
        ENS40,
        ENS41,
        ENS42,
        ENS43,
        ENS44,
        ENS45,
        ENS46,
        ENS47,
        ENS48,
        ENS49,
        ENS50
        from forecast_ensemble
)
select * from forecast_ensemble_utc;

--140231262
select count(*) from vw_forecast_ensemble limit 10;
select * from vw_forecast_ensemble limit 10;
select * from meteologica.api.forecast_ensemble limit 10;


