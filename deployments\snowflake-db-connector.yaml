apiVersion: v1
kind: Pod
metadata:
  name: database-connector-agent
spec:
  restartPolicy: Always
  containers:
    - name: database-connector-agent
      #image: snowflakedb/database-connector-agent:latest
      image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-database-connector-agent-${ENV}:latest
      resources:
        requests:
          memory: "6Gi"
        limits:
          memory: "8Gi"
      volumeMounts:
        - name: config
          mountPath: /home/<USER>/snowflake.json
          subPath: snowflake.json
        - name: config
          mountPath: /home/<USER>/.postgresql/root.crt
          subPath: root.crt
        - name: secrets
          mountPath: /home/<USER>/datasources.json
          subPath: datasources.json
        - name: secrets
          mountPath: /etc/private-key/private-key
          subPath: private-key
      env:
        - name: SNOWFLAKE_PRIVATEKEYPATH
          value: /etc/private-key/private-key
  volumes:
    - name: config
      configMap:
        name: database-connector-config
    - name: secrets
      secret:
        secretName: database-connector-secrets