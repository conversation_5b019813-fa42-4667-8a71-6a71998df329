#!/bin/bash

# Define an array of process names or specific arguments to monitor
processes=(
    "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/bin/mlink_batch_consumer.py --sr_msgname OptionNbboQuote"
    "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/bin/mlink_batch_consumer.py --sr_msgname StockBookQuote"
    "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/bin/bpipe-streaming-ticks.py"
    "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/bin/bpipe-bar-creater.py bpipe"
    "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/bin/tp_icap_connect.py"
)

# Remote server details
SSH_KEY="/tmp/private-key.pem"
OUTPUT_FILE="/etc/node_exporter/process_status.prom"
server_IP="*************"

while true; do
    # Clear the file before each iteration
    echo "" > $OUTPUT_FILE

    # Loop through each process and check if it's running on the remote server
    for process in "${processes[@]}"
    do
        echo "Checking process: $process"

        # Use pgrep with -a to check for the process and exclude the grep command itself
        if ! ssh -i "$SSH_KEY" jsvc-datait@$server_IP "pgrep -a -f \"$process\" | grep -v 'grep' > /dev/null"; then
            # If process is down
            echo "process_down{process=\"$process\",status=\"down\"} 1" >> $OUTPUT_FILE
            echo "Process is DOWN: $process"
        else
            # If process is up
            echo "process_down{process=\"$process\",status=\"up\"} 0" >> $OUTPUT_FILE
            echo "Process is UP: $process"
        fi
    done
    echo "Status written to $OUTPUT_FILE"
    echo "Monitoring complete. Sleeping for 5 seconds..."
    sleep 5
done
