import json
import requests
import time
from utils.snowflake.adaptor import SnowflakeAdaptor

# while True:
#     url = "https://dataservice.jainglobal.net/bpipe/get-position-intraday"
#     payload = {}
#     start_time = time.time()
#     response = requests.post(url, json=payload, headers={"api_key": ""})
#     elapsed_time = time.time() - start_time

#     if response.status_code == 200:
#         # print("Response : " + json.dumps(response.json()))
#         print(f"Time taken in {elapsed_time:.2f} seconds")
#     else:
#         print(f"Error : " + json.dumps(response.json()))

#     time.sleep(5)
    



# while True:
#     url = "https://dataservice.jainglobal.net/bpipe/reference_data_request"
#     payload = {
#         "securities": ["NVDA US Equity"],
#         "fields": [{"name": "LAST_ALL_SESSIONS", "mapping_name": "LAST_ALL_SESSIONS"}],
#         "bps_flag": True, #enable enhanced entitlements
#     }
#     response = requests.post(url, json=payload, headers={"api_key": "<api_key>"})

#     if response.status_code == 200:
#         print("Response : " + json.dumps(response.json()))
#     else:
#         print(f"Error : " + json.dumps(response.json()))

#     time.sleep(5)   

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(database="JG_SNP", warehouse="EQDELTAONE_WH", role="FR_EQDELTAONE")
    etfs_of_interest = ['ARKK','ARKG','GDX','GDXJ','LQD','HYG','IEF','TLT','SLV','XRT','XBI','XLI','SMH','EWW','EWC','IBIT','EWY','INDA','XLB','XLC','XLRE','JETS','ASHR','FEZ','IGV','KRE','BKLN','SPY','QQQ','IWM','EEM','EFA','FXI','KWEB','EWJ','DXJ','EWT','EWZ','USO','GLD','XLE','XLF','XLK','XLP','XLU','XLV','XLY','IYR','TAN']

    etfs_of_interest_str = ','.join([f"'{etf}'" for etf in etfs_of_interest])

    query = f"""
        select etd.business_date,
        holdings.ASAT_DATE,
        holdings.NosOfConstituents,
        CASE WHEN holdings.NosOfConstituents IS NULL THEN True ELSE FALSE END AS IsMissing
        from COPPCLARK.HDS.EXCHANGE_TRADING_DAYS etd left join
        (
            select SECURITY_ID, ASAT_DATE, COUNT(1) AS NosOfConstituents
            from JG_SNP.ETF.LATEST_CONSTITUENT_WITH_ADDNL_DETAILS
            WHERE EXCHANGE_TICKER in ({etfs_of_interest_str})
            GROUP BY ASAT_DATE, SECURITY_ID
        ) holdings ON holdings.ASAT_DATE = etd.BUSINESS_DATE
        WHERE etd.isomic_code = 'ARCX' and
        etd.business_date >= '2019-01-03' and
        etd.business_date <= '2025-03-12'
        and IsMissing
        ORDER BY 1, 2
    """

    print(query)

    df_securities = sf_adaptor.read_data("ETF", query)

    if df_securities.shape[0] > 0:
        print("Missing constituents for the following business dates:")
        print(df_securities)
    else:
        print("No missing constituents found for the specified business dates.")