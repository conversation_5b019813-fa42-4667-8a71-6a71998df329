import os
import json
import pandas as pd
from data_analyzer.amazon_review.amazon_review_data_anayzer import AmazonReviewDataAnalyzer
import http.server
import socketserver
import webbrowser
import plotly.io as pio

class AmazonReviewSearch:
    def __init__(self, api_key, input_datapath, output_datapath, debug=True):
        self.searcher = AmazonReviewDataAnalyzer(api_key, input_datapath, output_datapath, debug=debug)
        self.debug = debug
        self.json_dir = 'static/json'
        self.html_dir = 'static/html'
        self._ensure_directories()

    def _ensure_directories(self):
        os.makedirs(self.json_dir, exist_ok=True)
        os.makedirs(self.html_dir, exist_ok=True)

    def check_data(self):
        if not self.searcher.check_data_availability():
            raise FileNotFoundError("Data file is required to proceed.")

    def process_embeddings(self):
        self.searcher.load_or_create_embeddings()
        self.searcher.build_annoy_index(embedding_dimension=1536, trees=10)

    def search(self, query):
        results = self.searcher.search_embeddings(query, n=3)
        self._write_to_file("search_results.json", results)

    def visualize_embeddings(self):
        tsne_plot_json = self.searcher.visualize_embeddings_with_tsne()
        self._write_to_file("tsne_plot.json", tsne_plot_json)

    def perform_regression(self):
        self.searcher.split_data(test_size=0.2, random_state=42)
        regression_results = self.searcher.perform_regression()
        self._write_to_file("regression_results.json", regression_results)

    def classify_and_evaluate(self):
        self.searcher.train_classifier()
        evaluation_results = self.searcher.evaluate_model()
        self._write_to_file("evaluation_results.json", evaluation_results)

    def plot_precision_recall(self):
        precision_recall_plot_json = self.searcher.plot_multiclass_precision_recall()
        self._write_to_file("precision_recall_plot.json", precision_recall_plot_json)

    def find_and_visualize_clusters(self):
        clustering_results = self.searcher.find_clusters(n_clusters=4)
        self._write_to_file("clustering_results.json", clustering_results)

        cluster_tsne_plot_json = self.searcher.visualize_clusters_with_tsne()
        self._write_to_file("cluster_tsne_plot.json", cluster_tsne_plot_json)

    def summarize_clusters(self):
        cluster_summary = self.searcher.name_and_sample_clusters(n_samples=5)
        self._write_to_file("cluster_summary.json", cluster_summary)

    def start_server(self):
        PORT = 8000
        Handler = http.server.SimpleHTTPRequestHandler

        while True:
            try:
                with socketserver.TCPServer(("", PORT), Handler) as httpd:
                    print(f"Serving at port {PORT}")
                    webbrowser.open(f"http://localhost:{PORT}/static/html/index.html")
                    httpd.serve_forever()
            except OSError as e:
                if e.errno == 48:  # Address already in use
                    print(f"Port {PORT} is in use, trying the next port...")
                    PORT += 1
                else:
                    raise

    def _write_to_file(self, filename, data):
        if isinstance(data, pd.DataFrame):
            data = data.to_json()
        elif isinstance(data, (dict, list)):
            data = json.dumps(data, default=str, indent=4)
        elif isinstance(data, str):  # If it's already a JSON string
            pass
        else:
            raise ValueError("Unsupported data type for writing to file.")
        with open(os.path.join(self.json_dir, filename), "w") as f:
            f.write(data)
