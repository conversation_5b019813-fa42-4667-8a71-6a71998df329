import psycopg2
import numpy as np
import pandas as pd
from datetime import date
from loguru import logger as log
from datetime import datetime, timedelta
import pytz
import os 
import subprocess
from strunner import *
setupEnvironment()

from stcommon.infra.rds.snowflake_operation import *
JGDATA_PATH = os.environ.get("JGDATA_PATH")

def floor_to_nearest(d):
    if d.minute < 30:
        return d.replace(minute=30, second=0, microsecond=0)
    else:
        return (d+timedelta(hours=1)).replace(minute=0, second=0, microsecond=0)

def execute_commands(run=True):
    converted_times = {}
    for time in time_intervals:
            print("\nConverting to all timezones in the config table and running for the UTC time: ", time)
            for tz in timezone_list:
                if tz:
                    tz_obj = pytz.timezone(tz)
                    local_time = pytz.utc.localize(time).astimezone(tz_obj)
                    converted_times[tz] = local_time.strftime("%H%M")
                    print(f'Execute: python3 {JGDATA_PATH}/bin/bloomberg.snap.gl.py --st {converted_times[tz]} --tz {tz}')
                    if run:
                        command = ["python3", f"{JGDATA_PATH}/bin/bloomberg.snap.gl.py", "--st", f"{converted_times[tz]}", "--tz", f'{tz}']
                        subprocess.run(command)
                        print(f'\n\nExecution completed for: python3 {JGDATA_PATH}/bin/bloomberg.snap.gl.py --st {converted_times[tz]} --tz {tz}\n\n')

if __name__ == '__main__':
    try:
        utc_now = datetime.now(pytz.utc)
        utc_now = floor_to_nearest(utc_now)
        objconfig = {}
        objconfig = fio.read_config_secrets()

        # Connect to the PostgreSQL database
        pg_conn = psycopg2.connect(
            host=objconfig['pg_host'],
            database="airflow",
            user=objconfig['pg_user'],
            password=objconfig['pg_password']
        )

        pg_query = f""" WITH RECURSIVE TIME_SERIES AS (
                            SELECT TO_CHAR(TIMEZONE('UTC', NOW()),'YYYY-MM-DD HH:00:00')::TIMESTAMP - INTERVAL '5 DAYS' AS START_TIME
                            UNION ALL
                            SELECT START_TIME + INTERVAL '30 MINUTES'
                            FROM TIME_SERIES
                            WHERE START_TIME < TIMEZONE('UTC', NOW()) - INTERVAL '30 MINUTES'
                        ),
                        MY_DAG_ID AS (
                        	SELECT 'jg-etl-bloombergsnap-eod' AS DAG_ID 
                        ),
                        DAYS_OF_THE_WEEK AS (
                        	SELECT
                        	SPLIT_PART(SPLIT_PART(SCHEDULE_INTERVAL, ' ', 5), '-', 1) START_DAY, 
                        	REPLACE(SPLIT_PART(SPLIT_PART(SCHEDULE_INTERVAL, ' ', 5), '-', 2), '"', '') AS END_DAY 
                        	FROM DAG 
                        	WHERE DAG_ID=(SELECT DAG_ID FROM MY_DAG_ID)
                        )
                        SELECT START_TIME
                        FROM TIME_SERIES, DAYS_OF_THE_WEEK
                        WHERE EXTRACT(DOW FROM START_TIME) BETWEEN START_DAY::INTEGER AND END_DAY::INTEGER
                        AND START_TIME > 
                        (
                            SELECT COALESCE(MAX(TIMEZONE('UTC',EXECUTION_DATE)),CURRENT_DATE::TIMESTAMP)
                            FROM AIRFLOW.PUBLIC.DAG_RUN DR WHERE DAG_ID=(SELECT DAG_ID FROM MY_DAG_ID)
                            AND DR.STATE = 'success'
                        )
                    """
        df_pg = pd.read_sql(pg_query, pg_conn)
        
        time_intervals = df_pg["start_time"].tolist()

        obj_sf = SnowflakeDML("BLOOMBERG")
        sf_query=f"SELECT DISTINCT(TIMEZONE) FROM BLOOMBERG.SNAP.BLOOMBERG_SNAP_CONFIG"
        df = obj_sf.fetch_query(sf_query)
        timezone_list=df["TIMEZONE"].tolist()

        if len(time_intervals) > 2:
            print(f"\033[1m\nThe following schedules are not run: {time_intervals}.\nActions to be taken:\n1) Please manually run for these schedules using the below commands. You can run these commands using the adhoc job 'jg-bloombergsnap-exec-adhoc'.\n2) Mark this DAG run as True.\033[0m\n")
            execute_commands(False)
            log.error(f"The following schedules are not run: {time_intervals}, please manually run for these schedules using the above mentioned commands:\n")
        else:
            print("Running for following time intervals: ", time_intervals)
            execute_commands()
               
    except Exception as e:
            log.error("Unexpected error: {}".format(str(e)))
            raise e