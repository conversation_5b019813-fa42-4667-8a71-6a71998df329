select * from METEOLOGICA.API.FORECAST_DETERMINISTIC WHERE CONTENT_ID = 32 and ISSUE_DATE = '2022-01-01 00:53:03.000';

create or replace view meteologica.api.vw_forecast_deterministic as
with consolidated as (
    SELECT * FROM meteologica.api.forecast_deterministic
    WHERE issue_date < DATE('2025-01-01')
    UNION ALL 
    SELECT * FROM meteologica.ongoing.forecast_deterministic
    WHERE issue_date >= DATE('2025-01-01')
), forecast_deterministic as(
    select content_id, content_name, issue_date, timezone, unit, installed_capacity, update_id, source_file,
        to_varchar(data:"From yyyy-mm-dd hh:mm") from_datetime,
        to_varchar(data:"To yyyy-mm-dd hh:mm") to_datetime,
        to_varchar(data:"UTC offset from (UTC+/-hhmm)") utc_offset_from,
        to_varchar(data:"UTC offset to (UTC+/-hhmm)") utc_offset_to,

        -- special cases
        to_varchar(data:"Date yyyy-mm-dd") plain_date,
        to_varchar(data:"UTC offset(HHmm)") plain_utc_offset,
        
        to_varchar(data:"ARPEGE RUN") arpege_run,
        to_varchar(data:"ECMWF ENS RUN") ecmwf_ens_run,
        to_varchar(data:"ECMWF HRES RUN") ecmwf_hres_run,
        to_varchar(data:"GFS RUN") gfs_run,
        to_varchar(data:"forecast") forecast,
        to_varchar(data:"perc10") perc10,
        to_varchar(data:"perc90") perc90        
        from consolidated
), forecast_deterministic_utc as (
    select content_id, 
    content_name,
    issue_date,
    unit, 
    installed_capacity, 
    update_id,
    source_file,
    CASE WHEN content_id IN (32, 37, 289, 317, 321, 601, 876, 878, 880, 882, 884, 886, 888, 890, 892, 894, 896, 898, 930, 932, 934, 936, 938, 940, 942, 944, 1126, 1183, 1187, 1218, 1222, 1241, 1245, 1263, 1281, 1398, 1460, 1464, 1514, 1516, 4691, 4695, 4994, 4998, 5104, 5109, 5114, 5119, 5125, 5130, 5135, 5140, 5145, 5150, 5155, 5160, 5165, 5170, 5175) THEN
            to_timestamp_ntz(convert_timezone(
                'UTC', 
                TO_TIMESTAMP_TZ(plain_date, 'YYYY-MM-DD')
            ))
        ELSE
            to_timestamp_ntz(convert_timezone(
                'UTC', 
                TO_TIMESTAMP_TZ(FROM_DATETIME || ' ' || split(utc_offset_from, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
            )) 
        END AS FROM_DATETIME,
    CASE WHEN content_id IN (32, 37, 289, 317, 321, 601, 876, 878, 880, 882, 884, 886, 888, 890, 892, 894, 896, 898, 930, 932, 934, 936, 938, 940, 942, 944, 1126, 1183, 1187, 1218, 1222, 1241, 1245, 1263, 1281, 1398, 1460, 1464, 1514, 1516, 4691, 4695, 4994, 4998, 5104, 5109, 5114, 5119, 5125, 5130, 5135, 5140, 5145, 5150, 5155, 5160, 5165, 5170, 5175) THEN
            TIMEADD(HOUR, 24, to_timestamp_ntz(convert_timezone(
                'UTC', 
                TO_TIMESTAMP_TZ(plain_date, 'YYYY-MM-DD')
            )))
        ELSE
            to_timestamp_ntz(convert_timezone(
                'UTC', 
                TO_TIMESTAMP_TZ(TO_DATETIME || ' ' || split(utc_offset_to, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
            )) 
        END AS TO_DATETIME,
        arpege_run, ecmwf_ens_run, ecmwf_hres_run, gfs_run, forecast, perc10, perc90
        from forecast_deterministic
)
select * from forecast_deterministic_utc;

--553578434
select count(*) from vw_forecast_deterministic limit 100;
select * from vw_forecast_deterministic 
where content_id = 32
limit 100;

select distinct date(from_datetime) from poc_db.meteologica.vw_forecast_deterministic
order by date(from_datetime) desc
limit 1000;

select * from poc_db.meteologica.forecast_deterministic
where data:"From yyyy-mm-dd hh:mm" is not null
limit 100;