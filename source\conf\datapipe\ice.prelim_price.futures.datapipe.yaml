raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/ice/prelim_price/ICE_FLASH/futures_converted"  ## Location of Compress Raw Files
  s3_bucket: "jg-data-dp-vendor-data" ## S3 with Snowflake Acess
  s3_prefix: "ice/prelim_price/futures" ## Internal S3path to files
  # include_prefix: true
  structure: '[
    "*cleared_gas_$DATE$.xlsx.csv"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "ICE_PRELIM_PRICE"

  table_map:
    FUTURES_RAW:
      pattern: ".*cleared_gas_.*xls.*csv" ## Need to me a regex format
      col_num: 11
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "ice/prelim_price/futures/" ##<stage name>/<stage path>
      file_format: "FF_FUTURES"