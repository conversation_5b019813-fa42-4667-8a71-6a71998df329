import sys
import os
import time
import argparse
from datetime import datetime, timedelta
import kpler.kpler_processor as kpler_processor
from kpler.kpler_config import KplerDataType


def get_inputs():
    parser = argparse.ArgumentParser(description="Backfill Kpler data")
    parser.add_argument("--dataset", required=True, help="Kpler Dataset to backfill")
    parser.add_argument("--start_date", required=True, help="Start Date")
    parser.add_argument("--end_date", required=True, help="End Date")
    return parser.parse_args()

def generate_10am_timestamps(start_date_str, end_date_str):
    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

    timestamps = []
    current_date = start_date
    while current_date <= end_date:
        timestamps.append(current_date.replace(hour=10, minute=0, second=0, microsecond=0))
        current_date += timedelta(days=1)
    
    return timestamps


def get_run_dates(start_date_str, end_date_str):
    start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
    end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

    run_dates = []
    current_date = start_date
    while current_date <= end_date:
        run_dates.append(current_date.strftime("%Y-%m-%d"))
        current_date += timedelta(days=1)
    
    return run_dates


def backfill_avail_per_fuel_type(start_date_str, end_date_str):
    timestamps = generate_10am_timestamps(start_date_str, end_date_str)
    for timestamp in timestamps:
        backfill_avail_per_fuel_type = kpler_processor.KplerAvailabilityByFuelTypeProcessor(is_historical_backfill=True, hist_as_of=timestamp.strftime("%Y-%m-%d %H:%M:%S"))
        backfill_avail_per_fuel_type.process_kpler_data()
        time.sleep(30)
    

def backfill_avail_per_unit(start_date_str, end_date_str):
    timestamps = generate_10am_timestamps(start_date_str, end_date_str)
    for timestamp in timestamps:
        avail_per_units = kpler_processor.KplerAvailabilityPerUnitProcessor(is_historical_backfill=True, hist_as_of=timestamp.strftime("%Y-%m-%d %H:%M:%S"))
        avail_per_units.process_kpler_data()
        time.sleep(30)
        

def backfill_actual_load(start_date, end_date):
    actual_load_processor = kpler_processor.KplerActualLoadProcessor(is_historical_backfill=True, hist_start_date=start_date, hist_end_date=end_date)
    actual_load_processor.process_kpler_data()


def backfill_generation_per_fuel_type(start_date, end_date):
    gen_per_fuel_type_processor = kpler_processor.KplerGenerationPerFuelTypeProcessor(is_historical_backfill=True, hist_start_date=start_date, hist_end_date=end_date)
    gen_per_fuel_type_processor.process_kpler_data()


def backfill_generation_per_unit(start_date, end_date):
    gen_per_unit_processor = kpler_processor.KplerGenerationPerUnitProcessor(is_historical_backfill=True, hist_start_date=start_date, hist_end_date=end_date)
    gen_per_unit_processor.process_kpler_data()


def backfill_forecast_load(start_date, end_date):
    run_dates = get_run_dates(start_date, end_date)
    for run_date in run_dates:
        forecast_load_processor = kpler_processor.KplerForecastLoadProcessor(is_historical_backfill=True, hist_run_date=run_date)
        forecast_load_processor.process_kpler_data()


def backfill_consumption_per_fuel_type(start_date, end_date):
    consumption_per_fuel_type = kpler_processor.KplerConsumptionPerFuelTypeProcessor(is_historical_backfill=True, hist_start_date=start_date, hist_end_date=end_date)
    consumption_per_fuel_type.process_kpler_data()
        

def backfill_commercial_schedules(start_date, end_date):
    comm_sched_processor = kpler_processor.KplerCommercialSchedulesProcessor(is_historical_backfill=True, hist_start_date=start_date, hist_end_date=end_date)
    comm_sched_processor.process_kpler_data()

if __name__ == "__main__":
    inputs = get_inputs()
    if inputs.dataset == KplerDataType.ActualLoad.value:
        backfill_actual_load(inputs.start_date, inputs.end_date)
    elif inputs.dataset == KplerDataType.AvailPerUnit.value:
        backfill_avail_per_unit(inputs.start_date, inputs.end_date)
    elif inputs.dataset == KplerDataType.AvailPerFuelType.value:
        backfill_avail_per_fuel_type(inputs.start_date, inputs.end_date)
    elif inputs.dataset == KplerDataType.GenPerUnit.value:
        backfill_generation_per_unit(inputs.start_date, inputs.end_date)
    elif inputs.dataset == KplerDataType.GenPerFuelType.value:
        backfill_generation_per_fuel_type(inputs.start_date, inputs.end_date)
    elif inputs.dataset == KplerDataType.ForecastLoad.value:
        backfill_forecast_load(inputs.start_date, inputs.end_date)
    elif inputs.dataset == KplerDataType.ConsumptionPerFuelType.value:
        backfill_consumption_per_fuel_type(inputs.start_date, inputs.end_date)
    elif inputs.dataset == KplerDataType.CommercialSchedules.value:
        backfill_commercial_schedules(inputs.start_date, inputs.end_date)
    else:
        raise ValueError(f"Invalid dataset: {inputs.dataset}")
    

    
    