import os, sys
import logging
sys.path.append(os.getcwd())
import json
import psycopg2
import pandas as pd
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '../../../source/bin')))
from strunner import *
setupEnvironment()
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)
from utils.snowflake.adaptor import SnowflakeAdaptor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

configPath = os.environ.get('CONFIG_PATH', os.getcwd())
with open(f'{configPath}/config.json', 'r') as f:
    config = json.load(f)

def jg_config_path():
    return config["JG_CONFIG_PATH"]

def read_config_secrets():
    config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
    with open(config_secret_path, 'r') as f:
        config_secret = json.load(f)
    return config_secret

objconfig = read_config_secrets()

try:    
    db_params = {
                'dbname': objconfig['pg_database'],
                'user': objconfig['pg_user'],
                'password': objconfig['pg_password'],
                'host': objconfig['pg_host'],
                'port': '5432'
            }
    logger.info("Connecting to the Postgres RDS database...")
    conn = psycopg2.connect(**db_params)
    cur = conn.cursor()
    logger.info("Connected to the Postgres RDS database.")
    
    query = "SELECT * FROM FOTOOLS_PNL.BUSINESS_MAPPING_VIEW"
    
    cur.execute(query)
    result = cur.fetchall()
    columns = [desc[0] for desc in cur.description]
    mapping_df = pd.DataFrame(result, columns=columns)

    os.environ['SF_USERNAME'] = objconfig['sf_user']
    os.environ['SF_PASSWORD'] = objconfig['sf_password']

    logger.info("Connecting to the Snowflake database...")
    sf_adaptor = SnowflakeAdaptor(database=objconfig['ssnc_database'], warehouse=objconfig['ssnc_warehouse'], role=objconfig['ssnc_role'])   
    logger.info("Connected to the Snowflake database.")

    sf_schema = objconfig['ssnc_schema']
    sf_adaptor.execute_query(sf_schema, f'TRUNCATE {sf_schema}.BUSINESS_MAPPING')
    sf_adaptor.write_pandas_dataframe('GLOBEOP', mapping_df, f'{sf_schema}.BUSINESS_MAPPING')
    logger.info('Sucessfully synced RDS view to Snowflake table BUSINESS_MAPPING')
except Exception as e:
    logger.error(f"An error occurred: {e}")