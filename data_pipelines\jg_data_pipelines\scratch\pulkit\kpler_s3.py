import sys
import getopt
import boto3
import os
import zipfile
import pandas as pd
import argparse
from itertools import islice
from botocore.exceptions import NoCredentialsError

bucket_name = 'kp-power-data-prod-main'
# file_prefix="tbloptionintradayhist_EQT_ID_v2.00_"

from botocore.config import Config

custom_credentials_path = '~/.credentials/kpler_power_s3/.kpler_credentials'

os.environ['AWS_SHARED_CREDENTIALS_FILE'] = custom_credentials_path
session = boto3.Session()
credentials = session.get_credentials()
current_credentials = credentials.get_frozen_credentials()


def list_files(bucket, folder=""):
    try:
        s3 = boto3.resource('s3', aws_access_key_id=current_credentials.access_key, aws_secret_access_key=current_credentials.secret_key)
        bucket_obj = s3.Bucket(bucket)
        for obj in bucket_obj.objects.filter(Prefix=folder):
            print(obj.key)
            
    except NoCredentialsError:
        print("Invalid AWS credentials")
    except Exception as e:
        print(f"Error downloading folder: {e}")


if __name__ == "__main__":
    list_files(bucket_name, "dataset=availability-snapshot")