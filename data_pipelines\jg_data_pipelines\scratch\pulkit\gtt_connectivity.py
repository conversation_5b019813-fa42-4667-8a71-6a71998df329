import requests
from enum import Enum
import json
import pandas as pd
from datetime import datetime
import pytz

if __name__ == "__main__":
    baseUrl = 'https://www.globaltradetracker.com/api/rest'

    userid = "<FILLME>"
    password = "<FILLME>" 

    TOKEN = requests.get(baseUrl + "/gettoken?userid=" + userid + "&password=" + password).text
    print(TOKEN)

    # subscriptions = requests.get(baseUrl + "/subscriptions?token=" + TOKEN)
    # period_types = requests.get(baseUrl + "/subscriptions?token=" + TOKEN +"&periodtype=M")

    # countries = requests.get(baseUrl + "/countries?token=" + TOKEN + "&periods=true").json()
    # print(countries)
    # country_data=list(map(lambda x: pd.DataFrame.from_dict(x['country'], orient='index').transpose(),countries))
    # print(country_data)
    # countries = pd.concat(country_data, ignore_index=True)

    # trade_details=['SUBDIVISION','PORT','TRANSPORT','FOREIGN_PORT','PARTNER_US_STATE','CUSTOMS_REGIME','SUPPRESSION']

    # dfs=[]
    # for t in trade_details:
    #     trade_data = requests.get(baseUrl + "/tradedetails?token=" + TOKEN + "&countryCode=BR"+"&tradedetails={}".format(t)).json()
    #     if len(trade_data) > 0:
    #         flat_trade_data = [item[t] for item in trade_data]
    #         df_trade_details =  pd.DataFrame(flat_trade_data)
    #         df_trade_details['trade_detail']=t
    #         dfs.append(df_trade_details)
    # BR_trade_details = pd.concat(dfs)

    #Need to make sure your default Layout Template includes all available fields
    report_fields = requests.get(baseUrl + "/fields?token=" + TOKEN).json()
    df_report_fields=pd.DataFrame(report_fields)
    print(df_report_fields)

    # country_list = ','.join(list(countries.alphageonom2))
    # updatedAfter = '2024-04-01' #just an example date, you can change it to any date you want
    # data_updates = requests.get(baseUrl + "/dataupdates?token=" + TOKEN + "&countryCode=" + country_list
    #                         + "&updatedAfter=" +updatedAfter).json()
    # df_data_updates=pd.DataFrame(data_updates)

