raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/usda_agri"
  s3_bucket: "jg-data-dp-vendor-data"
  s3_prefix: "usda-agri"
  include_prefix: true

  structure: '[
   "quickstats_metadata_$DATE$.csv",
   "qs.**_$DATE$.txt"
  ]'

snowflake:
  db_name: "VENDOR_RAW"
  schema_name: "USDA_COMMOD"

  table_map:
  
    QUICKSTATS_METADATA:
      pattern: "^quickstats_metadata_$DATE$.csv" 
      col_num: 2
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "usda-agri/"
      file_format: "FF_USDA_AGRI" 

    QUICK_STATS_DATA_raw :
        pattern: "^qs.*_$DATE$.txt" 
        col_num: 39
        metadata_columns: ["filename", "start_scan_time"]
        stage_path: "usda-agri/"
        file_format: "FF_USDA_AGRI_TXT" 