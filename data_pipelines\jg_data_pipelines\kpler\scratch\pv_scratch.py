import requests
import kpler.kpler_config as k<PERSON>_config
import pandas as pd

if __name__ == "__main__":
    # countries = ['UK', 'DE', 'FR', 'NL', 'BE', 'AT']
    # list_a = [{"country": country, "fuel_type": fuel_type} for country, fuel_types in kpler_config.DICT_AVAIL_PARAMS.items() for fuel_type in fuel_types if country in countries]
    # print(list_a)
    # print(len(list_a))
    url = "https://api.kpler.com/power/interconnections/v1/series/commercial_schedules/?market_type=day_ahead&area_type=country&granularity=hourly&timezone=UTC&country=UK&start=2025-03-26&end=2025-03-27"

    "interconnections/v1/series/commercial_schedules"

    # [{'start': '2025-03-26', 'end': '2025-03-27', 'granularity': 'hourly', 'timezone': 'UTC', 'country': 'UK', 'market_type': 'day_ahead', 'area_type': 'country'}]

    payload={}
    headers = {
    'Authorization': '',
    'Accept': 'application/json'
    }

    response = requests.request("GET", url, headers=headers, data=payload)

    print(response.text)



    

