import os
import gzip
import argparse
import tempfile
import json

from pathlib import Path
from bloomberg.per_security.parser import Bloomberg<PERSON>ars<PERSON>
from utils.snowflake.bulk_loader import SnowflakeBulkLoader
from utils.compress_tools.recursive_uncompressor import RecursiveUncompressor


def get_create_table_stmt(target_schema, target_table):
    return f"""
        create TABLE IF NOT EXISTS {target_schema}.{target_table} (
            SECURITY VARCHAR(250) NOT NULL,
            TH_BAR_TIME TIMESTAMP_NTZ(9) NOT NULL,
            TH_BAR_TYPE VARCHAR(16777216) NOT NULL,
            TH_BAR_OPEN FLOAT,
            TH_BAR_HIGH FLOAT,
            TH_BAR_LOW FLOAT,
            TH_BAR_CLOSE FLOAT,
            TH_BAR_VOLUME FLOAT,
            TH_BAR_VWAP FLOAT,
            TH_BAR_TICK_COUNT NUMBER(38,0),
            TH_BAR_OPEN_TIME TIMESTAMP_NTZ(9),
            TH_BAR_HIGH_TIME TIMESTAMP_NTZ(9),
            TH_BAR_LOW_TIME TIMESTAMP_NTZ(9),
            TH_BAR_CLOSE_TIME TIMESTAMP_NTZ(9),
            TH_BAR_SNAP FLOAT,
            TH_BAR_SNAP_TIME TIMESTAMP_NTZ(9)
        );
    """


def get_cleanup_stage_stmt(stage_name, stage_path):
    return f"REMOVE '@{stage_name}/{stage_path}'"


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Upload all BBG files in a given path to Snowflake")
    parser.add_argument("--path", type=str, help="Source File Path")
    parser.add_argument("--parse_bbg", type=bool, help="Whether to use the BBG parser or leave file as it is (CSV))")
    parser.add_argument("--target_schema", type=str, help="Snowflake schema")
    parser.add_argument("--target_table", type=str, help="Snowflake table")
    parser.add_argument("--target_stage", type=str, help="Snowflake stage")
    
    args = parser.parse_args()
    with open('jg_data_pipelines_stage/files/fut_px_1min.json', 'r') as conf_file:
        conf = json.load(conf_file)
        columns = conf['columns']
        structure = conf['structure']
    
    target_schema = args.target_schema
    target_table = args.target_table
    target_stage = args.target_stage
    target_stage_path = f"{args.target_table}_STG"
    bulk_loader = SnowflakeBulkLoader(
        database="BLOOMBERG", 
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER",
        schema=target_schema)

    create_table_stmt = get_create_table_stmt(target_schema, target_table)
    bulk_loader.cursor.execute(create_table_stmt)

    with tempfile.TemporaryDirectory() as temp_dir:
        recursive_uncompressor = RecursiveUncompressor(args.path, structure)
        csv_root_path = f'{temp_dir}/csvs' 
        Path(csv_root_path).mkdir(parents=True, exist_ok=True)
        for flat_file in recursive_uncompressor.run_generator(temp_dir):
            if args.parse_bbg:
                parser = BloombergParser(flat_file, sep='|', skipinitialspace=True, on_bad_lines='warn')
                df = parser.parse_data(['REPLYFILENAME', 'TIMESTARTED', 'TIMEFINISHED', 'PROGRAMNAME'])
                csv_file_name = flat_file.split('/')[-1]
                csv_file_path = f'{csv_root_path}/{csv_file_name}'
                df.to_csv(csv_file_path, index=False)
            else:
                csv_file_path = flat_file
            bulk_loader.put_file(csv_file_path, target_stage, stage_path=target_stage_path)
            os.remove(flat_file)
        bulk_loader.load_path(
            target_table,
            target_stage,
            target_stage_path,
            columns=columns)

    cleanup_stage_stmt = get_cleanup_stage_stmt(target_stage, target_stage_path)
    bulk_loader.cursor.execute(cleanup_stage_stmt)
