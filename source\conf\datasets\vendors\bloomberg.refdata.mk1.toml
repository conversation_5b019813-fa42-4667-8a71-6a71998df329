dataset = 'bloomberg.refdata'
catalog = 'cache'
version = 'mk1'
description = 'reference data'
scope = ['gl']
tables = [
  'corpstructure',
   # lookups
  'currency',
  'country',
  'marketstatus',
  'primaryexchange',
  'compositeexchange',
  'cpDvdTyp',
  'cpDvdStockTyp',
  'cpStockSpltTyp',
  'mic',
  'settlecalendarcode',
  'exchcalendarcode',
   # mic - exchCode map
  'micmap',

  'datasetinfo',
  'fields',
  'caxfields',

   # fx rates
  'currencypx',
  'fxrates',
  ]