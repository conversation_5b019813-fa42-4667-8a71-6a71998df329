import json
import pandas as pd
from pathlib import Path
import logging
from collections import defaultdict
import re

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def read_kpler_json(file_path):
    # print(f"Analyzing {file_path}")
    with open(file_path, 'r') as f:
        data = json.load(f)
    json_keys = data.keys()

    if "detail" in json_keys:
        if type(data["detail"]) == str:
            detail_value = data["detail"].lower()
            if "no production units for" in detail_value:
                return file_path, [], [], []

    expected_elements = {
        "provider": str,
        "location": str,
        "timezone": str,
        "metadata": dict,
        "data": dict,
        "index": list,
    }

    missing_keys = set(expected_elements.keys()) - set(json_keys)
    extra_keys = set(json_keys) - set(expected_elements.keys())

    mismatched_data_types = []
    
    for key, expected_type in expected_elements.items():
        if key in json_keys:
            if type(data[key]) != expected_type:
                mismatched_data_types.append(key)
    
    return file_path, list(missing_keys), list(extra_keys), mismatched_data_types

def analyze_json_structure(root_dir):
    total_files = 0

    for json_path in Path(root_dir).rglob('*.json'):
        total_files += 1
        file_path, missing_keys, extra_keys, mismatched_data_types = read_kpler_json(json_path)
        print(f"File {json_path}: Missing keys: {missing_keys}, Extra keys: {extra_keys}, Mismatched data types: {mismatched_data_types}")
    
    return total_files

def main(root_dir):
    # Root directory containing JSON files
    total_files = analyze_json_structure(root_dir)
    print(f"\nAnalyzed {total_files} JSON files")

def analyze_json_structure_for_specific_file(file_path):
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    for key, value in data.items():
        val_type = type(value)
        print(f"{key}: {val_type}")
        print(f"Value: {value}")

def parse_log_file():
    
    pattern = r"""
    File\s(?P<filepath>.*\.json):\s+
    Missing\skeys:\s\[(?P<missing_keys>.*?)\],\s+
    Extra\skeys:\s\[(?P<extra_keys>.*?)\],\s+
    Mismatched\sdata\stypes:\s\[(?P<mismatched_types>.*?)\]
    """
    
    with open("kpler_parsing.log", 'r') as f:
        for line in f:
            match = re.match(pattern, line, re.VERBOSE)
            if match:
                # print(match.group('filepath'))
                dict_file_attributes = {
                    'filepath': match.group('filepath'),
                    'missing_keys': match.group('missing_keys').split(', ') if match.group('missing_keys') else [],
                    'extra_keys': match.group('extra_keys').split(', ') if match.group('extra_keys') else [],
                    'mismatched_types': match.group('mismatched_types').split(', ') if match.group('mismatched_types') else []
                }
                if len(dict_file_attributes['extra_keys']) > 0:
                    if len(dict_file_attributes['extra_keys']) == 1 and dict_file_attributes['extra_keys'][0] == "'detail'":
                        print(dict_file_attributes['filepath'])
                        # continue
                # if len(dict_file_attributes['missing_keys']) > 0 or len(dict_file_attributes['mismatched_types']) > 0 or len(dict_file_attributes['extra_keys']) > 0:
                #     print(dict_file_attributes["filepath"], dict_file_attributes["extra_keys"], dict_file_attributes["missing_keys"], dict_file_attributes["mismatched_types"])
    

if __name__ == "__main__":
    # analyze_json_structure_for_specific_file("/jfs/tech1/apps/rawdata/kpler_power/availability_units_yearly/2022-01-01T10:00:00+00:00/DE/fossil brown coal/2022-01/2022-01-01T10:00:00+00:00_DE_fossil brown coal_2022-01.json")
    # root_dir = "/jfs/tech1/apps/rawdata/kpler_power/availability_units_yearly/"
    root_dir = "/jfs/tech1/apps/rawdata/kpler_power/availability_units_yearly/"
    main(root_dir)
    # parse_log_file()

    

