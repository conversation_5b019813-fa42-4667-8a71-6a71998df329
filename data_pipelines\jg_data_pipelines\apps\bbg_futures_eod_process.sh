echo "Starting Futures End of the Day Process"

source $HOME/.bashrc

set -e

echo "Refreshing futures reference from Bbg DL plus"
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dlplus_futures_ref.py >> /home/<USER>/logs/bbg_dlplus_futures_ref.log 2>&1

echo "Updating futures reference from Bbg DL"
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_futures_ref.py >> /home/<USER>/logs/bbg_dl_futures_ref.log 2>&1

echo "Refreshing futures prices from Bbg DL"
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_futures_px.py >> /home/<USER>/logs/bbg_dl_futures_px.log 2>&1

echo "Sending Email Notification"
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/send_notification.py "Futures EOD process complete" "<EMAIL>,<EMAIL>"

echo "Completed Futures End of the Day Process"
