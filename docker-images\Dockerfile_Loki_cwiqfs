# Stage 1: Set up the base Python slim-buster image
FROM python:3.6-slim-buster AS base

# Force cache busting for console output
ARG USERNAME
ARG LOGIN_KEY
ENV USERNAME=$USERNAME
ENV LOGIN_KEY=$LOGIN_KEY

USER root

# Update package sources and install necessary utilities
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    wget \
    curl \
    vim \
    less \
    dos2unix \
    kmod \
    fuse3 \
    findutils \
    openssh-client \
    procps \
    gettext \
    unzip \
    alien \
    libkrb5-dev && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install specific RPM packages
COPY /docker-images/cwiqfs/fuse-libs-2.9.2-11.el7.x86_64.rpm  /tmp
COPY /docker-images/cwiqfs/fuse-sshfs-2.10-1.el7.x86_64.rpm /tmp
COPY /docker-images/cwiqfs/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm /tmp
RUN alien -i /tmp/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm

# Ensure the symbolic link exists for certificates
RUN ln -sf /etc/ssl/certs/ca-bundle.crt /etc/ssl/certs/ca-certificates.crt

# Copy and configure cwiqfs configuration
COPY docker-images/cwiqfs/cwiqfs.yaml /etc/cwiq/cwiqfs/config_temp.yaml
RUN sed -e "s/\${USERNAME}/$USERNAME/g" -e "s/\${LOGIN_KEY}/$LOGIN_KEY/g" /etc/cwiq/cwiqfs/config_temp.yaml > /etc/cwiq/cwiqfs/config.yaml
RUN sed -i -e 's/# user_allow_other/user_allow_other/g' /etc/fuse.conf
RUN mkdir -p /var/tmp/cache /var/log/cwiq && chmod 777 /var/tmp/cache

# Create directories and user
RUN mkdir /jfs && \
    mkdir -p /usr/lib/jfs && \
    chmod -R 755 /usr/lib/jfs && \
    useradd -ms /bin/bash jsvc-tech1

# Copy the mount script and ensure it's executable
# COPY docker-images/cwiqfs/mount-jfs.sh /usr/lib/jfs/mount-jfs.sh
# RUN chmod +x /usr/lib/jfs/mount-jfs.sh && \
#    dos2unix /usr/lib/jfs/mount-jfs.sh

# Stage 2: Copy Prometheus (Loki) components from the official image
FROM grafana/loki:latest AS loki_source

# Stage 3: Set up the final Python-based image with Loki components
FROM base AS final

# Copy the Loki binary and configuration from the Loki source image
COPY --from=loki_source /usr/bin/loki /usr/bin/loki
COPY --from=loki_source /etc/loki /etc/loki

# Additional Loki-specific setup
RUN useradd -ms /bin/bash loki && \
    mkdir -p /loki && \
    chown -R loki:loki /loki /etc/loki

# Expose Loki port
EXPOSE 3100

# Copy Loki configuration
COPY docker-images/grafana/loki.yaml /etc/loki/loki.yaml
COPY docker-images/grafana/loki-start.sh /etc/loki/loki-start.sh
RUN  chmod +x /etc/loki/loki-start.sh

# Final cleanup and permissions
RUN ls -lart /etc/loki && \
    mv /etc/loki/local-config.yaml /etc/loki/loki-config.yaml

CMD ["/etc/loki/loki-start.sh"]