<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Plotly Graphs</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
</head>
<body>
    <div id="tsne_plot" style="width: 100%; height: 600px;"></div>
    <div id="precision_recall_plot" style="width: 100%; height: 600px;"></div>
    <div id="cluster_tsne_plot" style="width: 100%; height: 600px;"></div>

    <script>
        function renderPlotlyGraph(elementId, jsonData) {
            var parsedData = JSON.parse(jsonData);
            Plotly.newPlot(elementId, parsedData.data, parsedData.layout);
        }

        // Load JSON data and render plots
        fetch('../json/tsne_plot.json')
            .then(response => response.json())
            .then(data => {
                renderPlotlyGraph('tsne_plot', JSON.stringify(data));
            });

        fetch('../json/precision_recall_plot.json')
            .then(response => response.json())
            .then(data => {
                renderPlotlyGraph('precision_recall_plot', JSON.stringify(data));
            });

        fetch('../json/cluster_tsne_plot.json')
            .then(response => response.json())
            .then(data => {
                renderPlotlyGraph('cluster_tsne_plot', JSON.stringify(data));
            });
    </script>
</body>
</html>
