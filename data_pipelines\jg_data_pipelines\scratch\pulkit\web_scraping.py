import time
from bs4 import BeautifulSoup
import re
from urllib.parse import urljoin, urlparse
import os
import requests
 
def extract_file_links(html_file_path, base_url=None, file_extensions=None):
    if file_extensions is None:
        file_extensions = [".zip", ".csv", ".pdf", ".xls", ".xlsx"]
    with open(html_file_path, 'r', encoding="utf-8") as file:
        html_content = file.read()
    soup = BeautifulSoup(html_content, 'html.parser')
    links = []
    for a_tag in soup.find_all('a', href=True):
        href = a_tag.get("href")
        if href.lower().startswith("https://") and any(href.lower().endswith(ext) for ext in file_extensions):
            links.append(href)
        elif base_url is not None and any(href.lower().endswith(ext) for ext in file_extensions):
            links.append(f"{base_url}{href}")
    return links
 
def download_file(url, output_dir):
    filename = os.path.basename(urlparse(url).path)
    if not filename:
        print(url)
    file_path = os.path.join(output_dir, filename)

    if os.path.exists(file_path):
        print(f"{file_path} already exists thus skipping.")
        return

    try:
        print(f"Downloading {url}...")
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'en-US,en;q=0.9,en-IN;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-CH-UA': '"Not(A:Brand";v="99", "Microsoft Edge";v="133", "Chromium";v="133"',
            'Sec-CH-UA-Mobile': '?0',
            'Sec-CH-UA-Platform': '"Linux"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1'
        }

        try:
            response = requests.get(url, timeout=10,headers=headers)
            response.raise_for_status()
        except:
            response = requests.get(url, timeout=10,headers=headers)
            response.raise_for_status()
    
        with open(file_path, 'wb') as f:
            f.write(response.content)
        
        print(f"Downloaded {file_path}")
    except Exception as e:
        print(f"Error downloading {url}: {e}")
 
if __name__ == "__main__":
    html_page = "/jfs/tech1_share/pulkit.vora/jsvc.datait/sec_adv/adv.html"
    html_data_archive = "/jfs/tech1_share/pulkit.vora/jsvc.datait/sec_adv/adv_data_archive.html"
 
    links = extract_file_links(html_page)
    links_archive = extract_file_links(html_data_archive, "https://www.sec.gov")
 
    all_links = links + links_archive
    all_links_unique = list(set(all_links))

    # print(len(all_links))
   
    for link in all_links_unique:
        download_file(link, "/jfs/tech1_share/pulkit.vora/jsvc.datait/sec_adv/downloaded_files/")
        time.sleep(1)

    


# 