
import snowflake.connector
import time
import json
import tempfile
from datetime import datetime
from collections import defaultdict
import pandas as pd
import polars as pl
from pathlib import Path
from typing import Optional
from zipfile import ZipFile
import re
import os
import logging
import os
import uuid
from jg_data_pipelines.utils.snowflake.bulk_loader import SnowflakeBulkLoader

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

_RAW = "/jfs/tech1_share/samuel.lasker/meteo_results/"
_PROCESSED = "/jfs/tech1/apps/rawdata/metelogica/test_jcrubioa/"

_BAD_JSON_REGEX = []
database = 'POC_DB'
schema_name = 'METEOLOGICA'
target_table = 'METEO_RAW_RELOAD'
stage_name = 'SAM_TEST'
stage_path = uuid.uuid4().hex
adaptor = SnowflakeBulkLoader(
    database=database, 
    schema=schema_name,
    warehouse="BBG_DLPLUS_WH", 
    role="FR_DATA_PLATFORM")


def get_meteo_params_ref():
    logger.info("Fetching content IDs from Snowflake")
    snowflake_conn_params = {
        'user': os.getenv('SF_USERNAME'),
        'private_key_file': os.getenv('SF_PK_FILE'),
        'private_key_file_pwd': os.getenv('SF_PK_PASSWORD'),
        'account': 'byb06077.us-east-1',
        'warehouse': 'TEST',
        'database': 'METEOLOGICA',
        'schema': 'API',
        'role': 'FR_DATA_PLATFORM'
    }

    conn = snowflake.connector.connect(**snowflake_conn_params)
    query = "SELECT * FROM METEOLOGICA.API.METEO_PARAMS_REF"

    df = pd.read_sql(query, conn)
    conn.close()
    logger.info(f"Fetched {len(df)} content IDs")
    return df


def extract_data_for_zipfile(zipfile: str):
    zip_path = os.path.join(_RAW, zipfile)
    files_cnt = 0
    df_list = []
    content_id = int(zipfile.split('_')[0])
    failure = None
    with tempfile.TemporaryDirectory() as tmpdirname:
        result = f"{tmpdirname}/{zipfile.replace('.zip', '')}"
        try:
            with ZipFile(zip_path, 'r') as zObject:   
                zObject.extractall( 
                    path=result)
        except Exception as e:
            logger.warning(f"Corrupt zipfile: {zip_path}")
            logger.exception(e)
            failure = (zipfile, "BAD_ZIP")
            with open('failures.txt', 'a') as outfile:
                outfile.write(f"{str(failure)}\n")
            return

        # Examine format for every single json
        for json_path in Path(result).rglob('*.json'):
            skip_file = False
            for regex in _BAD_JSON_REGEX:
                if re.match(regex, str(json_path)):
                    skip_file = True
                    break
            
            if skip_file:
                logger.warning(f"Skipping bad JSON file: {json_path}")
                continue
            
            logger.debug(f"Checking file: {json_path}")
            # Check data format
            examine_json(json_path, content_id)

        # Load all json at once to a df, explode, convert to parquet, upload to stage
        data_table = get_data_table(content_id)
        try:
            df = pl.scan_ndjson(f"{result}/*.json", include_file_paths='source_file')
            df = df.with_columns(
                source_file = pl.concat_str(
                    pl.lit(f"{zipfile}/"),
                    pl.col('source_file').str.split('/').list.last()
                )
            )
            df = df.explode(pl.col("data")).collect()
            file_path = f"{_PROCESSED}{zipfile.replace('.zip', '')}.parquet"
            df.write_parquet(file_path)
            adaptor.put_file(file_path, stage_name, stage_path=f"{stage_path}/{data_table}")
        except Exception as e:
            logger.warning(f"File {zipfile} failed to parse.")
            logger.exception(e)
            failure = (zipfile, "JSON_FILES_PARSE_ERROR")
            with open('failures.txt', 'a') as outfile:
                outfile.write(f"{str(failure)}\n")
            return
        return files_cnt


def get_data_table(content_id):
    is_forecast = meteo_params_df[meteo_params_df['CONTENT_ID'] == content_id]['DATA_TYPE'].values[0] == 'Forecast'
    if is_forecast:
        forecast_type = meteo_params_df[meteo_params_df['CONTENT_ID'] == content_id]['FORECAST_TYPE'].values[0]
        forecast_source = meteo_params_df[meteo_params_df['CONTENT_ID'] == content_id]['FORECAST_SOURCE'].values[0]
        if forecast_type == 'Ensemble':
            if forecast_source != 'ECMWF-ENSEXT':
                data_table = 'FORECAST_ENSEMBLE'
            else:
                data_table = 'FORECAST_EXTENDED'
        else:
            data_table = 'FORECAST_DETERMINISTIC'
    else:
        data_type = meteo_params_df[meteo_params_df['CONTENT_ID'] == content_id]['DATA_TYPE'].values[0]
        if data_type == 'Normal':
            data_table = 'NORMAL'
        elif data_type == 'Observation':
            data_table = 'OBSERVATION'
        elif data_type == 'Reanalysis':
            data_table = 'REANALYSIS'
    return data_table            


def examine_json(file_path: str, content_id: str):
    with open(file_path, 'r') as f:
        data = json.load(f)
    if not data['data']:
        logger.info(f"No data for {file_path}")
        return [], [], [], True
    
    json_keys = data.keys()

    expected_elements = {
        "content_id": int,
        "content_name": str,
        "data": list,
        "installed_capacity": str,
        "issue_date": str,
        "timezone": str,
        "unit": str,
        "update_id": str,
    }

    data_table = get_data_table(content_id)    
    missing_keys = set(expected_elements.keys()) - set(json_keys)
    extra_keys = set(json_keys) - set(expected_elements.keys())

    mismatched_data_types = []
    logger.debug(f"Examining {data_table} format")
    for key, expected_type in expected_elements.items():
        if key in json_keys:
            if type(data[key]) != expected_type:
                mismatched_data_types.append(key)
            if key == "data":
                data_val = data[key]
                for i, data_point in enumerate(data_val):
                    if type(data_point) != dict:
                        mismatched_data_types.append(f"data[{i}]")
    
    if len(missing_keys) > 0 or len(extra_keys) > 0 or len(mismatched_data_types) > 0:
        logger.debug(f"Unexpected file format for {data_table}: {file_path} - Missing Keys: {missing_keys}, Extra Keys: {extra_keys}, Mismatched Types: {mismatched_data_types}")
    return list(missing_keys), list(extra_keys), mismatched_data_types, False
    

meteo_params_df = get_meteo_params_ref()
if __name__  ==  '__main__':
    total_files = 0
    files = os.listdir(_RAW)
    try:
        os.remove('failures.txt')
    except OSError:
        pass
    sorted_files = sorted(files, key=lambda x: (int(x.split('_')[1]), int(x.split('_')[2].replace('.zip', ''))), reverse=True)
    # Make it parallel if needed?
    for zipfile in sorted_files:
        logger.info(f"Processing file {zipfile}")
        start_time = time.time()

        # Remove this if you want to process all files in folder
        if zipfile.split('_')[1] == '2023':
            break
        fname = zipfile.replace('.zip', '')
        overall_start = time.time()
        parquet_file = f"{_PROCESSED}{fname}.parquet"
        if os.path.exists(parquet_file):
            logger.info(f"File already exists: {parquet_file}")
            continue

        all_dfs = []
        r = extract_data_for_zipfile(zipfile)
        total_files += 1
        end_time = time.time()
        logger.info(f'File {zipfile} took {end_time - start_time} to finish.')

    for target_table in [
        'NORMAL',
        'OBSERVATION',
        'REANALYSIS', 
        'FORECAST_DETERMINISTIC',
        'FORECAST_ENSEMBLE',
        'FORECAST_EXTENDED']:
        adaptor.load_generic(
            target_table, 
            stage_name,
            f"{stage_path}/{target_table}",
            "(TYPE = PARQUET) MATCH_BY_COLUMN_NAME='CASE_INSENSITIVE'"
        )
    print(f"Total JSON Files processed: {total_files}")
