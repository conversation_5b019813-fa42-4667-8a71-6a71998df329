apiVersion: v1
kind: Secret
metadata:
  name: grafana-secret
  namespace: monitoring
type: opaque
data:
  loki-k8s-monitoring-host: ${LOKI_K8S_MONITORING_HOST}
  loki-k8s-monitoring-username: ${GRAFANA_LOKI_ACCOUNT_ID}
  loki-k8s-monitoring-password: ${GRAFANA_LOKI_PASSWORD}
  prometheus-k8s-monitoring-host: ${PROMETHEUS_K8S_MONITORING_HOST}
  prometheus-k8s-monitoring-username: ${GRAFANA_PROM_ACCOUNT_ID}
  prometheus-k8s-monitoring-password: ${GRAFANA_PROM_PASSWORD}
  tempo-k8s-monitoring-host: ${TEMPO_K8S_MONITORING_HOST}
  tempo-k8s-monitoring-username: ${GRAFANA_TEMPO_ACCOUNT_ID}
  tempo-k8s-monitoring-password: ${GRAFANA_TEMPO_PASSWORD}