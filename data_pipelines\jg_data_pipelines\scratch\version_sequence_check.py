import os
import logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_sequence(jfs_dir, incoming_file_list=None, file_type=None):
    files_in_jfs = sorted(os.listdir(jfs_dir))
    all_sequences = [int(file.split('_')[-1].split('.')[0]) for file in files_in_jfs if file.startswith(file_type)]
    if incoming_file_list and file_type:
        sequences_in_ftp = [int(file.split('_')[-1].split('.')[0]) for file in incoming_file_list]
        all_sequences = set(all_sequences + sequences_in_ftp)
    missing_sequences = []
    if all_sequences:
        missing_sequences = [seq for seq in range(min(all_sequences), max(all_sequences) + 1) if seq not in all_sequences]
    if missing_sequences:
        logger.error(f"Missing file versions in {jfs_dir}: {missing_sequences}")
    else:
        logger.info(f"No file versions are missing in {jfs_dir}\n")

if __name__ == "__main__":
    check_sequence("/jfs/tech1/apps/rawdata/factset/fundamentals/ff_basic_am_v3", file_type='ff_basic_am_v3')