create or replace view vw_forecast_extended as
with consolidated as (
    SELECT * FROM meteologica.api.forecast_extended
    WHERE issue_date < DATE('2025-01-01')
    UNION ALL 
    SELECT * FROM meteologica.ongoing.forecast_extended
    WHERE issue_date >= DATE('2025-01-01')
), forecast_ensemble as(
    select content_id, content_name, issue_date, timezone, unit, installed_capacity, update_id, source_file,
        -- to_varchar(data:"From yyyy-mm-dd hh:mm") from_datetime,
        -- to_varchar(data:"To yyyy-mm-dd hh:mm") to_datetime,
        
        -- Handle different JSON structures and null data for from_datetime
        COALESCE(
            to_varchar(data:"From yyyy-mm-dd hh:mm"),
            CASE 
                WHEN to_varchar(data:"Date yyyy-mm-dd") IS NOT NULL 
                THEN to_varchar(data:"Date yyyy-mm-dd") || ' 00:00'
                ELSE NULL
            END
        ) as from_datetime,
        
        -- Handle different JSON structures and null data for to_datetime
        COALESCE(
            to_varchar(data:"To yyyy-mm-dd hh:mm"),
            CASE 
                WHEN to_varchar(data:"Date yyyy-mm-dd") IS NOT NULL 
                THEN to_varchar(data:"Date yyyy-mm-dd") || ' 23:59'
                ELSE NULL
            END
        ) as to_datetime,
        COALESCE(
            to_varchar(data:"UTC offset from (UTC+/-hhmm)"),
            CASE 
                WHEN to_varchar(data:"UTC offset to (UTC+/-hhmm)") IS NOT NULL 
                THEN to_varchar(data:"UTC offset to (UTC+/-hhmm)")
                ELSE 'UTC+0000'
            END
        ) as utc_offset_from,
        COALESCE(   
            to_varchar(data:"UTC offset to (UTC+/-hhmm)"),
            CASE 
                WHEN to_varchar(data:"UTC offset from (UTC+/-hhmm)") IS NOT NULL 
                THEN to_varchar(data:"UTC offset from (UTC+/-hhmm)")
                ELSE 'UTC+0000'
            END
        ) as utc_offset_to,

        to_varchar(data:"Average") average,
        to_varchar(data:"Bottom") bottom,
        to_varchar(data:"Top") top,
        to_varchar(data:"ENS00") ENS00,
        to_varchar(data:"ENS01") ENS01,
        to_varchar(data:"ENS02") ENS02,
        to_varchar(data:"ENS03") ENS03,
        to_varchar(data:"ENS04") ENS04,
        to_varchar(data:"ENS05") ENS05,
        to_varchar(data:"ENS06") ENS06,
        to_varchar(data:"ENS07") ENS07,
        to_varchar(data:"ENS08") ENS08,
        to_varchar(data:"ENS09") ENS09,
        to_varchar(data:"ENS10") ENS10,
        to_varchar(data:"ENS11") ENS11,
        to_varchar(data:"ENS12") ENS12,
        to_varchar(data:"ENS13") ENS13,
        to_varchar(data:"ENS14") ENS14,
        to_varchar(data:"ENS15") ENS15,
        to_varchar(data:"ENS16") ENS16,
        to_varchar(data:"ENS17") ENS17,
        to_varchar(data:"ENS18") ENS18,
        to_varchar(data:"ENS19") ENS19,
        to_varchar(data:"ENS20") ENS20,
        to_varchar(data:"ENS21") ENS21,
        to_varchar(data:"ENS22") ENS22,
        to_varchar(data:"ENS23") ENS23,
        to_varchar(data:"ENS24") ENS24,
        to_varchar(data:"ENS25") ENS25,
        to_varchar(data:"ENS26") ENS26,
        to_varchar(data:"ENS27") ENS27,
        to_varchar(data:"ENS28") ENS28,
        to_varchar(data:"ENS29") ENS29,
        to_varchar(data:"ENS30") ENS30,
        to_varchar(data:"ENS31") ENS31,
        to_varchar(data:"ENS32") ENS32,
        to_varchar(data:"ENS33") ENS33,
        to_varchar(data:"ENS34") ENS34,
        to_varchar(data:"ENS35") ENS35,
        to_varchar(data:"ENS36") ENS36,
        to_varchar(data:"ENS37") ENS37,
        to_varchar(data:"ENS38") ENS38,
        to_varchar(data:"ENS39") ENS39,
        to_varchar(data:"ENS40") ENS40,
        to_varchar(data:"ENS41") ENS41,
        to_varchar(data:"ENS42") ENS42,
        to_varchar(data:"ENS43") ENS43,
        to_varchar(data:"ENS44") ENS44,
        to_varchar(data:"ENS45") ENS45,
        to_varchar(data:"ENS46") ENS46,
        to_varchar(data:"ENS47") ENS47,
        to_varchar(data:"ENS48") ENS48,
        to_varchar(data:"ENS49") ENS49,
        to_varchar(data:"ENS50") ENS50,
        to_varchar(data:"ENS51") ENS51,
        to_varchar(data:"ENS52") ENS52,
        to_varchar(data:"ENS53") ENS53,
        to_varchar(data:"ENS54") ENS54,
        to_varchar(data:"ENS55") ENS55,
        to_varchar(data:"ENS56") ENS56,
        to_varchar(data:"ENS57") ENS57,
        to_varchar(data:"ENS58") ENS58,
        to_varchar(data:"ENS59") ENS59,
        to_varchar(data:"ENS60") ENS60,
        to_varchar(data:"ENS61") ENS61,
        to_varchar(data:"ENS62") ENS62,
        to_varchar(data:"ENS63") ENS63,
        to_varchar(data:"ENS64") ENS64,
        to_varchar(data:"ENS65") ENS65,
        to_varchar(data:"ENS66") ENS66,
        to_varchar(data:"ENS67") ENS67,
        to_varchar(data:"ENS68") ENS68,
        to_varchar(data:"ENS69") ENS69,
        to_varchar(data:"ENS70") ENS70,
        to_varchar(data:"ENS71") ENS71,
        to_varchar(data:"ENS72") ENS72,
        to_varchar(data:"ENS73") ENS73,
        to_varchar(data:"ENS74") ENS74,
        to_varchar(data:"ENS75") ENS75,
        to_varchar(data:"ENS76") ENS76,
        to_varchar(data:"ENS77") ENS77,
        to_varchar(data:"ENS78") ENS78,
        to_varchar(data:"ENS79") ENS79,
        to_varchar(data:"ENS80") ENS80,
        to_varchar(data:"ENS81") ENS81,
        to_varchar(data:"ENS82") ENS82,
        to_varchar(data:"ENS83") ENS83,
        to_varchar(data:"ENS84") ENS84,
        to_varchar(data:"ENS85") ENS85,
        to_varchar(data:"ENS86") ENS86,
        to_varchar(data:"ENS87") ENS87,
        to_varchar(data:"ENS88") ENS88,
        to_varchar(data:"ENS89") ENS89,
        to_varchar(data:"ENS90") ENS90,
        to_varchar(data:"ENS91") ENS91,
        to_varchar(data:"ENS92") ENS92,
        to_varchar(data:"ENS93") ENS93,
        to_varchar(data:"ENS94") ENS94,
        to_varchar(data:"ENS95") ENS95,
        to_varchar(data:"ENS96") ENS96,
        to_varchar(data:"ENS97") ENS97,
        to_varchar(data:"ENS98") ENS98,
        to_varchar(data:"ENS99") ENS99,
        to_varchar(data:"ENS100") ENS100
        from consolidated
), forecast_ensemble_utc as (
    select content_id, content_name, 
    issue_date,
    unit, installed_capacity, update_id, source_file,
    to_timestamp_ntz(convert_timezone(
            'UTC', 
            TO_TIMESTAMP_TZ(FROM_DATETIME || ' ' || split(utc_offset_from, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
        )) FROM_DATETIME,
    to_timestamp_ntz(convert_timezone(
            'UTC', 
            TO_TIMESTAMP_TZ(TO_DATETIME || ' ' || split(utc_offset_to, 'UTC')[1], 'YYYY-MM-DD HH24:MI TZHTZM')
        )) TO_DATETIME,
        average, bottom, top,
        ENS00,
        ENS01,
        ENS02,
        ENS03,
        ENS04,
        ENS05,
        ENS06,
        ENS07,
        ENS08,
        ENS09,
        ENS10,
        ENS11,
        ENS12,
        ENS13,
        ENS14,
        ENS15,
        ENS16,
        ENS17,
        ENS18,
        ENS19,
        ENS20,
        ENS21,
        ENS22,
        ENS23,
        ENS24,
        ENS25,
        ENS26,
        ENS27,
        ENS28,
        ENS29,
        ENS30,
        ENS31,
        ENS32,
        ENS33,
        ENS34,
        ENS35,
        ENS36,
        ENS37,
        ENS38,
        ENS39,
        ENS40,
        ENS41,
        ENS42,
        ENS43,
        ENS44,
        ENS45,
        ENS46,
        ENS47,
        ENS48,
        ENS49,
        ENS50,
        ENS51,
        ENS52,
        ENS53,
        ENS54,
        ENS55,
        ENS56,
        ENS57,
        ENS58,
        ENS59,
        ENS60,
        ENS61,
        ENS62,
        ENS63,
        ENS64,
        ENS65,
        ENS66,
        ENS67,
        ENS68,
        ENS69,
        ENS70,
        ENS71,
        ENS72,
        ENS73,
        ENS74,
        ENS75,
        ENS76,
        ENS77,
        ENS78,
        ENS79,
        ENS80,
        ENS81,
        ENS82,
        ENS83,
        ENS84,
        ENS85,
        ENS86,
        ENS87,
        ENS88,
        ENS89,
        ENS90,
        ENS91,
        ENS92,
        ENS93,
        ENS94,
        ENS95,
        ENS96,
        ENS97,
        ENS98,
        ENS99,
        ENS100
        from forecast_ensemble
)
select * from forecast_ensemble_utc;

select count(*) from vw_forecast_extended limit 100;
select * from vw_forecast_extended limit 100;