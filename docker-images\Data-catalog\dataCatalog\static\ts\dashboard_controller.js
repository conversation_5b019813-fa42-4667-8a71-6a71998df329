"use strict";
class DashboardController {
    constructor() {
        this.model = new DashboardModel();
        // Adding an event listener to the slider in the document
        this.attachDocumentListeners();
    }
    attachDocumentListeners() {
        const frequencySlider = document.getElementById('frequencySlider');
        if (frequencySlider) {
            frequencySlider.addEventListener('input', () => {
                this.adjustTickFrequency(parseInt(frequencySlider.value));
            });
        }
    }
    adjustTickFrequency(ticksPerSecond) {
        let displayText;
        displayText = `${1 / (1 / (ticksPerSecond / 10))} `;
        this.model.freqDisplay = displayText;
        console.log('Updating frequency to:', displayText); // Debugging log
        // Assuming a FastAPI server endpoint is set up to receive frequency updates
        fetch(`${this.model.baseUrl}/api/update-frequency`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ frequency: ticksPerSecond / 10 }) // converting slider value to actual frequency
        })
            .then(response => response.json())
            // .then(data => console.log('Server response:', data))
            .catch(error => console.error('Error:', error));
    }
}
