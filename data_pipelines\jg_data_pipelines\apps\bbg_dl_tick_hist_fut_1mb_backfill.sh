echo "Backfilling Futures 1 min bars using DL Tick Hist."

source $HOME/.bashrc

set -e

python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type BID --month_year 2025_01 >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_bid_bf.log 2>&1
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type BID --month_year 2025_02 >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_bid_bf.log 2>&1
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type BID --month_year 2025_03 >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_bid_bf.log 2>&1
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type BID --month_year 2025_04 >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_bid_bf.log 2>&1

python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type ASK --month_year 2025_01 >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_ask_bf.log 2>&1
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type ASK --month_year 2025_02 >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_ask_bf.log 2>&1
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type ASK --month_year 2025_03 >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_ask_bf.log 2>&1
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_tick_hist_fut_1mb.py --bar_type ASK --month_year 2025_04 >> /home/<USER>/logs/bbg_dl_tick_hist_fut_1mb_ask_bf.log 2>&1


echo "Backfilled Futures 1 min bars using DL Tick Hist."
