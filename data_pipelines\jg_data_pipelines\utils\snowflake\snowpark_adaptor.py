import logging
import os


from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric import rsa
from cryptography.hazmat.primitives.asymmetric import dsa
from cryptography.hazmat.primitives import serialization
from snowflake.snowpark import Session
from snowflake.snowpark.functions import when_matched, when_not_matched
from snowflake.snowpark.functions import is_null
from snowflake.snowpark.functions import lit


class SnowparkAdaptor:
    __SF_ACCOUNT = "byb06077.us-east-1"
    __SF_WAREHOUSE = None
    __SF_DATABASE = None
    __SF_ROLE = None
    __SF_SCHEMA = None

    def __init__(self, database=None, schema=None, warehouse=None, role=None):
        if database:
            self.__SF_DATABASE = database

        if warehouse:
            self.__SF_WAREHOUSE = warehouse

        if role:
            self.__SF_ROLE = role
        
        if schema:
            self.__SF_SCHEMA = schema

        self.session = None
        self.setup_logging()
        self.connect()
    
    def setup_logging(self):
        """Configure basic logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

    def connect(self, role=None):
        """Establish connection to Snowflake"""
        try:
            if not role:
                role = self.__SF_ROLE
            """Initialize Snowflake connection."""        
            sf_pk_file = os.getenv('SF_PK_FILE')
            sf_pk_password = os.getenv('SF_PK_PASSWORD')
            sf_username = os.getenv('SF_USERNAME')
            sf_password = os.getenv('SF_PASSWORD')
            pk_file_auth = sf_username is not None and sf_pk_file is not None \
                and sf_pk_password is not None
            user_pass_auth = sf_username is not None \
                and sf_password is not None
            if not pk_file_auth and not user_pass_auth:
                    raise ValueError('You must either provide (SF_USERNAME, SF_PK_FILE, SF_PK_PASSWORD)' \
                                        'or (SF_USERNAME, SF_PASSWORD) env vars for Snowflake auth')
            elif pk_file_auth:
                logging.info("Snowflake authentication using Private Key File...")
                sf_password = None
            elif user_pass_auth:
                logging.info("Snowflake authentication using User/Password...")
                sf_pk_file = None
                sf_pk_password = None
            
            connection_parameters = {
                "account": self.__SF_ACCOUNT,
                "user": sf_username,
                "password": sf_password,
                "role": role,
                "private_key_file": sf_pk_file,
                "private_key_file_pwd": sf_pk_password,
                "warehouse": self.__SF_WAREHOUSE,
                "database": self.__SF_DATABASE,
                "schema": self.__SF_SCHEMA
            }

            self.session = Session.builder.configs(
                connection_parameters
            ).create()
            self.logger.info("Successfully created Snowpark session")
        except Exception as e:
            self.logger.error(f"Error creating Snowpark session: {str(e)}")
            raise
    
    def upsert_snowpark(self, df, target_table, merge_keys=[], only_insert=False):
        target = self.session.table(target_table)

        join_expr = None
        for mk in merge_keys:
            if join_expr is None:
                join_expr = (target[mk] == df[mk])
            else:
                join_expr = join_expr & (target[mk] == df[mk])
        values = {}
        for col in df.columns:
            values[col] = df[col]
        
        if only_insert:
            upsert_ret_val = target.merge(df, join_expr,
            [
                when_not_matched().insert(values)
            ])
        else:
            upsert_ret_val = target.merge(df, join_expr,
            [
                when_matched().update(values), 
                when_not_matched().insert(values)
            ])

        return upsert_ret_val

    def upsert(self, df, target_table, merge_keys=[], only_insert=False):
        for mk in merge_keys:
            assert not df[mk].isnull().any(), f"Merge key {mk} contains NULL values"
        df.columns = df.columns.str.upper().str.replace(' ', '')
        sdf = self.session.create_dataframe(df)
        return self.upsert_snowpark(sdf, target_table, merge_keys, only_insert)
