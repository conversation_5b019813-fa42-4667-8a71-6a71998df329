import os
import pandas as pd

def get_business_days(start_date: str, end_date: str) -> list:
    date_range = pd.date_range(
        start=pd.to_datetime(start_date, format='%Y%m%d'),
        end=pd.to_datetime(end_date, format='%Y%m%d'),
        freq='B'  # Business day frequency
    )
    return date_range.strftime('%Y%m%d').tolist()

if __name__ == "__main__":
    # business_days = get_business_days('20250101', '20250131')
    # ticker = "AAPL UW"
    # df_all_tickers = pd.DataFrame(columns=["tickerAndExchCode", "bday"])
    # df_ticks = pd.DataFrame()
    # for bd in business_days:
    #     th_file_path = f"/jfs/stshare/tickdata/equities/mk2/taqbin/na/2025/{bd}"
    #     id_file_path = f"/jfs/stshare/data/prod/0.1/na/identifiers/2025/{bd}"
    #     if os.path.exists(th_file_path) and os.path.exists(id_file_path):
    #         df_th = pd.read_parquet(th_file_path)
    #         df_id = pd.read_parquet(id_file_path)
    #         df_th = pd.merge(df_th,df_id[['uid','tickerAndExchCode']],on='uid')
    #         idx_ticker = df_th["tickerAndExchCode"] == ticker
    #         df_th = df_th[idx_ticker]
    #         df_ticks = pd.concat([df_ticks, df_th], ignore_index=True)
    #         print(f"Processed file for {bd}")
    #     else:
    #         print(f"File does not exist for {bd}")
    #         continue
    
    # df_ticks.to_csv("/jfs/tech1_share/pulkit.vora/analysis_1mb/st_aapl_1mb.csv", index=False)

    df_all_ticks_st = pd.read_csv("/jfs/tech1_share/pulkit.vora/analysis_1mb/st_aapl_1mb.csv")
    print(df_all_ticks_st.dtypes)
    df_all_ticks_st["timestamp"] = pd.to_datetime(df_all_ticks_st["timestamp"])
    df_all_ticks_st = df_all_ticks_st.set_index("timestamp")
    df_all_ticks_bbg = pd.read_csv("/jfs/tech1_share/pulkit.vora/analysis_1mb/bbg_aapl_1mb.csv")
    print(df_all_ticks_bbg.dtypes)
    df_all_ticks_bbg["TH_BAR_TIME"] = pd.to_datetime(df_all_ticks_bbg["TH_BAR_TIME"])
    df_all_ticks_bbg = df_all_ticks_bbg.set_index("TH_BAR_TIME")
    
    from_datetime = '2025-01-30 14:29:00'
    to_datetime = '2025-01-30 21:15:00'
    
    from_dt = pd.to_datetime(from_datetime)
    to_dt = pd.to_datetime(to_datetime)
    df_all_ticks_st_filtered = df_all_ticks_st[(df_all_ticks_st.index >= from_dt) & (df_all_ticks_st.index <= to_dt)]
    df_all_ticks_bbg_filtered = df_all_ticks_bbg[(df_all_ticks_bbg.index >= from_dt) & (df_all_ticks_bbg.index <= to_dt)]
    df_all_ticks_st_filtered = df_all_ticks_st_filtered[["last_tp"]]
    df_all_ticks_st_filtered.rename(columns={"last_tp": "ST_CLOSE"}, inplace=True)
    df_all_ticks_bbg_filtered = df_all_ticks_bbg_filtered[["TH_BAR_CLOSE"]]
    df_all_ticks_bbg_filtered.rename(columns={"TH_BAR_CLOSE": "BBG_CLOSE"}, inplace=True)
    print(df_all_ticks_bbg_filtered.head(500))
    df_combined = df_all_ticks_st_filtered.merge(df_all_ticks_bbg_filtered, left_index=True, right_index=True, how='outer')
    
    # print(df_combined.dtypes)
    # print(df_combined.head(500))
    # print(df_combined.shape)
    # df_combined.to_csv("/jfs/tech1_share/pulkit.vora/analysis_1mb/aapl_1mb_combined.csv", index=True)