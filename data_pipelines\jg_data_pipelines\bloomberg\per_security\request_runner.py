import shutil
import time
from pathlib import Path
from utils.file import temp_folder
from utils.sftp import SftpManager
from bloomberg.per_security.request_builder import Re<PERSON><PERSON><PERSON><PERSON>, RequestConfig, PerSecurityRequestType, PerSecurityResponseOutExtn
from bloomberg.per_security.config import sftp_kwargs

class PerSecurityRequestRunner:
    def __init__(self, batch_name: str, per_sec_req_type: PerSecurityRequestType, request_dict: dict, target_folder: str):
        self.per_sec_req_type = per_sec_req_type
        self.batch_name = batch_name
        self.request_dict = request_dict
        self.target_folder = target_folder

    def run(self, append_timestamp: bool = False):
        response_file_path = ""
        with temp_folder() as tmp:
            try:
                request_builder = RequestBuilder(self.per_sec_req_type, tmp)
                request_config = RequestConfig(**self.request_dict)
                request_file = request_builder.build(self.batch_name, request_config, append_timestamp)
                print(request_file)
                request_file_archive_path = Path(self.target_folder, "requests")
                response_file_archive_path = Path(self.target_folder, "responses")
                print(request_file_archive_path)
                shutil.copy(request_file, request_file_archive_path)
                time.sleep(60)
                sftp_manager = SftpManager(**sftp_kwargs)
                sftp_manager.put(request_file, ".")
                filename = request_file.split('/')[-1]
                filename_no_extension = filename.split('.')[0]
                extension = PerSecurityResponseOutExtn[self.per_sec_req_type.value]
                lookup = f'{filename_no_extension}{extension}'
                
                if self.per_sec_req_type == PerSecurityRequestType.gettickhistory:
                    timeout = 60 * 30
                else:
                    timeout = 3000
                
                response = sftp_manager.long_poll(lookup, check_interval_seconds=30, timeout_seconds=timeout)
                result_file = sftp_manager.fetch(response, ".", tmp)
                shutil.copy(result_file, response_file_archive_path)
                response_file_path = f"{response_file_archive_path}/{result_file.split('/')[-1]}"
                sftp_manager.close()
            except Exception as e:
                raise e
            finally:
                shutil.rmtree(tmp)
        return response_file_path