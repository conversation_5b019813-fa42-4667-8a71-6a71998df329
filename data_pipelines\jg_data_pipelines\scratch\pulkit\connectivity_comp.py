import time
import statistics
import psycopg2

def test_psycopg2_connection(conn_params, num_trials=100):
    times = []
    for _ in range(num_trials):
        start_time = time.perf_counter()
        conn = psycopg2.connect(**conn_params)
        end_time = time.perf_counter()
        conn.close()
        times.append(end_time - start_time)
    return times

def print_statistics(times, label):
    print(f"\n{label} Statistics:")
    print(f"Average time: {statistics.mean(times):.6f} seconds")
    print(f"Median time: {statistics.median(times):.6f} seconds")
    print(f"Min time: {min(times):.6f} seconds")
    print(f"Max time: {max(times):.6f} seconds")
    print(f"Standard deviation: {statistics.stdev(times):.6f} seconds")

def main():
    # Connection parameters
    conn_params = {
        'dbname': "postgres",
        'user': '',
        'password': '',
        'host': 'tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com',
        'port': '5432'
    }
    
    # Number of trials
    num_trials = 20
    
    print(f"Running {num_trials} connection trials for each library...")
    
    # Test psycopg2
    psycopg2_times = test_psycopg2_connection(conn_params, num_trials)
    print_statistics(psycopg2_times, "psycopg2")
    
    # Compare averages
    psycopg2_avg = statistics.mean(psycopg2_times)
    
    print("\nComparison:")
    print(f"psycopg2 avg is {psycopg2_avg:.2f}.")

if __name__ == "__main__":
    main()
