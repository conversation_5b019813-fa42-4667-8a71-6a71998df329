use role sysadmin;
use database poc_db;
create schema meteologica;
grant ownership on schema meteologica to role fr_data_platform;

use role FR_DATA_PLATFORM;

create or replace TABLE normal (
	content_id NUMBER(38,0),
	content_name VARCHAR(16777216),
	data VARIANT default null,
	installed_capacity VARCHAR(16777216) default null,
	issue_date VARCHAR(16777216),
	timezone VARCHAR(16777216),
	unit VARCHAR(16777216),
	update_id STRING,
    source_file STRING
);

create OR REPLACE TABLE reanalysis (
	content_id NUMBER(38,0),
	content_name VARCHAR(16777216),
	data VARIANT default null,
	installed_capacity VARCHAR(16777216) default null,
	issue_date VARCHAR(16777216),
	timezone VARCHAR(16777216),
	unit VARCHAR(16777216),
	update_id STRING,
    source_file STRING
);

create OR REPLACE TABLE observation (
	content_id NUMBER(38,0),
	content_name VARCHAR(16777216),
	data VARIANT default null,
	installed_capacity VARCHAR(16777216) default null,
	issue_date VARCHAR(16777216),
	timezone VARCHAR(16777216),
	unit VARCHAR(16777216),
	update_id STRING,
    source_file STRING
);

create or replace TABLE FORECAST_DETERMINISTIC (
	content_id NUMBER(38,0),
	content_name VARCHAR(16777216),
	data VARIANT default null,
	installed_capacity VARCHAR(16777216) default null,
	issue_date VARCHAR(16777216),
	timezone VARCHAR(16777216),
	unit VARCHAR(16777216),
	update_id STRING,
    source_file STRING
);

create OR REPLACE TABLE FORECAST_EXTENDED (
	content_id NUMBER(38,0),
	content_name VARCHAR(16777216),
	data VARIANT default null,
	installed_capacity VARCHAR(16777216) default null,
	issue_date VARCHAR(16777216),
	timezone VARCHAR(16777216),
	unit VARCHAR(16777216),
	update_id STRING,
    source_file STRING
);

create OR REPLACE TABLE FORECAST_ENSEMBLE (
	content_id NUMBER(38,0),
	content_name VARCHAR(16777216),
	data VARIANT default null,
	installed_capacity VARCHAR(16777216) default null,
	issue_date VARCHAR(16777216),
	timezone VARCHAR(16777216),
	unit VARCHAR(16777216),
	update_id STRING,
    source_file STRING
);

list '@SAM_TEST/';