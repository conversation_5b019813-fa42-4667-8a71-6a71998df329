"use strict";
class DataCatalogModel extends ModelBase {
    constructor() {
        super();
        this.baseUrl = "";
        this.FeedName = "";
        this.FeedDescription = "";
        this.VendorContactDetails = "";
        this.Credentials = "";
        this.JGDataOwner = "";
        this.GrafanaAlerts = "";
        this.HistoricalFeeds = "";
        this.DailyFeeds = "";
        this.Historical_FilePatternsExpected = "";
        this.Daily_FilePatternsExpected = "";
        this.TablesLoaded = "";
        this.SLA = "";
        this.AirflowDagName = "";
        this.Estimated_Files = "";
        this.SlackChannelName = "";
        this.TeamsChannelName = "";
        // this.defineProperties();
        // this.setDefaultValues();
        // Initialize properties with default values or from URL parameters so assignments trigger HTML updates
        this.baseUrl = `${window.location.protocol}//${window.location.host}`;
        this.urlParams = new URLSearchParams(window.location.search);
        // Ensure the properties are strings, provide a default value if null
        // this.dataSetId = this.urlParams.get("Dataset_Id") || "defaultDataSetId";
        // Logger.info(`DataSet ID: ${this.dataSetId}`);
        // if (!this.dataSetId) {
        //     Logger.error("DataSet ID not found in URL");
        // } else {
        //     Logger.info(`DataSet ID: ${this.dataSetId}`);
        // }
    }
    setDefaultValues() {
        this.FeedName = "Please select a data set to view the FeedName.";
        this.FeedDescription = "Please select a data set to view the FeedDescription.";
        this.VendorContactDetails = "Please select a data set to view the Vendor Contact Details.";
        this.SourcingURL = "Please select a data set to view the Sourcing URL.";
        this.Credentials = "Please select a data set to view the Credentials.";
        this.JGDataOwner = "Please select a data set to view the JG Data Owner.";
        this.GrafanaAlerts = "Please select a data set to view the Grafana Alerts.";
        this.HistoricalFeeds = "Please select a data set to view the Historical Feeds.";
        this.DailyFeeds = "Please select a data set to view the Daily Feeds.";
        this.Historical_FilePatternsExpected = "Please select a data set to view the FilePatternsExpected(Historical).";
        this.Daily_FilePatternsExpected = "Please select a data set to view the FilePatternsExpected(Daily).";
        this.TablesLoaded = "Please select a data set to view the Tables Loaded.";
        this.SLA = "Please select a data set to view the SLA.";
        this.AirflowDagName = "Please select a data set to view the AirflowDagName.";
        this.Estimated_Files = "Please select a data set to view the Estimated Files.";
        this.SlackChannelName = "Please select a data set to view the Slack Channel Name.";
        this.TeamsChannelName = "Please select a data set to view the Teams Channel Name.";
    }
    defineProperties() {
        // this.defineProperty("DataSet", "DataSet", true);
        this.defineProperty("Dataset_Id", "Dataset_Id", true);
        this.defineProperty("FeedName", "FeedName", true);
        this.defineProperty("FeedDescription", "FeedDescription", true);
        this.defineProperty("VendorContactDetails", "VendorContactDetails", true);
        this.defineProperty("SourcingStrategy", "SourcingStrategy", true);
        this.defineProperty("SourcingURL", "SourcingURL", true);
        this.defineProperty("Credentials", "Credentials", true);
        this.defineProperty("JGDataOwner", "JGDataOwner", true);
        this.defineProperty("GrafanaAlerts", "GrafanaAlerts", true);
        this.defineProperty("HistoricalFeeds", "HistoricalFeeds", true);
        this.defineProperty("DailyFeeds", "DailyFeeds", true);
        this.defineProperty("FilePatternsExpected_Historical", "FilePatternsExpected_Historical", true);
        this.defineProperty("FilePatternsExpected_Daily", "FilePatternsExpected_Daily", true);
        this.defineProperty("TablesLoaded", "TablesLoaded", true);
        this.defineProperty("SLA", "SLA", true);
        this.defineProperty("AirflowDagName", "AirflowDagName", true);
        this.defineProperty("Estimated#Files", "Estimated#Files", true);
        this.defineProperty("SlackChannelName", "SlackChannelName", true);
        this.defineProperty("Teams_Channel_Name", "Teams_Channel_Name", true);
    }
}
