FROM ubuntu:22.04

# Force cache busting for console output
ARG USERNAME
ARG LOGIN_KEY
ENV USERNAME=$USERNAME
ENV LOGIN_KEY=$LOGIN_KEY

USER root

# Update package sources and install necessary utilities
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    wget \
    curl \
    vim \
    jq \
    less \
    dos2unix \
    kmod \
    fuse3 \
    findutils \
    openssh-client \
    procps \
    gettext \
    unzip \
    alien \
    libkrb5-dev \
    python3-pip \
    python3-setuptools \
    python3-venv \
    cron \
    systemctl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Install yq (correct version from mikefarah/yq)
RUN wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/latest/download/yq_linux_amd64 && \
    chmod +x /usr/local/bin/yq

# Install specific RPM packages
COPY /docker-images/cwiqfs/fuse-libs-2.9.2-11.el7.x86_64.rpm  /tmp
COPY /docker-images/cwiqfs/fuse-sshfs-2.10-1.el7.x86_64.rpm /tmp
COPY /docker-images/cwiqfs/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm /tmp
RUN alien -i /tmp/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm

# Ensure the symbolic link exists for certificates
RUN ln -sf /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-bundle.crt

# Copy and configure cwiqfs configuration
COPY docker-images/cwiqfs/cwiqfs.yaml /etc/cwiq/cwiqfs/config_temp.yaml
RUN sed -e "s/\${USERNAME}/$USERNAME/g" -e "s/\${LOGIN_KEY}/$LOGIN_KEY/g" /etc/cwiq/cwiqfs/config_temp.yaml > /etc/cwiq/cwiqfs/config.yaml
RUN sed -i -e 's/# user_allow_other/user_allow_other/g' /etc/fuse.conf
RUN mkdir -p /var/tmp/cache /var/log/cwiq && chmod 777 /var/tmp/cache

# Copy requirements.txt
COPY docker-images/requirements_Monitoring.txt /tmp/requirements.txt

RUN python3 -m pip install --upgrade pip
# Install Python dependencies
RUN pip install --no-cache-dir -r /tmp/requirements.txt

# Create directories and user
RUN mkdir /jfs && \
    mkdir -p /usr/lib/jfs && \
    chmod -R 755 /usr/lib/jfs && \
    useradd -ms /bin/bash jsvc-tech1

COPY docker-images/grafana/run-pod-with-mount.sh  /etc/cwiqfs/run-pod-with-mount.sh
RUN chmod +x /etc/cwiqfs/run-pod-with-mount.sh

# Install Node Exporter
RUN wget https://github.com/prometheus/node_exporter/releases/download/v1.3.1/node_exporter-1.3.1.linux-amd64.tar.gz && \
    tar -xvzf node_exporter-1.3.1.linux-amd64.tar.gz && \
    mv node_exporter-1.3.1.linux-amd64/node_exporter /usr/local/bin/ && \
    rm -rf node_exporter-1.3.1.linux-amd64*

# Install Promtool
RUN wget -qO /tmp/prometheus.tar.gz https://github.com/prometheus/prometheus/releases/download/v2.45.0/prometheus-2.45.0.linux-amd64.tar.gz \
    && tar -xzf /tmp/prometheus.tar.gz -C /tmp \
    && mv /tmp/prometheus-2.45.0.linux-amd64/promtool /usr/local/bin/ \
    && chmod +x /usr/local/bin/promtool \
    && rm -rf /tmp/*

# Create directory for Node Exporter textfile collector
RUN mkdir -p /etc/node_exporter/textfile && chmod -R 777 /etc/node_exporter/textfile

# Expose ports
EXPOSE 9100
EXPOSE 22

CMD ["/bin/bash"]