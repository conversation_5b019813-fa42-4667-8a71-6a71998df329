import os

from utils.postgres.adaptor import PostgresAdaptor

from spiderrock.mlink_restapi import (
    invoke_mlink_rest_api,
    SpiderRockMessageType,
    DICT_MARKET_STATUS,
)

if __name__ == "__main__":
    # loader = PostgresAdaptor(
    #     # host="tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com",
    #     host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
    #     database="fe_risk",
    #     schema="eqvol",
    #     user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
    #     password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    # )

    # df_securities = loader.execute_query(
    #     "select security_code_id, security_code from eqvol.sr_security_ref;"
    # )
    # dict_securities = dict(
    #     zip(df_securities["security_code"], df_securities["security_code_id"])
    # )
    # print(dict_securities)

    # print(dict_securities.keys())

    securities = [
        'TSLA'
    ]

    (df_stock_quotes, opts_api_req_time, opts_api_ref_date) = invoke_mlink_rest_api(
        tickers=securities,
        messageTyp=SpiderRockMessageType.StockQuote,
    )
    print(df_stock_quotes.shape)
    print(df_stock_quotes.head())

    (df_options_quotes, opts_api_req_time, opts_api_ref_date) = invoke_mlink_rest_api(
        tickers=securities,
        messageTyp=SpiderRockMessageType.OptionNbboQuote,
    )

    print(df_options_quotes.shape)
    print(df_options_quotes.head())

    