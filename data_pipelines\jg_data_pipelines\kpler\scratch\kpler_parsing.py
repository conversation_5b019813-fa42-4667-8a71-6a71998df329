from pydantic import BaseModel, field_validator, ValidationError
from datetime import datetime
import json
import pandas as pd
from pathlib import Path
import logging
from collections import defaultdict
from typing import Optional
import re
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PowerAvailabilityPerUnits(BaseModel):
    timestamp: datetime  # Timestamp as datetime
    as_of: datetime  # As_of as datetime
    provider: str
    location: str
    unit: str
    unit_name: Optional[str]  # Make unit_name Optional to handle missing data
    production_code: Optional[str]  # Make production_code Optional to handle missing data
    asset_type: Optional[str]  # Make asset_type Optional to handle missing data
    level: str
    availability_amount: float
    type: str
    fuel_type: str
 
    @field_validator('timestamp', mode='before')
    def validate_timestamp(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)  # Ensure it converts to a datetime object if it's a string
        return v
   
    @field_validator('as_of', mode='before')
    def validate_as_of(cls, v):
        if isinstance(v, str):
            return datetime.fromisoformat(v)  # Ensure it converts to a datetime object if it's a string
        return v

def read_kpler_json(file_path):
    # print(f"Analyzing {file_path}")
    with open(file_path, 'r') as f:
        data = json.load(f)
    json_keys = data.keys()

    if "detail" in json_keys:
        if type(data["detail"]) == str:
            detail_value = data["detail"].lower()
            if "no production units for" in detail_value:
                return file_path, [], [], []

    expected_elements = {
        "provider": str,
        "location": str,
        "timezone": str,
        "metadata": dict,
        "data": dict,
        "index": list,
    }

    missing_keys = set(expected_elements.keys()) - set(json_keys)
    extra_keys = set(json_keys) - set(expected_elements.keys())

    mismatched_data_types = []
    
    for key, expected_type in expected_elements.items():
        if key in json_keys:
            if type(data[key]) != expected_type:
                mismatched_data_types.append(key)
            
    return file_path, list(missing_keys), list(extra_keys), mismatched_data_types

def analyze_json_structure(root_dir):
    total_files = 0

    for json_path in Path(root_dir).rglob('*.json'):
        total_files += 1
        file_path, missing_keys, extra_keys, mismatched_data_types = read_kpler_json(json_path)
        print(f"File {json_path}: Missing keys: {missing_keys}, Extra keys: {extra_keys}, Mismatched data types: {mismatched_data_types}")
    
    return total_files

def main(root_dir):
    # Root directory containing JSON files
    total_files = analyze_json_structure(root_dir)
    print(f"\nAnalyzed {total_files} JSON files")

def analyze_json_structure_for_specific_file(file_path):
    with open(file_path, 'r') as f:
        data = json.load(f)
    
    for key, value in data.items():
        val_type = type(value)
        print(f"{key}: {val_type}")
        print(f"Value: {value}")

def parse_log_file():
    
    pattern = r"""
    File\s(?P<filepath>.*\.json):\s+
    Missing\skeys:\s\[(?P<missing_keys>.*?)\],\s+
    Extra\skeys:\s\[(?P<extra_keys>.*?)\],\s+
    Mismatched\sdata\stypes:\s\[(?P<mismatched_types>.*?)\]
    """
    
    with open("kpler_parsing.log", 'r') as f:
        for line in f:
            match = re.match(pattern, line, re.VERBOSE)
            if match:
                # print(match.group('filepath'))
                dict_file_attributes = {
                    'filepath': match.group('filepath'),
                    'missing_keys': match.group('missing_keys').split(', ') if match.group('missing_keys') else [],
                    'extra_keys': match.group('extra_keys').split(', ') if match.group('extra_keys') else [],
                    'mismatched_types': match.group('mismatched_types').split(', ') if match.group('mismatched_types') else []
                }
                if len(dict_file_attributes['extra_keys']) > 0:
                    if len(dict_file_attributes['extra_keys']) == 1 and dict_file_attributes['extra_keys'][0] == "'detail'":
                        print(dict_file_attributes['filepath'])
                        # continue
                # if len(dict_file_attributes['missing_keys']) > 0 or len(dict_file_attributes['mismatched_types']) > 0 or len(dict_file_attributes['extra_keys']) > 0:
                #     print(dict_file_attributes["filepath"], dict_file_attributes["extra_keys"], dict_file_attributes["missing_keys"], dict_file_attributes["mismatched_types"])
    
def flatten_json_avail_per_unit_sam(self, data, **params):
        as_of = params.get('as_of', None)
        as_of = datetime.fromisoformat(as_of)
        flattened_data = []
        if 'index' not in data:
            logger.error(f"'index' key not found in data: {data}")
            return flattened_data
        for i, timestamp in enumerate(data['index']):
            for unit, values in data['data']['productions'].items():
                metadata = data['metadata'].get(unit, {})
                row = {
                    'timestamp': timestamp,
                    'as_of': as_of,
                    'provider': data['provider'],
                    'location': data.get('location', ''),
                    'unit': unit,
                    'unit_name': metadata.get('name', ''),
                    'production_code': metadata.get('production_code', ''),
                    'asset_type': metadata.get('asset_type', ''),
                    'level': '',
                    'availability_amount': values['data'][0],
                    'type': 'production',
                    'fuel_type': metadata.get('fuel_type', '')
                }
                try:
                    validated_row = PowerAvailabilityPerUnits(**row)
                    validated_row_dict = validated_row.model_dump()
                    validated_row_dict['timestamp'] = validated_row.timestamp.strftime('%Y-%m-%dT%H:%M:%SZ')
                    validated_row_dict['as_of'] = validated_row.as_of.strftime('%Y-%m-%dT%H:%M:%SZ')
                    flattened_data.append(validated_row_dict)
                except ValidationError as e:
                    logger.error(f"Validation failed for row: {row} - Error: {e}")
                    continue
                for generation, gen_values in values['generations'].items():
                    gen_metadata = data['metadata'].get(generation, {})
                    row = {
                        'timestamp': timestamp,
                        'as_of': as_of,
                        'provider': data['provider'],
                        'location': data.get('location', ''),
                        'unit': generation,
                        'unit_name': gen_metadata.get('name', ''),
                        'production_code': gen_metadata.get('production_code', ''),
                        'asset_type': gen_metadata.get('asset_type', ''),
                        'level': '',
                        'availability_amount': gen_values[0],
                        'type': 'generation',
                        'fuel_type': gen_metadata.get('fuel_type', '')
                    }
                    try:
                        validated_row = PowerAvailabilityPerUnits(**row)
                        validated_row_dict = validated_row.model_dump()
                        validated_row_dict['timestamp'] = validated_row.timestamp.strftime('%Y-%m-%dT%H:%M:%SZ')
                        validated_row_dict['as_of'] = validated_row.as_of.strftime('%Y-%m-%dT%H:%M:%SZ')
                        flattened_data.append(validated_row_dict)
                    except ValidationError as e:
                        logger.error(f"Validation failed for row: {row} - Error: {e}")
                        continue
        return flattened_data

def flatten_json_avail_per_unit(file_path):
    file_name = os.path.basename(file_path)
    as_of_date = extract_as_of_date(file_name)
    
    with open(file_path, 'r') as f:
        full_dict = json.load(f)
    
    provider = full_dict['provider']
    location = full_dict['location']
    timezone = full_dict['timezone']

    metadata = full_dict['metadata']
    data = full_dict["data"]["productions"]
    index_dates = full_dict["index"]
    df_all_data = pd.DataFrame(columns=["timestamp", "type", "availability_amount", "as_of", "provider", "country", "unit", "unit_name", "production_code", "asset_type", "fuel_type"])
    
    for key, value in metadata.items():
        plant_id = key
        plant_metadata = value
        
        if plant_id in data.keys():
            print(f"Productions Found for {plant_id}")
            plant_data = data[plant_id]
            
            if "data" in plant_data.keys():
                print(f"Data Found for {plant_id}")
                production_data = plant_data["data"]
                if len(index_dates) == len(production_data):
                    df_inner = pd.DataFrame({
                        "timestamp": index_dates,   
                        "type": "production",
                        "as_of": as_of_date,
                        "provider": provider,
                        "country": location,
                        "unit": plant_id,
                        "unit_name": plant_metadata.get('name', ''),
                        "production_code": plant_metadata.get('production_code', ''),
                        "asset_type": plant_metadata.get('asset_type', ''),
                        "fuel_type": plant_metadata.get('fuel_type', ''),
                        "availability_amount": production_data,
                        "timezone": timezone
                    })
                    df_all_data = pd.concat([df_all_data, df_inner], ignore_index=True)
                else:
                    ValueError(f"Length of index dates and production data are not equal for {plant_id} in {file_name}")                

            
            if "generations" in plant_data.keys():
                print(f"Generations Found for {plant_id}")
                generations_data_dict = plant_data["generations"]
                for power_gen_id in generations_data_dict.keys():
                    power_generation_data = generations_data_dict[power_gen_id]
                    print(len(power_generation_data), len(index_dates))
                    if len(index_dates) == len(power_generation_data):
                        df_inner = pd.DataFrame({
                            "timestamp": index_dates,   
                            "type": "generation",
                            "as_of": as_of_date,
                            "provider": provider,
                            "country": location,
                            "unit": power_gen_id,
                            "unit_name": plant_metadata.get('name', ''),
                            "production_code": plant_metadata.get('production_code', ''),
                            "asset_type": plant_metadata.get('asset_type', ''),
                            "fuel_type": plant_metadata.get('fuel_type', ''),
                            "availability_amount": power_generation_data,
                            "timezone": timezone
                        })
                        df_all_data = pd.concat([df_all_data, df_inner], ignore_index=True)
                    else:
                        ValueError(f"Length of index dates and generation data are not equal for {plant_id} in {file_name}")                
        else:
            print(f"Data Not Found for {plant_id}")
    
    df_all_data.reset_index(drop=True, inplace=True)
    
    print(df_all_data.shape)
    print(df_all_data.head())    
    
    # print(metadata)

def extract_as_of_date(filename):
    timestamp_str = filename.split('_')[0].replace('\\:', ':')
    return datetime.fromisoformat(timestamp_str)

if __name__ == "__main__":
    # analyze_json_structure_for_specific_file("/jfs/tech1/apps/rawdata/kpler_power/availability_units_yearly/2022-01-01T10:00:00+00:00/DE/fossil brown coal/2022-01/2022-01-01T10:00:00+00:00_DE_fossil brown coal_2022-01.json")
    # root_dir = "/jfs/tech1/apps/rawdata/kpler_power/availability_units_yearly/"
    # root_dir = "/jfs/tech1/apps/rawdata/kpler_power/availability_units_yearly/"
    # main(root_dir)
    # parse_log_file()
    file_path = "2025-02-15T10\:00\:00+00\:00_BE_nuclear_2025-01.json"
    
    # timestamp = extract_timestamp(file_path)
    # print(timestamp)
    
    flatten_json_avail_per_unit(file_path)
    
    # read_kpler_json(file_path)

    

