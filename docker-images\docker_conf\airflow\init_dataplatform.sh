#!/bin/sh

modprobe fuse
/usr/bin/cwiqfs -f /jfs -o cache_size_mb=10000 -o allow_other -o force_allow_non_admin_allow_other -o cwiqfs_yaml=/etc/cwiq/cwiqfs/config.yaml -o log_file=/var/log/cwiq/cwiqfs/cwiqfs_v2.log -o tmpdir=/var/tmp/cache -o audit_log_file=/var/log/cwiq/cwiqfs/audit_cwiqfs_v2.log &

sleep 100
export JGDATA_PATH='/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source'
export STCOMMON_PATH='/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/stcommon'
export RAWSTORE_ROOT='/jfs/tech1/apps/rawdata/'
export BUILDINPARALLEL=False
export SPARK_DIST_CLASSPATH='/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/jars/iceberg-spark-runtime.jar'
export CONFIG_PATH='/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source'

# if [ "$1" ]; then
#     echo "Running provided script: $1"
#     python3 "$1"
#     sleep 100
# else
#     echo "No script argument provided. Running default initialization."
# fi
