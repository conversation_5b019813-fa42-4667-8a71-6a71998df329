from pydantic import BaseModel
   
    
import datetime as _dt
from datetime import date

import pydantic as _pydantic


class _UserBase(_pydantic.BaseModel):
    email: str
    isAdmin: bool

class UserCreate(_UserBase):
    hashed_password: str
    isAdmin: bool
    

    class Config:
        orm_mode = True
        from_attributes=True


class User(_UserBase):
    id: int

    class Config:
        orm_mode = True
        from_attributes=True


class _DatasetBase(_pydantic.BaseModel):
    dataset_id: str
    dataset_name: str
    vendor: str
    dataset_description: str
    dataset_details: str
    status: str
    permission_group: str
    product_owner: str
    data_management_lead: str
    dm_lead_email: str
    dm_lead_phone: str
    dm_lead_mobile: str
    vendor_contact_other: str
    vendor_contact_title: str
    vendor_contact_work_phone: str
    vendor_contact_mobile: str
    vendor_contact_email: str
    raw_data_location: str
    process_data_location: str
    file_type: str
    update_frequency: str
    technical_notes: str
    github_repository: str
    support_document_link: str
    file_names: str
    vendor_feed_sla_est: str
    file_count: str
    vendor_feed_extraction_source: str
    vendor_feed_extraction_source_url: str
    credentials: str
    grafana_alerts: str
    dataset_billing_name: str
    tables_loaded: str
    airflow_dag_names: str
    slack_channel_name: str
    teams_channel_name: str
    vendor_account_manager: str
    vendor_account_manager_work_phone: str
    vendor_account_manager_mobile: str
    vendor_account_manager_email: str
    vendor_sales_specialist: str
    vendor_sales_specialist_work_phone: str
    vendor_sales_specialist_mobile: str
    vendor_sales_specialist_email: str
    vendor_technical_account_manager: str
    vendor_technical_account_manager_work_phone: str
    vendor_technical_account_manager_mobile: str
    vendor_technical_account_manager_email: str
    customer_success_manager_product: str
    customer_success_manager_product_work_phone: str
    customer_success_manager_product_mobile : str
    customer_success_manager_product_email: str
    last_updated_by: str
    users: str
    vendor_ticketing_portal: str
    vendor_customer_support_dl: str
    vendor_hotline_number: str
    sftp_details: str
    historical_data_start_date: date  


class DatasetCreate(_DatasetBase):
    pass


class Catalog(_DatasetBase):
    id: int
    owner_id: int
    date_last_updated: _dt.datetime

    class Config:
        orm_mode = True
        from_attributes=True
    