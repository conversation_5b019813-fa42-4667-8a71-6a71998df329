{"headers": {"Dataset_Name": "Dataset_Name", "Dataset_Id": "Dataset_Id", "Vendor": "<PERSON><PERSON><PERSON>", "Dataset_Description": "Dataset_Description", "Dataset_Details": "Dataset_Details", "Data_Management_Lead": "Data_Management_Lead", "DM_Lead_Email": "DM_Lead_Email", "DM_Lead_Phone": "DM_Lead_Phone", "DM_Lead_Mobile": "DM_Lead_Mobile", "Vendor_Contact_Other": "Vendor_Contact_Other", "Vendor_Contact_Title": "Vendor_Contact_Title", "Vendor_Contact_Work_Phone": "Vendor_Contact_Work_Phone", "Vendor_Contact_Mobile": "Vendor_Contact_Mobile", "Vendor_Contact_Email": "Vendor_Contact_Email", "Raw_Data_Location": "Raw_Data_Location", "Process_Data_Location": "Process_Data_Location", "File_Type": "File_Type", "Update_Frequency": "Update_Frequency", "Technical_Notes": "Technical_Notes", "GitHub_Repository": "GitHub_Repository", "Support_Document_Link": "Support_Document_Link", "File_names": "File_names", "Vendor_Feed_SLA_EST": "Vendor_Feed_SLA_EST", "File_Count": "File_Count", "Vendor_Feed_Extraction_Source": "Vendor_Feed_Extraction_Source", "Vendor_Feed_Extraction_Source_URL": "Vendor_Feed_Extraction_Source_URL", "Credentials": "Credentials", "Grafana_Alerts": "<PERSON><PERSON><PERSON>", "DataSet_Billing_Name": "DataSet_Billing_Name", "Tables_Loaded": "Tables_Loaded", "Airflow_Dag_Names": "Airflow_Dag_Names", "Slack_Channel_Name": "Slack_Channel_Name", "Teams_Channel_Name": "Teams_Channel_Name", "Vendor_Account_Manager": "Vendor_Account_Manager", "Vendor_Account_Manager_Work_Phone": "Vendor_Account_Manager_Work_Phone", "Vendor_Account_Manager_Mobile": "Vendor_Account_Manager_Mobile", "Vendor_Account_Manager_Email": "Vendor_Account_Manager_Email", "Vendor_Sales_Specialist": "Vendor_Sales_Specialist", "Vendor_Sales_Specialist_Work_Phone": "Vendor_Sales_Specialist_Work_Phone", "Vendor_Sales_Specialist_Mobile": "Vendor_Sales_Specialist_Mobile", "Vendor_Sales_Specialist_Email": "Vendor_Sales_Specialist_Email", "Vendor_Technical_Account_Manager": "Vendor_Technical_Account_Manager", "Vendor_Technical_Account_Manager_Work_Phone": "Vendor_Technical_Account_Manager_Work_Phone", "Vendor_Technical_Account_Manager_Mobile": "Vendor_Technical_Account_Manager_Mobile", "Vendor_Technical_Account_Manager_Email": "Vendor_Technical_Account_Manager_Email", "Customer_Success_Manager_product": "Customer_Success_Manager_product", "Customer_Success_Manager_product_Work_Phone": "Customer_Success_Manager_product_Work_Phone", "Customer_Success_Manager_product_Mobile": "Customer_Success_Manager_product_Mobile", "Customer_Success_Manager_product_Email": "Customer_Success_Manager_product_Email"}, "data": {"snpETF": {"Dataset_Id": "snpETF", "Vendor": "SnP", "Dataset_Description": "ETF Holdings", "Dataset_Details": "This dataset contains the ETF Holdings. SnP is providing a daily feed as well as 5 years work of history", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM_Lead_Mobile": "+****************", "Vendor_Contact_Other": "<PERSON><PERSON>", "Vendor_Contact_Title": "\nClient Services Analyst\nS&P Global Market Intelligence", "Vendor_Contact_Work_Phone": "+************ \n+1212-849-3733", "Vendor_Contact_Mobile": "******-752-3269", "Vendor_Contact_Email": "<EMAIL>", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "/jfs/datalake/data/prod/1.0/cache/snp/feeds", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "This contains the ETF holdings data. The FTP server contains 2 variations of files. Each day has around 7.5-8K files, that need to get processed.\n\nHistorical files contain one zip file per month and one additional file per quarter.\nEach zip file contains a folder per day\nEach day folder contains multiple files per day.\nEach file contains 5 datasets\n\nIn FTP for the current dates, we have a day folder contains multiple files per day. Structure is similar to the historical folder.\nEach file contains 5 datasets, similar to he ones in the Historical Folders.", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Document_Link": "SNP - Overview (azure.com)", "File_names": "Historical\nYYYY-MM.zip\nYYYY-MMDD.zip (for the last day fo the quarter)\nEach zip file will have thse file patterns\nETF_XXXXX_YYYYMMDD.txt\nETF_XXXXX_XXXXX_YYYYMMDD_XXXXX.txt\n\nDaily:\nETF_XXXXX_YYYYMMDD.txt\nETF_XXXXX_XXXXX_YYYYMMDD_XXXXX.txt", "Vendor_Feed_SLA_EST": "02:00:00", "File_Count": "Approx 7500 to 8000 files", "Vendor_Feed_Extraction_Source": "FTP", "Vendor_Feed_Extraction_Source_URL": "ffd.ebs.ihsmarkit.com ", "Credentials": "Username: Jainglobal\nPassword: Available in 1Password", "Grafana_Alerts": "To Be <PERSON>up", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "Summary\nHeader\nBasket\nConstituent\nFXRate", "Airflow_Dag_Names": "jg-etl-SnP-ETF-Holdings", "Slack_Channel_Name": "snp-data-feeds", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "snpEncylopedia": {"Dataset_Id": "snpEncylopedia", "Vendor": "SnP", "Dataset_Description": "Encyclopedia", "Dataset_Details": "This dataset contains the reference data to be used in conjunction with the ETF Holdings", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM_Lead_Mobile": "+****************", "Vendor_Contact_Other": "<PERSON><PERSON>", "Vendor_Contact_Title": "\nClient Services Analyst\nS&P Global Market Intelligence", "Vendor_Contact_Work_Phone": "+************ \n+1212-849-3733", "Vendor_Contact_Mobile": "******-752-3269", "Vendor_Contact_Email": "<EMAIL>", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "/jfs/datalake/data/prod/1.0/cache/snp/refdata", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "Historical Files are available since 2021. Each file contians just one table to be loaded with has 183 columms\n\nDaily files are not zipped.", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Document_Link": "SNP - Overview (azure.com)", "File_names": "Historical\nEtp Encyclopedia Primary Listing_YYYYMMDD_2100.zip\nDaily:\nEtp_Encyclopedia_Primary_Listing_YYYYMMDD_1428.txt", "Vendor_Feed_SLA_EST": "02:00:00", "File_Count": "1 file per day", "Vendor_Feed_Extraction_Source": "FTP", "Vendor_Feed_Extraction_Source_URL": "ffd.ebs.ihsmarkit.com ", "Credentials": "Username: Jainglobal\nPassword: Available in 1Password", "Grafana_Alerts": "To Be <PERSON>up", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "Encyclopedia", "Airflow_Dag_Names": " jg-etl-SnP-Refdata ", "Slack_Channel_Name": "snp-data-feeds", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "bloombergHoldings": {"Dataset_Id": "bloombergHoldings", "Vendor": "Bloomberg", "Dataset_Description": "Bloomberg Holdings", "Dataset_Details": "This dataset contains the ETF holidings data", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM_Lead_Mobile": "+****************", "Vendor_Contact_Other": "\n<PERSON><PERSON>", "Vendor_Contact_Title": "Bloomberg Enterprise Data\nSolutions Engineers", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "\n<EMAIL>\n\n<EMAIL>\n\n<EMAIL>\n\n<EMAIL>", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "https://us-east-2.console.aws.amazon.com/s3/buckets/jg-s3-etl-<env>?region=us-east-2&bucketType=general&prefix=cache&showversions=false\n\nHere: env points to uat/tech1dev/tech1prod", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "All the data is in an S3 bucket that has to be requested via an API/or aws cli.\n\nThe Bloomberg Handbook can be used to check the commands.\nThe files are Avro files that can be loaded into a pandas dataframe", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Document_Link": "Bloomberg - Overview (azure.com)", "File_names": "History:\nequityEtf<region>HistorySnapshot.avro\n\nThe historical files are in an S3 bucket with the Location having the date\n\nDaily:\nequityEtf<region><time>.avro\n\nTime is either 0030,  0830 or 1630\n\nWe had an option of using S3 or FTP and have gone ahead with S3", "Vendor_Feed_SLA_EST": "02:00:00", "File_Count": "3 Daily files", "Vendor_Feed_Extraction_Source": "S3", "Vendor_Feed_Extraction_Source_URL": "aws s3api list-objects-v2 --bucket arn:aws:s3:us-east-1:6950********:accesspoint/dl-c0a4e143-ae06-4a08-b0ed-5483f78a53ec --prefix \"GuXDxca3/catalogs/bbg/datasets/<datasetPrefix>\" --request-payer requester", "Credentials": "We need to provide the role that can be used to download the data from their S3 bucker", "Grafana_Alerts": "To Be <PERSON>up", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "equity_etf_bbGlobal_xref\nequity_etf_positions\nequity_etf", "Airflow_Dag_Names": "jg-etl-Bloomberg-Refdata", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "\n<PERSON><PERSON>", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "Bloomberg Enterprise Data\nSolutions Engineers\n\n<EMAIL>\n\n<EMAIL>\n\n<EMAIL>\n\n<EMAIL>", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "bloombergReference": {"Dataset_Id": "bloombergReference", "Vendor": "Bloomberg", "Dataset_Description": "Bloomberg Ref Data", "Dataset_Details": "This contains the reference tables that are to be used in conjunction with the holdings", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM_Lead_Mobile": "+****************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "<PERSON>\n(BLOOMBERG/ PRINCETON)", "Vendor_Contact_Work_Phone": "", "Vendor_Contact_Mobile": "", "Vendor_Contact_Email": "<EMAIL>\n<EMAIL>\ncko<PERSON>@bloomberg.net\n<EMAIL>\n<EMAIL>", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "https://us-east-2.console.aws.amazon.com/s3/buckets/jg-s3-etl-<env>?region=us-east-2&bucketType=general&prefix=cache&showversions=false\n\nHere: env points to uat/tech1dev/tech1prod", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "Historical files contain multiple zip files - one for each type of record. There is one file per type of data, per month. The number of files are a lot to list here.\nEach file contains a format wherein the file is the implied table name, the file has START-OF-FIELDS which has the column names, followed by the data.\nThere could be variations of this.\nOur parsing logic will use this information to create the csv per type of filethat has to be loaded in iceberg.\n\nThis feed contains all the reference data that bloomberg provides us.\nThis reference data will be used along with our ETPs and ETFs\nThe Expectation is to load all files that starts with \"etp\" or \"fund\", excluding \"dif\", \"Alloc\", \"cins\", and \"Bulk\" files.", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Document_Link": "Bloomberg - Overview (azure.com)", "File_names": "etp<region>.out\netp<region>.px\netp<region>.rpx\n\nfund<region>.out\nfund<region>.px\nfund<region>.rpx\n\nRegions are Asia1, Asia2, <PERSON><PERSON>, <PERSON>r, Name", "Vendor_Feed_SLA_EST": "02:00:00", "File_Count": 27, "Vendor_Feed_Extraction_Source": "S3", "Vendor_Feed_Extraction_Source_URL": "aws s3api list-objects-v2 --bucket arn:aws:s3:us-east-1:6950********:accesspoint/dl-c0a4e143-ae06-4a08-b0ed-5483f78a53ec --prefix \"GuXDxca3/catalogs/bbg/datasets/<datasetPrefix>\" --request-payer requester", "Credentials": "We need to provide the role that can be used to download the data from their S3 bucker", "Grafana_Alerts": "To Be <PERSON>up", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "etp_out\netp_px\netp_rpx\nfund_px\nfund_rpx\nfund_out", "Airflow_Dag_Names": "jg-etl-Bloomberg-Refdata", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "msci": {"Dataset_Id": "msci", "Vendor": "msci", "Dataset_Description": "CRSP", "Dataset_Details": "This contains Index Feeds from MSCI which will be used as benchmarks for our comparisons and strategies.", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM_Lead_Mobile": "+****************", "Vendor_Contact_Other": "Nick Story\n\nPEPE GARZA", "Vendor_Contact_Title": "\nVice President | Index Client Coverage | \n\n\nIndex Client Service Specialist ", "Vendor_Contact_Work_Phone": "Pepe: ****** 804 1536\n\nCS Helpline: ****** 588 4567 ", "Vendor_Contact_Mobile": "Nick: ****** 361 3723", "Vendor_Contact_Email": "<EMAIL>\n\n<EMAIL>\n\n<EMAIL>", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "https://us-east-2.console.aws.amazon.com/s3/buckets/jg-s3-etl-<env>?region=us-east-2&bucketType=general&prefix=cache&showversions=false\n\nHere: env points to uat/tech1dev/tech1prod", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "Historical files contain multiple zip files - one for each type of record. There is one file per type of month per type. \nThe file contains a format where they have the table name followed by the columns, followed by the data. \nEach zip file will be processed by looking at the information and loading the data into the appropriate table.\nWe would be parsing the data for 5 years and loading the multiple tables\n\nFor Daily feeds we will get multiple zip files per day. Again, every file in the zip file will be processed and loaded to the table mentioned in the file", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Document_Link": "MSCI - Overview (azure.com)", "File_names": "Historical: \nYYYYMMDD_YYYYMMDD_<type>.zip\nThese are the different type we have for historical files.\nd15d.extension, d15d_rif, d15e.extension, d15e_rif, d16d_rif, d16e_rif, d17d.extension, d17d_rif, d17e.extension, d17e_rif, d18d_rif, d18e_rif, d51d, d51e, d52d, d52e, d95d.vg.extension, d95d.vg_rif, d95e.vg.extension, d95e.vg_rif, d96d, d96e, d98d_rif, d98e_rif, d99d_rif, d99e_rif, d_5h, d_icf_core_idx_oc, d_icf_core_idxsec_oc_d_rif, d_icf_sc_idxsec_oc_d_rif, m15d.extension, m15d_rif, m15e.extension, m15e_rif, m17d.extension, m17d_rif, m17e.extension, m17e_rif, m51d, m51e, m52d, m52e, m95d.vg.extension, m95d.vg_rif, m95e.vg.extension, m95e.vg_rif, m96d, m96e, m98d_rif, m98e_rif, m99d_rif, m99e_rif, m_5h, m_bd, m_icf_core_idx_oc, m_icf_core_idxsec_oc_d_rif, m_icf_sc_idxsec_oc_d_rif\n\nDaily\nFormat1: <MMDD><Type>.zip\nThese are the different types for Format1: d_tarif, d_tdrif, d_terif, d_tfrif, dstarif, dstdrif, dsterif, dstfrif, china_bin_securities\n\nFormat2: <YYYYMMDD><Type>.zip\nThese are the different types for Format2:\n_index_description_dom_int, daily_icfcoroc_d_rif, daily_icfcorts_amer_d_rif, daily_icfcorts_dm_d_rif, daily_icfcorts_eafe_d_rif, daily_icfcorts_em_d_rif, daily_icfscoc_d_rif, dm_ace_rif, em_ace_rif, sc_ace_rif, scem_ace_rif,", "Vendor_Feed_SLA_EST": "18:10:00", "File_Count": "9 files in Format1\n\n11 Files in Format2", "Vendor_Feed_Extraction_Source": "FTP", "Vendor_Feed_Extraction_Source_URL": "sftp://sftp.msci.com:22", "Credentials": "Username: gwgikulw\nPassword: Available in 1Password", "Grafana_Alerts": "To Be <PERSON>up", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "DAILY_TRACKER_XRATES\nINDEX_REPORT\nINDEX_SAME_DAY\nSECURITY_REPORT\nOFFICIAL_DTR_SECURITY_FILE\nADVANCED_DIVIDENDS_FILE\nSECURITY_CODE_MAP\nCOUNTRY_<PERSON>ATA\nINDEX_DESCRIPTION\nCOUNTRIES_WEIGHT\nSECURITY_SMALL_CAP_REPORT\nSECURITY_VG_REPORT\nVENDOR_BOXES_REPORT", "Airflow_Dag_Names": "jg-etl-MSCI-Index", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "CRSP": {"Dataset_Id": "CRSP", "Vendor": "CRSP", "Dataset_Description": "Index Prices and Returns", "Dataset_Details": "This dataset contains pricing and returns for multipe listed indices.", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+****************", "DM_Lead_Mobile": "+****************", "Vendor_Contact_Other": "<PERSON>", "Vendor_Contact_Title": "\nSr. Support and Relationship Specialist", "Vendor_Contact_Work_Phone": "******.263.6400", "Vendor_Contact_Mobile": "******.263.6332", "Vendor_Contact_Email": "", "Raw_Data_Location": "https://us-east-1.console.aws.amazon.com/s3/buckets/jg-data-batch-vendor-data?region=us-east-1", "Process_Data_Location": "/jfs/datalake/data/prod/1.0/cache/crsp/feeds", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "This contains the ETF holdings data. The FTP server contains 5 variations of files. Each day has around 10-12K files, that need to get processed._x000D_\n_x000D_\nHistorical files contain one zip file per month and one additional file per quarter._x000D_\nEach zip file contains a folder per month/quarter_x000D_\nEach day folder contains multiple files per day._x000D_\nIn FTP for the current dates, we have a day folder contains multiple files per day. Structure is similar to the historical folder._x000D_\nEach file contains 5 datasets, similar to he ones in the Historical Folders.", "GitHub_Repository": "https://dev.azure.com/JainGlobal/_git/JG-DATA-PLATFORM?path=/source&version=GBdevelopment", "Support_Document_Link": "CRSP - Overview (azure.com)", "File_names": "Historical\nhistory_icl/YYYY/YYYY_crsp_index_constituents_open_PF_V2.zip\nhistory_icl/YYYY/YYYY_<Quarter>_crsp_index_constituents_open_V2.zip\nhistory_icl/YYYY/YYYY_<Quarter>_crsp_index_constituents_open_Proj_V2.zip\n\nhistory_icl/YYYY/YYYY_crsp_index_constituents_close_PF_V2.zip\nhistory_icl/YYYY/YYYY_<Quarter>_crsp_index_constituents_close_V2.zip\n\n/history_index_levels/YYYY/YYYY_<Quarter>_crsp_index_levels.zip\n\n/history_indexchanges/crsp_indexchanges_YYYY.zip\n/history_indexchanges/crsp_indexchanges_YYYY_<Quarter>.zip\n\nDaily \n/icl_close/crsp_index_constituents_close_V2_2024MMDD.txt\n/icl_open/crsp_index_constituents_open_V2_2024MMDD.txt\n/index_levels/crsp_index_levels_2024MMDDD.txt\n/indexchanges/crsp_indexchanges_2024MMDD_HHMM.txt [9 files per date]\n/indexchanges5/crsp_indexchanges5_2024MMDD_HHMM.txt", "Vendor_Feed_SLA_EST": "19:10:00", "File_Count": "10-12 files Approx", "Vendor_Feed_Extraction_Source": "FTP", "Vendor_Feed_Extraction_Source_URL": "ftp.crspmi.org:22", "Credentials": "Username: jainglobal\nPassword: Available in 1Password", "Grafana_Alerts": "To Be <PERSON>up", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "icl_close\nicl_open\nindex_changes\nindex_changes5\nindex_level", "Airflow_Dag_Names": "jg-etl-CRSP-index", "Slack_Channel_Name": "crsp-data-feeds", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "<EMAIL>", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "ICE Security Master": {"Dataset_Id": "ICE Security Master", "Vendor": "ICE Security Master", "Dataset_Description": "TBD", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON><PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "(*************", "DM_Lead_Mobile": "(*************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "TBD", "Process_Data_Location": "TBD", "File_Type": "TBD", "Update_Frequency": "TBD", "Technical_Notes": "TBD", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "TBD", "Vendor_Feed_SLA_EST": "TBD", "File_Count": "TBD", "Vendor_Feed_Extraction_Source": "TBD", "Vendor_Feed_Extraction_Source_URL": "TBD", "Credentials": "TBD", "Grafana_Alerts": "TBD", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "TBD", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "CME Pricing Data": {"Dataset_Id": "CME Pricing Data", "Vendor": "CME Pricing Data", "Dataset_Description": "TBD", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON><PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "(*************", "DM_Lead_Mobile": "(*************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "TBD", "Process_Data_Location": "TBD", "File_Type": "TBD", "Update_Frequency": "TBD", "Technical_Notes": "TBD", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "TBD", "Vendor_Feed_SLA_EST": "TBD", "File_Count": "TBD", "Vendor_Feed_Extraction_Source": "TBD", "Vendor_Feed_Extraction_Source_URL": "TBD", "Credentials": "TBD", "Grafana_Alerts": "TBD", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "TBD", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "ICE Pricing Data": {"Dataset_Id": "ICE Pricing Data", "Vendor": "ICE Pricing Data", "Dataset_Description": "TBD", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON><PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "(*************", "DM_Lead_Mobile": "(*************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "TBD", "Process_Data_Location": "TBD", "File_Type": "TBD", "Update_Frequency": "TBD", "Technical_Notes": "TBD", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "TBD", "Vendor_Feed_SLA_EST": "TBD", "File_Count": "TBD", "Vendor_Feed_Extraction_Source": "TBD", "Vendor_Feed_Extraction_Source_URL": "TBD", "Credentials": "TBD", "Grafana_Alerts": "TBD", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "TBD", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "GS Marque (SS VOL)": {"Dataset_Id": "GS Marque (SS VOL)", "Vendor": "GS Marque (SS VOL)", "Dataset_Description": "TBD", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON><PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "(*************", "DM_Lead_Mobile": "(*************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "TBD", "Process_Data_Location": "TBD", "File_Type": "TBD", "Update_Frequency": "TBD", "Technical_Notes": "TBD", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "TBD", "Vendor_Feed_SLA_EST": "TBD", "File_Count": "TBD", "Vendor_Feed_Extraction_Source": "TBD", "Vendor_Feed_Extraction_Source_URL": "TBD", "Credentials": "TBD", "Grafana_Alerts": "TBD", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "TBD", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "MSCI Barra Global": {"Dataset_Id": "MSCI Barra Global", "Vendor": "MSCI Barra Global Model", "Dataset_Description": "MSCI Barra Global Model", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "(*************", "DM_Lead_Mobile": "(*************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "/jfs/tech1/apps/rawdata/MSCI/barra/1.0/gemcs1tr", "Process_Data_Location": "/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "At first airflow DAG's from data account gets the data from the broker and dumps it in the jfs as raw data and verify whether we receive 14 files from the broker. After the verification, production airlfow DAG's does ETL on the data present in the JFS and put it in a s3 bucket as iceberg tables and verify that all the files in JFS is also present in the iceberg.", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "GMD_GEMCS1TRD_100_ETF_YYMMDD.zip\nGMD_GEMCS1TRD_100_UnadjCov_YYMMDD.zip\nGMD_GEMCS1TR_SPAC_Market_Data_YYMMDD.zip\nGMD_GEMCS1TR_SPAC_ID_YYMMDD.zip\nGMD_GEMCS1TR_Market_Data_YYMMDD.zip\nGMD_GEMCS1TRD_100_SPAC_YYMMDD.zip\nGMD_GEMCS1TR_Daily_Precision_Asset_Price_YYMMDD.zip\nGEMCS1TR_Update_Release.20YYMMDD\nFPD_GEMCS1TRD_YYMMDD.zip\nGMD_GEMCS1TR_100_PeerGroupData_YYMMDD.zip\nGMD_GEMCS1TR_ID_YYMMDD.zip\nGMD_GEMCS1TR_100_Std_Descriptor_YYMMDD.zip\nGMD_GEMCS1TRD_100_PrecisionExp_YYMMDD.zip\nGMD_GEMCS1TRD_100_YYMMDD.zip", "Vendor_Feed_SLA_EST": "GEMCS1TR: 09:00 UTC during Standard Time /  08:00 UTC during Daylight Saving Time", "File_Count": "Daily-14, monthly-2", "Vendor_Feed_Extraction_Source": "SFTP", "Vendor_Feed_Extraction_Source_URL": "sftp.barra.com:22/gemcs1tr", "Credentials": "username: pwdmqvmz\nPassword: Available in 1Password", "Grafana_Alerts": "Setup Completed", "DataSet_Billing_Name": "", "Tables_Loaded": "assetdata, assetfacts, assetfactsmonthly, assetidentity, compassetdata, compexposure, covariance, descriptor, estuniv, exposure, factorportfolio, factorreturn, factorreturnmonthly, factors, peerspecrisk, rates", "Airflow_Dag_Names": "Airflow Data Account : jg-raw-MSCI-Barra-gem / Airflow PROD : jg-etl-MSCI-Barra-gem", "Slack_Channel_Name": "msci-data-feeds", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "MSCI Barra US": {"Dataset_Id": "MSCI Barra US", "Vendor": "MSCI Barra US Model", "Dataset_Description": "MSCI Barra US Model", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "(*************", "DM_Lead_Mobile": "(*************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "/jfs/tech1/apps/rawdata/MSCI/barra/1.0/usacs1tr", "Process_Data_Location": "/jfs/datalake/data/prod/1.0/lake/na/msci/barra/usacs1trd/mk1", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "At first airflow DAG's from data account gets the data from the broker and dumps it in the jfs as raw data and verify whether we receive 14 files from the broker. After the verification, production airlfow DAG's does ETL on the data present in the JFS and put it in a s3 bucket as iceberg tables and verify that all the files in JFS is also present in the iceberg.", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "USACS1TR_Update_Release.YYYYMMDD\nFPD_USACS1TRD_YYMMDD.zip\nSMD_USACS1TRD_100_YYMMDD.zip\nSMD_USACS1TRD_100_ETF_YYMMDD.zip\nSMD_USACS1TRD_100_PrecisionExp_YYMMDD.zip\nSMD_USACS1TRD_100_SPAC_YYMMDD.zip\nSMD_USACS1TRD_100_UnadjCov_YYMMDD.zip\nSMD_USACS1TR_100_PeerGroupData_YYMMDD.zip\nSMD_USACS1TR_100_Std_Descriptor_YYMMDD.zip\nSMD_USACS1TR_Daily_Precision_Asset_Price_YYMMDD.zip\nSMD_USACS1TR_ID_YYMMDD.zip\nSMD_USACS1TR_Market_Data_YYMMDD.zip\nSMD_USACS1TR_SPAC_ID_YYMMDD.zip\nSMD_USACS1TR_SPAC_Market_Data_YYMMDD.zip", "Vendor_Feed_SLA_EST": "USACS1TR: 09:30 UTC during Standard Time / 08:30 UTC during Daylight Saving Time", "File_Count": "Daily-14, monthly-2", "Vendor_Feed_Extraction_Source": "SFTP", "Vendor_Feed_Extraction_Source_URL": "sftp.barra.com:22/usacs1tr", "Credentials": "username: pwdmqvmz\nPassword: Available in 1Password", "Grafana_Alerts": "Setup Completed", "DataSet_Billing_Name": "", "Tables_Loaded": "assetdata, assetfacts, assetfactsmonthly, assetidentity, compassetdata, compexposure, covariance, descriptor, estuniv, exposure, factorportfolio, factorreturn, factorreturnmonthly, factors, peerspecrisk, rates", "Airflow_Dag_Names": "Airflow Data Account : jg-raw-MSCI-Barra-us / Airflow PROD : jg-etl-MSCI-Barra-us", "Slack_Channel_Name": "msci-data-feeds", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "MSCI Barra Japan": {"Dataset_Id": "MSCI Barra Japan", "Vendor": "MSCI Barra Japan Model", "Dataset_Description": "MSCI Barra Japan Model", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "(*************", "DM_Lead_Mobile": "(*************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "/jfs/tech1/apps/rawdata/MSCI/barra/1.0/jpnefmtrd", "Process_Data_Location": "/jfs/datalake/data/prod/1.0/lake/as/msci/barra/jpnefmtrd/mk1", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "At first airflow DAG's from data account gets the data from the broker and dumps it in the jfs as raw data and verify whether we receive 14 files from the broker. After the verification, production airlfow DAG's does ETL on the data present in the JFS and put it in a s3 bucket as iceberg tables and verify that all the files in JFS is also present in the iceberg.", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "JPNEFMTR_Update_Release.YYYYMMDD\nSMD_JPNEFMTR_Daily_Precision_Asset_Price_YYMMDD.zip\nSMD_JPNEFMTR_100_PeerGroupData_YYMMDD.zip\nSMD_JPNEFMTR_Market_Data_YYMMDD.zip\nSMD_JPNEFMTRD_100_ETF_YYMMDD.zip\nSMD_JPNEFMTR_ID_YYMMDD.zip\nSMD_JPNEFMTR_100_Std_Descriptor_YYMMDD.zip\nSMD_JPNEFMTRD_100_PrecisionExp_YYMMDD.zip\nSMD_JPNEFMTRD_100_YYMMDD.zip\nSMD_JPNEFMTRD_100_UnadjCov_YYMMDD.zip\nFPD_JPNEFMTRD_YYMMDD.zip", "Vendor_Feed_SLA_EST": "JPNEFMTR: 18:00 UTC during Standard Time / 17:00 UTC during Daylight Saving Time", "File_Count": "Daily-14, monthly-2", "Vendor_Feed_Extraction_Source": "SFTP", "Vendor_Feed_Extraction_Source_URL": "sftp.barra.com:22/jpnefmtr", "Credentials": "username: pwdmqvmz\nPassword: Available in 1Password", "Grafana_Alerts": "To be setup", "DataSet_Billing_Name": "", "Tables_Loaded": "assetdata, assetfacts, assetfactsmonthly, assetidentity, compassetdata, compexposure, covariance, descriptor, estuniv, exposure, factorportfolio, factorreturn, factorreturnmonthly, factors, peerspecrisk, rates", "Airflow_Dag_Names": "Airflow Data Account : jg-raw-MSCI-Barra-jp / Airflow PROD : jg-etl-MSCI-Barra-jp", "Slack_Channel_Name": "msci-data-feeds", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "MSCI Barra EU": {"Dataset_Id": "MSCI Barra EU", "Vendor": "MSCI Barra EU Model", "Dataset_Description": "MSCI Barra EU Model", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "(*************", "DM_Lead_Mobile": "(*************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "/jfs/tech1/apps/rawdata/MSCI/barra/1.0/eurefmtrd", "Process_Data_Location": "/jfs/datalake/data/prod/1.0/lake/eu/msci/barra/eurefmtrd/mk1", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "At first airflow DAG's from data account gets the data from the broker and dumps it in the jfs as raw data and verify whether we receive 14 files from the broker. After the verification, production airlfow DAG's does ETL on the data present in the JFS and put it in a s3 bucket as iceberg tables and verify that all the files in JFS is also present in the iceberg.", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "FPD_EUREFMTRD_YYMMDD.zip\nGMD_EUREFMTRD_100_YYMMDD.zip\nGMD_EUREFMTRD_100_UnadjCov_YYMMDD.zip\nGMD_EUREFMTR_100_Std_Descriptor_YYMMDD.zip\nGMD_EUREFMTRD_100_PrecisionExp_YYMMDD.zip\nEUREFMTR_Update_Release.YYYYMMDD\nGMD_EUREFMTR_SPAC_ID_YYMMDD.zip\nGMD_EUREFMTRD_100_ETF_YYMMDD.zip\nGMD_EUREFMTR_SPAC_Market_Data_YYMMDD.zip\nGMD_EUREFMTR_Market_Data_YYMMDD.zip\nGMD_EUREFMTRD_100_SPAC_YYMMDD.zip\nGMD_EUREFMTR_100_PeerGroupData_YYMMDD.zip\nGMD_EUREFMTR_Daily_Precision_Asset_Price_YYMMDD.zip\nGMD_EUREFMTR_ID_YYMMDD.zip", "Vendor_Feed_SLA_EST": "EUREFMTR: 05:30 UTC during Standard Time /  04:30 UTC during Daylight Saving Time", "File_Count": "Daily-14, monthly-2", "Vendor_Feed_Extraction_Source": "SFTP", "Vendor_Feed_Extraction_Source_URL": "sftp.barra.com:22/eurefmtr", "Credentials": "username: pwdmqvmz\nPassword: Available in 1Password", "Grafana_Alerts": "To be setup", "DataSet_Billing_Name": "", "Tables_Loaded": "assetdata, assetfacts, assetfactsmonthly, assetidentity, compassetdata, compexposure, covariance, descriptor, estuniv, exposure, factorportfolio, factorreturn, factorreturnmonthly, factors, peerspecrisk, rates", "Airflow_Dag_Names": "Airflow Data Account : jg-raw-MSCI-Barra-eu / Airflow PROD : jg-etl-MSCI-Barra-eu", "Slack_Channel_Name": "msci-data-feeds", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "MSCI Barra APAC": {"Dataset_Id": "MSCI Barra APAC", "Vendor": "MSCI Barra APAC Model", "Dataset_Description": "MSCI Barra APAC Model", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "(*************", "DM_Lead_Mobile": "(*************", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "/jfs/tech1/apps/rawdata/MSCI/barra/1.0/apacefmtrd", "Process_Data_Location": "/jfs/datalake/data/prod/1.0/lake/as/msci/barra/apacefmtrd/mk1", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "At first airflow DAG's from data account gets the data from the broker and dumps it in the jfs as raw data and verify whether we receive 14 files from the broker. After the verification, production airlfow DAG's does ETL on the data present in the JFS and put it in a s3 bucket as iceberg tables and verify that all the files in JFS is also present in the iceberg.", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "GMD_APACEFMTRD_100_SPAC_YYMMDD.zip\nGMD_APACEFMTRD_100_ETF_YYMMDD.zip\nGMD_APACEFMTR_100_Std_Descriptor_YYMMDD.zip\nGMD_APACEFMTR_SPAC_ID_YYMMDD.zip\nGMD_APACEFMTR_ID_YYMMDD.zip\nGMD_APACEFMTR_Market_Data_YYMMDD.zip\nGMD_APACEFMTR_Daily_Precision_Asset_Price_YYMMDD.zip\nGMD_APACEFMTR_100_PeerGroupData_YYMMDD.zip\nGMD_APACEFMTRD_100_YYMMDD.zip\nGMD_APACEFMTR_SPAC_Market_Data_YYMMDD.zip\nGMD_APACEFMTRD_100_PrecisionExp_YYMMDD.zip\nGMD_APACEFMTRD_100_UnadjCov_YYMMDD.zip\nAPACEFMTR_Update_Release.YYYYMMDD\nFPD_APACEFMTRD_YYMMDD.zip", "Vendor_Feed_SLA_EST": "APACEFMTR: 00:30 UTC during Standard Time / 23:30 UTC during Daylight Saving Time", "File_Count": "Daily-14, monthly-2", "Vendor_Feed_Extraction_Source": "SFTP", "Vendor_Feed_Extraction_Source_URL": "sftp.barra.com:22/apacefmtr", "Credentials": "username: pwdmqvmz\nPassword: Available in 1Password", "Grafana_Alerts": "To be setup", "DataSet_Billing_Name": "", "Tables_Loaded": "assetdata, assetfacts, assetfactsmonthly, assetidentity, compassetdata, compexposure, covariance, descriptor, estuniv, exposure, factorportfolio, factorreturn, factorreturnmonthly, factors, peerspecrisk, rates", "Airflow_Dag_Names": "Airflow Data Account : jg-raw-MSCI-Barra-apac / Airflow PROD : jg-etl-MSCI-Barra-apa", "Slack_Channel_Name": "msci-data-feeds", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "Axe & Avail": {"Dataset_Id": "Axe & Avail", "Vendor": "SOCGEN", "Dataset_Description": "Axe & Avail", "Dataset_Details": "Axe & Availability Files of PB", "Data_Management_Lead": "<PERSON><PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+44 ************", "DM_Lead_Mobile": "+44 7541 894 950", "Vendor_Contact_Other": "<PERSON>, <PERSON>", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "DON<PERSON><PERSON><PERSON> <PERSON> <ryan.don<PERSON><PERSON>@sgcib.com>;  MAXWELL Dawn <<EMAIL>>; OLIVE Frederic <<EMAIL>>", "Raw_Data_Location": "TBD", "Process_Data_Location": "TBD", "File_Type": "File Transfer", "Update_Frequency": "Daily", "Technical_Notes": "Axe & Global Availability files for the PB are placed in PB SFTP and the files are copied to Arcesium SFTP for processing.", "GitHub_Repository": "https://dev.azure.com/JainGlobal/JG-LOCATE-HUB/_git/JG-Locate-AxeAvail", "Support_Document_Link": "TBD", "File_names": "SG_Axe_List_US_YYYYMMDD.csv\nAxes_CN_YYYYMMDD.csv\nSocGen_availability_US_YYYYMMDD.csv\nSocGen_availability_APAC_YYYYMMDD.csv\nSocGen_availability_EMEA_YYYYMMDD.csv", "Vendor_Feed_SLA_EST": "6 to 6:30 AM\n9:30 PM to 1:30 AM(T)\n7:30 AM to 8 :30 AM\n????\n3:30 AM to 4:30 AM", "File_Count": 5, "Vendor_Feed_Extraction_Source": "SFTP", "Vendor_Feed_Extraction_Source_URL": "prmssp.amer.sgcib.com:22", "Credentials": "User & password in 1 Password", "Grafana_Alerts": "TBD", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "TBD", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "CMG": {"Dataset_Id": "CMG", "Vendor": "CMG", "Dataset_Description": "Capital Markets Gate way", "Dataset_Details": "List of deals and their details for ECM Module", "Data_Management_Lead": "<PERSON><PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+44 ************", "DM_Lead_Mobile": "+44 7541 894 950", "Vendor_Contact_Other": "<PERSON>", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "<EMAIL>\n<EMAIL>", "Raw_Data_Location": "TBD", "Process_Data_Location": "TBD", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "Real time data pull from the data source and will be consumed to trino tables", "GitHub_Repository": "", "Support_Document_Link": "TBD", "File_names": "TBD", "Vendor_Feed_SLA_EST": "real time", "File_Count": "TBD", "Vendor_Feed_Extraction_Source": "API (HTTPS)", "Vendor_Feed_Extraction_Source_URL": "https://jainglobal.api.cmgecm.com/dlgw/graphql/", "Credentials": "User & password in 1 Password", "Grafana_Alerts": "TBD", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "TBD", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "Capital data": {"Dataset_Id": "Capital data", "Vendor": "Arceisum", "Dataset_Description": "Excess Deficit API, extended positions, \naccounting balances", "Dataset_Details": "Capital Position for the date from Arceisum", "Data_Management_Lead": "<PERSON><PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+44 ************", "DM_Lead_Mobile": "+44 7541 894 950", "Vendor_Contact_Other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "Abhishek3.<PERSON>@arcesium.com", "Raw_Data_Location": "TBD", "Process_Data_Location": "TBD", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "API to pull data from Arcesium ", "GitHub_Repository": "", "Support_Document_Link": "TBD", "File_names": "TBD", "Vendor_Feed_SLA_EST": "8:05 PM EST", "File_Count": "TBD", "Vendor_Feed_Extraction_Source": "API (HTTPS)", "Vendor_Feed_Extraction_Source_URL": "https://api-jainglobal.arcesium.com", "Credentials": "User & password in 1 Password", "Grafana_Alerts": "TBD", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "TBD", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "Pnl": {"Dataset_Id": "Pnl", "Vendor": "Arceisum", "Dataset_Description": "\nPnlService , BundleService, AccountingPnlSearchRequest", "Dataset_Details": "TBD", "Data_Management_Lead": "<PERSON><PERSON>", "DM_Lead_Email": "<EMAIL>", "DM_Lead_Phone": "+44 ************", "DM_Lead_Mobile": "+44 7541 894 950", "Vendor_Contact_Other": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "Abhishek3.<PERSON>@arcesium.com", "Raw_Data_Location": "TBD", "Process_Data_Location": "TBD", "File_Type": "Iceberg Table", "Update_Frequency": "Daily", "Technical_Notes": "API to pull data from Arcesium ", "GitHub_Repository": "https://dev.azure.com/JainGlobal/JG-DATA-PLATFORM/_git/JG-DATA-PLATFORM?path=/source/dags/etl&version=GBdevelopment&_a=contents", "Support_Document_Link": "TBD", "File_names": "TBD", "Vendor_Feed_SLA_EST": "8:05 PM EST", "File_Count": "TBD", "Vendor_Feed_Extraction_Source": "API (HTTPS)", "Vendor_Feed_Extraction_Source_URL": "https://api-jainglobal.arcesium.com", "Credentials": "User & password in 1 Password", "Grafana_Alerts": "TBD", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "TBD", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}, "SnP Holdings": {"Dataset_Id": "SnP Holdings", "Vendor": "SnP Holdings", "Dataset_Description": "TBD", "Dataset_Details": "TBD", "Data_Management_Lead": "TBD", "DM_Lead_Email": "TBD", "DM_Lead_Phone": "TBD", "DM_Lead_Mobile": "TBD", "Vendor_Contact_Other": "TBD", "Vendor_Contact_Title": "TBD", "Vendor_Contact_Work_Phone": "TBD", "Vendor_Contact_Mobile": "TBD", "Vendor_Contact_Email": "TBD", "Raw_Data_Location": "TBD", "Process_Data_Location": "TBD", "File_Type": "TBD", "Update_Frequency": "TBD", "Technical_Notes": "TBD", "GitHub_Repository": "TBD", "Support_Document_Link": "TBD", "File_names": "TBD", "Vendor_Feed_SLA_EST": "TBD", "File_Count": "TBD", "Vendor_Feed_Extraction_Source": "TBD", "Vendor_Feed_Extraction_Source_URL": "TBD", "Credentials": "TBD", "Grafana_Alerts": "TBD", "DataSet_Billing_Name": "TBD", "Tables_Loaded": "TBD", "Airflow_Dag_Names": "TBD", "Slack_Channel_Name": "TBD", "Teams_Channel_Name": "TBD", "Vendor_Account_Manager": "TBD", "Vendor_Account_Manager_Work_Phone": "TBD", "Vendor_Account_Manager_Mobile": "TBD", "Vendor_Account_Manager_Email": "TBD", "Vendor_Sales_Specialist": "TBD", "Vendor_Sales_Specialist_Work_Phone": "TBD", "Vendor_Sales_Specialist_Mobile": "TBD", "Vendor_Sales_Specialist_Email": "TBD", "Vendor_Technical_Account_Manager": "TBD", "Vendor_Technical_Account_Manager_Work_Phone": "TBD", "Vendor_Technical_Account_Manager_Mobile": "TBD", "Vendor_Technical_Account_Manager_Email": "TBD", "Customer_Success_Manager_product": "TBD", "Customer_Success_Manager_product_Work_Phone": "TBD", "Customer_Success_Manager_product_Mobile": "TBD", "Customer_Success_Manager_product_Email": "TBD"}}}