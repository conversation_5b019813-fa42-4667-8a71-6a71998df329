import os
import gzip
import shutil
import tempfile
import pandas as pd

from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor
from utils.date_utils import get_n_bday_yyyymmdd, get_n_bday, get_now
from bloomberg.bpipe import <PERSON>pipeHandler

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
        database="JG_SNP", warehouse="TEST", role="FR_DATA_PLATFORM"
    )

    df_securities = sf_adaptor.read_data(
        "BBGH_ONDEMAND",
        """
            SELECT DISTINCT curr.CONSTITUENT_BLOOMBERG AS BBG_TICKER
            FROM JG_SNP.ETF.LATEST_CONSTITUENT_WITH_ADDNL_DETAILS_CASH_NOSAGIE_SANDBOX curr
            JOIN COPPCLARK.HDS.EXCHANGE_TRADING_DAYS etd
            ON curr.FILE_DATE = TO_NUMBER(TO_CHAR(etd.BUSINESS_DATE,'YYYYMMDD'))
            AND etd.ISOMIC_CODE = curr.MIC
            WHERE curr.BLOOMBERG = 'ARKK UP Equity' and 
            curr.constituent_bloomberg IS NOT NULL
            AND curr.IS_PRIMARY_LISTING;
        """,
    )
    
    securities = df_securities["BBG_TICKER"].tolist()
    print(securities)

    to_date = get_n_bday(0)
    from_date = get_n_bday(2900)
    

    bpipe_instance = ("Tech1ProdBPipe47537.jainglobal.net", 8194)
    bpipe_app = "JAIN:pmdashboard-bps"
    bpipe_handler = BpipeHandler(bpipe_instance, bpipe_app)

    df_risk_index_hist = bpipe_handler.fetch_hist(p_ticker=securities, 
    p_field=["OFFICIAL_OPEN_AUCTION_PRICE", "PX_LAST"], 
    p_start=from_date, p_end=to_date)

    print(df_risk_index_hist.head())
    print(df_risk_index_hist.shape)
    df_risk_index_hist.to_parquet("/jfs/tech1_share/pulkit.vora/nosagie/nosagie_arkk_20250306.parquet")
    

    # df_risk_index_hist.rename(columns= {"ticker": "BBG_TICKER", "field": "FIELD", "date": "DATE", "value": "VALUE"}, inplace=True)
    # df_risk_index_hist["DATE"] = pd.to_datetime(df_risk_index_hist["DATE"], format="%m/%d/%Y", errors="coerce")
    # df_risk_index_hist["VALUE"] = pd.to_numeric(df_risk_index_hist["VALUE"], errors="coerce")

    

    # print(df_risk_index_hist.head())

    