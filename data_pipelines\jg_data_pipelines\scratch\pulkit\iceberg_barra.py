from datetime import datetime
from pyiceberg.table import StaticTable

if __name__ == "__main__":
    # path="/jfs/datalake/data/prod/1.0/lake/na/coredata/equities/mk1/prices/"  #Equity Prices
    # path="/jfs/datalake/data/prod/1.0/lake/na/coredata/equities/mk1/products/" #Equity Products

    # path="/jfs/datalake/data/prod/1.0/lake/gl/wshorizon/events/mk1/earningsdate/" #Wallstreet Horizon Earnings data.
    
    # path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/assetdata/" #Barra Factors
    # path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/assetfacts/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/assetfactsmonthly/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/assetidentity/" # doesn't have date filter.
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/compassetdata/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/compexposure/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/descriptor/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/estuniv/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/exposure/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/factorportfolio/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/factorreturn/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/factorreturnmonthly/"
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/factors/" # doesn't have date filter.
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/peerspecrisk/" 
    path="/jfs/datalake/data/prod/1.0/lake/gl/msci/barra/gemcs1trd/mk1/rates/" 
    

    v_file = open(path+"metadata/version-hint.text", "r")
    version=v_file.readline()
    static_table = StaticTable.from_metadata(path+"metadata/v"+version+".metadata.json")
    
    last_data_date = datetime(2025,1,31).isoformat()
    filter=f"date = '{last_data_date}'"
    
    df = static_table.scan(row_filter=filter, limit=100).to_pandas()
    # df = static_table.scan(limit=100).to_pandas()
    # df = static_table.scan().to_pandas()
    print(df.head(100))