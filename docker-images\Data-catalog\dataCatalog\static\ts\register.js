"use strict";
class RegisterUser {
    constructor() {
        this.baseUrl = `${window.location.protocol}//${window.location.host}`;
        this.submitRegistration = async () => {
            const register_email_input = document.getElementById('register_email');
            const register_password_input = document.getElementById('register_password');
            const email = register_email_input.value;
            const password = register_password_input.value;
            const requestOptions = {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ email: email, hashed_password: password, isAdmin: false }),
            };
            const response = await fetch(`${this.baseUrl}/api/users`, requestOptions);
            const data = await response.json();
            if (!response.ok) {
                console.log(data.detail);
            }
            else {
                const data = await response.json();
                const baseUrl = `${window.location.protocol}//${window.location.host}`;
                console.log("localStorage.setItem(\"userToken\", data.access_token):", localStorage.setItem("userToken", data.access_token));
                localStorage.setItem("userToken", data.access_token);
                window.location.assign(baseUrl + "/static/index.html");
                const dataCatalogController = new DataCatalogController();
            }
        };
        this.handleRegisterSubmit = () => {
            const register_password_input = document.getElementById('register_password');
            const register_confirm_password_input = document.getElementById('register_confirm_password');
            const password = register_password_input === null || register_password_input === void 0 ? void 0 : register_password_input.value;
            const confirmationPassword = register_confirm_password_input === null || register_confirm_password_input === void 0 ? void 0 : register_confirm_password_input.value;
            if (password === confirmationPassword && password.length > 8) {
                this.submitRegistration();
            }
            else {
                alert("Ensure that the passwords match and greater than 8 characters");
            }
        };
        DataCatalogController.openTab(new Event('click'), 'MarketData');
        const register_form = document.getElementById('register_form');
        register_form === null || register_form === void 0 ? void 0 : register_form.addEventListener("submit", (event) => {
            event.preventDefault();
            this.handleRegisterSubmit();
        });
    }
}
