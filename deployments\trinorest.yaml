apiVersion: apps/v1
kind: Deployment
metadata:
  name: iceberg-rest
spec:
  replicas: 1
  selector:
    matchLabels:
      app: iceberg-rest
  template:
    metadata:
      labels:
        app: iceberg-rest
    spec:
      containers:
      - name: iceberg-rest
        image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-trinorest-${ENV}:latest
        ports:
        - containerPort: 8181
        env:
        - name: CATALOG_WAREHOUSE
          value: "/mnt/data/iceberg"
        - name: CATALOG_URI
          value: "****************************************************"
        - name: CATALOG_JDBC_USER
          value: "admin"
        - name: CATALOG_JDBC_PASSWORD
          value: "${TRINO_CATALOG_JDBC_PASSWORD}"
---
apiVersion: v1
kind: Service
metadata:
  name: iceberg-rest
spec:
  selector:
    app: iceberg-rest
  ports:
    - protocol: TCP
      port: 8181
      targetPort: 8181
  type: ClusterIP
