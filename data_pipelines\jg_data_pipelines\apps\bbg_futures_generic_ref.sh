echo "Refreshing Futures Generic Ref"

source $HOME/.bashrc

echo "Refreshing futures generic reference from Bbg DL"
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/bbg_dl_futures_generic_ref.py >> /home/<USER>/logs/bbg_dl_futures_generic_ref.log 2>&1

echo "Refreshing intraday subscriptions"
python /home/<USER>/code/data-platform/data_pipelines/jg_data_pipelines/apps/refresh_intraday_subscription_univ.py >> /home/<USER>/logs/refresh_intraday_subscription_univ.log 2>&1

echo "Completed Refreshing Futures Generic Ref"
