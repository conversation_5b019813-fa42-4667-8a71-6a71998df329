*/5 * * * * bash -i -c "/opt/jsvc-datait/airflow/airflow_health_check.sh"
#*/5 * * * * bash -i -c "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/start_demon_process.sh"
#*/5 * * * * bash -i -c "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/start_connectors.sh"

# PRODUCTION: The following jobs are written as candidates for Airflow DAGs (Ron Benchetrit)
48 16 * * 1-5 /opt/jsvc-datait/ron.benchetrit/JG-DATA-PLATFORM/source/dags/main_cme_settle_batch.sh --ftp >> /opt/jsvc-datait/ron.benchetrit/JG-DATA-PLATFORM/source/dags/logs/cronCME.log 2>&1
30 17 * * 1-5 /opt/jsvc-datait/ron.benchetrit/JG-DATA-PLATFORM/source/dags/main_cme_settle_batch.sh --ftp >> /opt/jsvc-datait/ron.benchetrit/JG-DATA-PLATFORM/source/dags/logs/cronCME.log 2>&1
00 18 * * 1-5 /opt/jsvc-datait/ron.benchetrit/JG-DATA-PLATFORM/source/dags/main_cme_settle_batch.sh --ftp >> /opt/jsvc-datait/ron.benchetrit/JG-DATA-PLATFORM/source/dags/logs/cronCME.log 2>&1
00 18 * * 1-5 /opt/jsvc-datait/ron.benchetrit/JG-DATA-PLATFORM/source/dags/main_cme_settle_prices.sh --sendemail >> /opt/jsvc-datait/ron.benchetrit/JG-DATA-PLATFORM/source/dags/logs/cronCMEprc.log 2>&1


# Corp Structure
10 1 * * * bash -i -c "/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/automation-scripts/run-corpstructure.sh"
