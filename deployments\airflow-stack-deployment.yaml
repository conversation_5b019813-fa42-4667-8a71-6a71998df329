apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: airflow-var-tmp-vol
spec:
  storageClassName: gp2-encrypted
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1000Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: airflow-var-tmp-vol-web
spec:
  storageClassName: gp2-encrypted
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 500Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: cwiq-log-vol
spec:
  storageClassName: gp2-encrypted
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: phdata-tmp
spec:
  storageClassName: gp2-encrypted
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 4Ti
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: initialize-airflow-db-config
data:
  AIRFLOW__CORE__SQL_ALCHEMY_CONN: postgresql+psycopg2://${AIRFLOW_USERNAME}:${AIRFLOW_PASSWORD}@service-postgres:5432/airflow
  AIRFLOW__CORE__EXECUTOR: LocalExecutor
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: airflow-configs
data:
  airflow.cfg: |
    [core]
    airflow_home = /home/<USER>
    dags_folder =  /jfs/tech1/apps/datait/jg-code/uat/JG-DATA-PLATFORM/source/dags/etl
    base_log_folder = /home/<USER>/logs
    remote_base_log_folder =
    remote_log_conn_id =
    encrypt_s3_logs = False
    executor = LocalExecutor
    sql_alchemy_conn = postgresql+psycopg2://#AIRFLOW_USERNAME#:#AIRFLOW_PASSWORD#@service-postgres:5432/airflow
    sql_alchemy_pool_size = 5
    sql_alchemy_pool_recycle = 3600
    parallelism = 32
    dag_concurrency = 16
    dags_are_paused_at_creation = True
    non_pooled_task_slot_count = 128
    max_active_runs_per_dag = 16
    load_examples = False
    plugins_folder = /usr/local/airflow/plugins
    fernet_key = hMjFQ7oRxq0IBDnbnUbm5fWv4j_70jK2ZvxpdbXYO40=
    donot_pickle = False
    dagbag_import_timeout = 30

    [operators]
    default_owner = Airflow

    [webserver]
    base_url = http://localhost:8080
    web_server_host = 0.0.0.0
    web_server_port = 8080
    web_server_worker_timeout = 1200
    secret_key = a932a01d109494bba2891046387897c1cb58ee86c55f69645b8f750896a0
    workers = 4
    worker_class = sync
    expose_config = false
    authenticate = True
    auth_backend = airflow.contrib.auth.backends.ldap_auth
    filter_by_owner = False
    warn_deployment_exposure = False

    [email]
    email_backend = airflow.utils.email.send_email_smtp

    [smtp]
    smtp_host = [SMTP host i.e. smtp.gmail.com]
    smtp_starttls = True
    smtp_ssl = False
    smtp_user = [some e-mail]
    smtp_port = [port i.e. 587]
    smtp_password = [e-mail password]
    smtp_mail_from = <EMAIL>

    [celery]
    celery_app_name = airflow.executors.celery_executor
    celeryd_concurrency = 16
    worker_log_server_port = 8793
    broker_url = None
    celery_result_backend = None
    flower_port = 5555
    default_queue = default

    [scheduler]
    job_heartbeat_sec = 5
    scheduler_heartbeat_sec = 5
    max_threads = 2

    [mesos]
    master = localhost:5050
    framework_name = Airflow
    task_cpu = 1
    task_memory = 256
    checkpoint = False
    authenticate = False

    [spark]
    spark_master = spark://spark:7077
    spark_home = /usr/local/spark
    spark_submit_deploy_mode = client
    spark_submit_bin = /usr/local/spark/bin/spark-submit

    [metrics]
    statsd_on = True
    statsd_host = localhost
    statsd_port = 8125
    statsd_prefix = airflow

    [ldap]
    uri = ldap://***********:389
    bind_user = ${USER}@jainglobal.local
    bind_password = insecure

    #[oauth]
    #provider_key = google
    #redirect_uri = http://a75dcc9bb29af4686a607e39041e91d1-**********.us-east-2.elb.amazonaws.com:8080
    #client_id = 1007745115948-ievcvhhltq9a9rgqae5s0rlkpi8d520q.apps.googleusercontent.com
    #client_secret = 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

    #[lineage]
    #backend = airflow_provider_openmetadata.lineage.backend.OpenMetadataLineageBackend
    #airflow_service_name = service-airflow
    #openmetadata_api_endpoint = http://internal-k8s-default-openmeta-e85dbd1e9b-316426303.us-east-2.elb.amazonaws.com/api
    #jwt_token = "eyJraWQiOiJHYjM4OWEtOWY3Ni1nZGpzLWE5MmotMDI0MmJrOTQzNTYiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************.tS8um_5DKu7HgzGBzS1VTA5uUjKWOCU0B_j08WXBiEC0mr0zNREkqVfwFDD-d24HlNEbrqioLsBuFRiwIWKc1m_ZlVQbG7P36RUxhuv2vbSp80FKyNM-Tj93FDzq91jsyNmsQhyNv_fNr3TXfzzSPjHt8Go0FMMP66weoKMgW2PbXlhVKwEuXUHyakLLzewm9UMeQaEiRzhiTMU3UkLXcKbYEJJvfNFcLwSl9W8JCO_l0Yj3ud-qt_nQYEZwqW6u5nfdQllN133iikV4fM5QZsMCnm8Rq1mvLR0y9bmJiD7fwM1tmJ791TUWqmKaTnP49U493VanKpUAfzIiOiIbhg"



---

apiVersion: v1
kind: Service
metadata:
  name: service-airflow
spec:
  ports:
  - port: 8080
    targetPort: 8080  
    protocol: TCP
  selector:
    airflow: web
  type: NodePort
  externalTrafficPolicy: Local

---

apiVersion: batch/v1
kind: Job
metadata:
  name: initialize-airflow-db
spec:
  template:
    metadata:
      name: initialize-airflow-db
    spec:
      containers:
      - name: initialize-airflow-db
        image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-app-${ENV}:latest
        envFrom:
        - configMapRef:
            name: initialize-airflow-db-config
        command:
          - "bash"
          - "-c"
          - |
            # run commands here if any
        readinessProbe:
          httpGet:
            path: /healthz
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5 
      restartPolicy: Never


---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-web
spec:
  replicas: 1
  selector:
    matchLabels:
      airflow: web
  template:
    metadata:
      labels:
        airflow: web
    spec:
      containers:
        - name: airflow
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-app-${ENV}:latest
          securityContext:
              privileged: true
          env:
            - name: DB_USER
              value: "${AIRFLOW_USERNAME}"
            - name: DB_PWD
              value: "${AIRFLOW_PASSWORD}"
            - name: DB_HOST
              value: service-postgres
            - name: DB_PORT
              value: "5432"
            - name: DB_NAME
              value: "airflow"
            - name: IS_WEB
              value: "yes"
            - name: DB_TYPE
              value: postgres
          ports:
            - name: airflow-web
              containerPort: 8080
            - name: service-statsd
              containerPort: 8125
          volumeMounts:
          - mountPath: /var/tmp/cache
            name: airflow-var-tmp-vol-web
          - mountPath: /home/<USER>/spark/logs
            name: logs-data
          - mountPath: /home/<USER>/logs
            name: airflow-logs
          - mountPath: /home/<USER>/airflow.cfg
            name: airflow-configs
            subPath: airflow.cfg
      volumes:
      - name: airflow-var-tmp-vol-web
        persistentVolumeClaim:
          claimName: airflow-var-tmp-vol-web
      - name: logs-data
        persistentVolumeClaim:
          claimName: common-logs-data
      - name: airflow-logs
        persistentVolumeClaim:
          claimName: common-logs-airflow
      - configMap:
          defaultMode: 420
          name: airflow-configs
        name: airflow-configs

---

apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      airflow: scheduler
  template:
    metadata:
      labels:
        airflow: scheduler
    spec:
      containers:
        - name: airflow
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-app-${ENV}:latest
          securityContext:
              privileged: true
          env:
            - name: DB_USER
              value: "${AIRFLOW_USERNAME}"
            - name: DB_PWD
              value: "${AIRFLOW_PASSWORD}"
            - name: DB_HOST
              value: service-postgres
            - name: DB_PORT
              value: "5432"
            - name: DB_NAME
              value: "airflow"
            - name: IS_WEB
              value: "no"
            - name: DB_TYPE
              value: postgres
            - name: AIRFLOW__SCHEDULER__STATSD_ON
              value: "true"
            - name: AIRFLOW__SCHEDULER__STATSD_HOST
              value: "grafana-agent-statsd.default.svc"
            - name: AIRFLOW__SCHEDULER__STATSD_PORT
              value: "8125"
            - name: AIRFLOW__SCHEDULER__STATSD_PREFIX
              value: "airflow"
          volumeMounts:
          - mountPath: /var/tmp/cache
            name: airflow-var-tmp-vol
          - mountPath: /home/<USER>/spark/logs
            name: logs-data
          - mountPath: /home/<USER>/logs
            name: airflow-logs
          - mountPath: /tmp/data
            name: phdata-tmp
          - mountPath: /home/<USER>/airflow.cfg
            name: airflow-configs
            subPath: airflow.cfg
      volumes:
      - name: airflow-var-tmp-vol
        persistentVolumeClaim:
          claimName: airflow-var-tmp-vol
      - name: logs-data
        persistentVolumeClaim:
          claimName: common-logs-data
      - name: airflow-logs
        persistentVolumeClaim:
          claimName: common-logs-airflow
      - name: phdata-tmp
        persistentVolumeClaim:
          claimName: phdata-tmp
      - configMap:
          defaultMode: 420
          name: airflow-configs
        name: airflow-configs
---

apiVersion: v1
kind: Service
metadata:
  name: service-statsd
spec:
  ports:
    - port: 8125
      targetPort: 8125
      protocol: UDP
  selector:
    airflow: web

---

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "airflow-ingress"
  annotations:
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/certificate-arn: ${SSL_CERTIFICATE_ARN}
  labels:
    app: airflow-nginx-ingress
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: "service-airflow"
              port:
                number: 8080