import sys, os, glob, argparse
from datetime import datetime
from strunner import *
setupEnvironment() 
from jgdata import *
from jgdata.datasets.bloomberg.credit.util import process_file, find_matching_files

BBG_CR_RAW_DATA_PATH = f"{getRawStoreMountPath()}bbg_bo_credit/"
REGIONS = ['asia', 'euro', 'lamr', 'namr']
# TODAY = datetime.today().strftime('%Y%m%d')
parser = argparse.ArgumentParser(description="Download the data for the specified date")
parser.add_argument("--procdate", required=True, help="Specify the processing date in format YYYYMMDD")
args = parser.parse_args()
procdate = args.procdate

FILE_PATTERNS = {
    f'fixedincome_bo_{{region}}_bb_comp.out.gz.{procdate}': 'FIXEDINCOME_BO_BB_COMP',
    f'pfd_{{region}}_bb_comp.out.gz.{procdate}': 'PFD_BB_COMP'
}

files_to_process = find_matching_files(BBG_CR_RAW_DATA_PATH, FILE_PATTERNS, REGIONS)
if not files_to_process:
    print("No files found to process.")
else:
    for file_path, table_name, region in files_to_process:
        print(f"Processing file: {file_path} -> Table: {table_name}, Region: {region}")
        process_file(file_path, table_name, region, BBG_CR_RAW_DATA_PATH)
