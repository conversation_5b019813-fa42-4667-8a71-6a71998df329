auth_enabled: false

compactor:
  working_directory: /loki/compactor
  compaction_interval: 1h
  retention_enabled: true
  retention_delete_delay: 12h
  delete_request_store: filesystem

ingester:
  chunk_block_size: 262144
  chunk_idle_period: 2m
  chunk_retain_period: 1m
  lifecycler:
    ring:
      replication_factor: 1
  wal:
    dir: /jfs/tech1/apps/datait/loki/wal

limits_config:
  allow_structured_metadata: true
  reject_old_samples: true
  reject_old_samples_max_age: 168h
  retention_period: 168h  # 7 days
  max_entries_limit_per_query: 5000
  max_query_series: 100000
  max_query_parallelism: 2

memberlist:
  join_members:
    - 'loki-0.loki-headless.default.svc.cluster.local'

schema_config:
  configs:
    - from: "2020-10-24"
      index:
        period: 24h
        prefix: index_
      object_store: filesystem
      schema: v13
      store: tsdb

server:
  grpc_listen_port: 9095
  http_listen_port: 3100

storage_config:
  tsdb_shipper:
    active_index_directory: /loki/tsdb-index
    cache_location: /loki/tsdb-cache
    cache_ttl: 24h
  filesystem:
    directory: /loki/chunks