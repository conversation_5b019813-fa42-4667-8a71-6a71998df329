raw_data:
  rawdata_location: "/jfs/tech1/apps/rawdata/iirftp"
  s3_bucket: "jg-tech1dev-dp-snowflake-poc-data"
  s3_prefix: "iir"
  include_prefix: true

  structure: '[
  "OFFLINE_EVENT_**$DATE$**.CSV"
  ]'



snowflake:
  db_name: "COMMOD_UAT"
  schema_name: "iir"

  table_map:
  
    offline_events:
      pattern: "^OFFLINE_EVENT_.*$DATE$.*.CSV" 
      col_num: 86
      metadata_columns: ["filename", "start_scan_time"]
      stage_path: "iir/"
      file_format: "ff_commod_iir" 

   
