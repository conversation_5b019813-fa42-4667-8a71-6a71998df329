from datetime import datetime, timedelta
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Query, Depends, status
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
import logging
from icecream import ic
from pydantic import BaseModel
from typing import List, Optional
from services.connection_manager import ConnectionManager
from data_analyzer.data_catalog.data_catalog_data_analyzer  import DataCatalogDataAnaltyzer
from io import BytesIO
import httpx

from typing import Optional
import ssl
import models, schemas,  database
import service as _services
from typing import List
import fastapi as _fastapi
import fastapi.security as _security

import sqlalchemy.orm as _orm


import nltk
nltk.download('punkt')
nltk.download('wordnet')
nltk.download('omw-1.4')

import asyncio
import random
import json
import boto3
import pandas as pd

class ChangeRequest(BaseModel):
    changes: list
    
class OnboardingDatasetStatus(BaseModel):
    dataset: str
    consumer: str
    estimated_uat_date: str
    uat_status: str
    estimated_go_live_date: str
    prod_status: str
    daily_update: str
    
class OnboardingDatasetStatusList(BaseModel):
    data: List[OnboardingDatasetStatus]

app = FastAPI()
ssl_context = ssl.SSLContext(ssl.PROTOCOL_TLS_SERVER)
ssl_context.load_cert_chain('./cert.pem', keyfile='./key.pem')
app.mount("/static", StaticFiles(directory="static"), name="static")

manager = ConnectionManager()

# Configure logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# f = open('config_secret.json')
# config = json.load(f)
# s3 = boto3.client('s3', aws_access_key_id=config['aws_access_key'], aws_secret_access_key=config['aws_secret_key'])
# obj = s3.get_object(Bucket=config['data_catalog_feed_bucket'], Key=config['object_key'])
# data = obj['Body'].read()
# df = pd.read_excel(BytesIO(data), sheet_name='Datasets_Information')

# df = df.fillna('')

# Defining the headers part of the JSON
# headers = {
#     "Dataset_Name": "Dataset_Name",
#     "Dataset_Id": "Dataset_Id",
#     **{col: col for col in df.columns}
# }
# Defining the data part of the JSON
# data = {}
# for index, row in df.iterrows():
#     dataset_key = row['Dataset_Name']
#     # data[dataset_key] = row.to_dict()
#     data[dataset_key] = {
#         "Dataset_Id": dataset_key,
#         **{col: row[col] for col in df.columns if col != 'Dataset_Name'}
#     }
    
# # Combining headers and data into final JSON format
# jsonResult = {
#     "headers": headers,
#     "data": data
# }
# with open('data/datacatalog.json', 'w') as json_file:
#     json.dump(jsonResult, json_file, default=str)

# # Load dummy datasets from JSON file
# with open("data/datacatalog.json", "r") as f:
#     dummy_datasets = json.load(f)
#     excel_df = df

# Initialize the data catalog analyzer
api_key = "***************************************************"
input_datapath = "data/datacatalog.json"
output_datapath = "data/data_catalog_with_embeddings_1k"
#data_catalog_analyzer = DataCatalogDataAnaltyzer(api_key, input_datapath, output_datapath, debug=True)
# data_catalog_analyzer = DataCatalogDataAnaltyzer(input_datapath, output_datapath, debug=True)
#data_catalog_analyzer.check_data_availability()
#data_catalog_analyzer.load_or_create_embeddings()
# word_tokens = data_catalog_analyzer.create_word_tokens()

# Initialize the global variable for update frequency
update_frequency = 0.1  # Default frequency (10 updates per second)

@app.get("/")
async def read_index():
    return FileResponse("static/index.html")

@app.get("/api/data_catalog/{data_set_id}")
async def get_data_catalog(data_set_id: str):
    data = dummy_datasets['data'].get(data_set_id)
    if not data:
        raise HTTPException(status_code=404, detail="Data set not found (" + data_set_id + ")") 
    return data

@app.get("/api/search")
async def search_data_catalog(q: Optional[str] = Query("")):
    logger.info("Received search query: %s", q)
    columns = []
    data = await get_datasets()
    returnData = {"search_results": data, "etl_status": []}
    
    return returnData
        
@app.get("/api/search2")
async def search_data_catalog():
    logger.info("Received search query: ")
    columns = []
    async with httpx.AsyncClient() as client:
        data = await get_datasets()
        returnData = {"search_results": data, "etl_status": []}
 
        return returnData

@app.websocket("/ws/{symbol}/{subscriber_id}")
async def websocket_endpoint(websocket: WebSocket, symbol: str, subscriber_id: str):
    await manager.connect(websocket, subscriber_id)
    try:
        while True:
            # Base bid and ask
            base_bid = 100 * (1 + random.random() / 10)
            base_ask = base_bid + random.random() * 2
            
            # Generate L1 to L5 bid/ask prices and sizes
            levels = {}
            for level in range(1, 6):
                decrement = random.random() * base_bid / 100  # Decrease each bid level by a percentage
                increment = random.random() * base_ask / 100  # Increase each ask level by a percentage
                bid_price = base_bid - decrement * level
                ask_price = base_ask + increment * level
                
                bid_size = random.randint(1, 100)  # Replace with your sizing logic
                ask_size = random.randint(1, 100)  # Replace with your sizing logic
                
                levels[f'bidPriceL{level}'] = bid_price
                levels[f'askPriceL{level}'] = ask_price
                levels[f'bidSizeL{level}'] = bid_size
                levels[f'askSizeL{level}'] = ask_size

            # Construct the message including the new L1 to L5 data
            message_data = {
                "symbol": symbol,
                "currentBidPrice": base_bid,
                "currentAskPrice": base_ask,
                "bidPrice": base_bid,
                "askPrice": base_ask,
                **levels  # Merge the levels dict into the message data
            }
            message = json.dumps(message_data)

            await manager.broadcast_to_subscriber(message, subscriber_id)
            await asyncio.sleep(update_frequency)
    except WebSocketDisconnect:
        manager.disconnect(websocket, subscriber_id)
        
        
###############################################################################################################

@app.post("/api/users")
async def create_user(
    user: schemas.UserCreate, db: _orm.Session = _fastapi.Depends(_services.get_db)
):
    db_user = await _services.get_user_by_email(user.email, db)
    if db_user:
        raise _fastapi.HTTPException(status_code=400, detail="Email already in use")

    user = await _services.create_user(user, db)

    return await _services.create_token(user)


@app.post("/api/token")
async def generate_token(
    form_data: _security.OAuth2PasswordRequestForm = _fastapi.Depends(),
    db: _orm.Session = _fastapi.Depends(_services.get_db),
):
    user = await _services.authenticate_user(form_data.username, form_data.password, db)

    if not user:
        raise _fastapi.HTTPException(status_code=401, detail="Invalid Credentials")

    return await _services.create_token(user)


@app.get("/api/users/me", response_model=schemas.User)
async def get_user(user: schemas.User = _fastapi.Depends(_services.get_current_user)):
    return user


@app.post("/api/add-row", response_model=schemas.Catalog)
async def create_dataset(
    dataset: schemas.DatasetCreate,
    user: schemas.User = _fastapi.Depends(_services.get_current_user)
):
    db: _orm.Session = _services.get_db
    return await _services.create_dataset(user=user, db=db, dataset=dataset)


@app.get("/api/rows", response_model=List[schemas.Catalog])
async def get_datasets(
    # user: schemas.User = _fastapi.Depends(_services.get_current_user),
    # db: _orm.Session = _fastapi.Depends(_services.get_db),
):
    user: schemas.User = _services.get_current_user
    db: _orm.Session = _services.get_db
    return await _services.get_datasets(user=user, db=db)


@app.get("/api/rows/{dataset_id}", status_code=200)
async def get_dataset(
    dataset_id: int,
    user: schemas.User = _fastapi.Depends(_services.get_current_user),
    db: _orm.Session = _fastapi.Depends(_services.get_db),
):
    return await _services.get_datasets(dataset_id, user, db)


@app.delete("/api/rows/{dataset_id}", status_code=204)
async def delete_dataset(
    dataset_id: str,
    user: schemas.User = _fastapi.Depends(_services.get_current_user),
):
    db: _orm.Session = _services.get_db
    await _services.delete_dataset(dataset_id, user, db)
    return {"message", "Successfully Deleted"}


@app.put("/api/rows/{dataset_id}", status_code=200)
async def update_dataset(
    dataset_id: str,
    dataset: schemas.DatasetCreate,
    user: schemas.User = _fastapi.Depends(_services.get_current_user),
):
    db: _orm.Session = _services.get_db
    await _services.update_dataset(dataset_id, dataset, user, db)
    return {"message", "Successfully Updated"}

@app.get('/api/getETL_status/{dateParam}')
async def get_etl_status(dateParam: Optional[str]):
    
    api_url = f'http://10.115.5.130:8000/getDataSet'
    if dateParam: 
        api_url += f"?date={dateParam}"
    print("API_URL",api_url)
    async with httpx.AsyncClient() as client:
        response = await client.get(api_url)
        all_datasets = response.json()
        print("get_etl_status is triggered")
        return all_datasets

@app.get('/api/getOnboardingDatasetStatus/{dateParam}')
async def getOnboardingDatasetStatus(dateParam: Optional[str]):
    
    api_url = f'http://10.115.5.130:8000/getDatasetOnboardingDetails'
    if dateParam: 
        api_url += f"?date={dateParam}"
    print("API_URL",api_url)
    async with httpx.AsyncClient() as client:
        response = await client.get(api_url)
        all_datasets = response.json()
        return all_datasets
    
@app.post('/api/updateOnboardingDatasetStatus')
async def getOnboardingDatasetStatus(request: OnboardingDatasetStatusList):
    
    api_url = f'http://10.115.5.130:8000/insertDatasetOnboardingDetails'
    print("API_URL",api_url)
    async with httpx.AsyncClient() as client:
        response = await client.post(api_url,data=request.data)
        all_datasets = response.json()
        return all_datasets

@app.get('/api/getAppcatalogData')
async def getAppcatalogData():
    
    api_url = f'http://10.115.5.130:8000/getAppCatalogDetails'
    async with httpx.AsyncClient() as client:
        response = await client.get(api_url)
        all_datasets = response.json()
        return all_datasets
    
@app.put("/api/updateApp")
async def updateApp(data: dict):
    async with httpx.AsyncClient() as client:
        response = await client.put(f'http://10.115.5.130:8000/insertAppCatalogDetails', json=data)

    
    return {
        "status_code": response.status_code,
        "response": response.json()
    }
    
@app.get("/api/download_historical_status/{dataset_id}")
async def downloadHistorical_ETLStatus(dataset_id: str):
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(f'http://10.112.18.166:8080/data_variation_analysis?dataset_id={dataset_id}&days=30', timeout=10)
            if response.status_code == 200:
                file_name = f"{dataset_id}_{datetime.now().date()}.csv"
                with open(file_name, 'wb') as f:
                    f.write(response.content)
                return FileResponse(file_name, filename=file_name, media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
            else:
                raise HTTPException(status_code=response.status_code, detail="Failed to download file")
    
        except httpx.TimeoutException as e:
            raise HTTPException(status_code=response.status_code, detail="Failed to download file due to timeout error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0")
