#!/usr/bin/env python3
"""
Standalone script to download Meteologica data for a specific content_id and date.
Downloads historical data by month and saves to local directory.
"""

import os
import sys
import json
import time
import requests
import jwt
import argparse
from pathlib import Path
from zipfile import ZipFile
from datetime import datetime

class MeteologicaDownloader:
    """Simplified Meteologica API client for downloading specific data."""
    
    def __init__(self, username=None, password=None):
        """Initialize with credentials."""
        self.username = username or os.getenv('METEOLOGICA_USERNAME')
        self.password = password or os.getenv('METEOLOGICA_PASSWORD')
        self.token = None
        self.base_url = "https://api-markets.meteologica.com/api/v1"
        
        if not self.username or not self.password:
            raise ValueError("METEOLOGICA_USERNAME and METEOLOGICA_PASSWORD must be set as environment variables or passed as parameters")
    
    def _get_new_token(self):
        """Get a new authentication token."""
        url = f"{self.base_url}/login"
        payload = {"user": self.username, "password": self.password}
        
        print("Getting new authentication token...")
        response = requests.post(url, json=payload)
        response.raise_for_status()
        
        token = response.json()["token"]
        self.token = token
        print("Token obtained successfully")
        return token
    
    def _get_token(self):
        """Get or refresh token as needed."""
        if not self.token or self._is_token_expired():
            return self._get_new_token()
        return self.token
    
    def _is_token_expired(self):
        """Check if current token is expired."""
        try:
            exp = jwt.decode(self.token, options={"verify_signature": False})["exp"]
            return time.time() > exp
        except:
            return True
    
    def download_historical_data(self, content_id, year, month, output_dir="./meteologica_data"):
        """
        Download historical data for specific content_id, year, and month.
        
        Args:
            content_id (int): Content ID to download
            year (int): Year (e.g., 2024)
            month (int): Month (1-12)
            output_dir (str): Directory to save downloaded files
            
        Returns:
            str: Path to downloaded ZIP file
        """
        # Ensure output directory exists
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        
        # Get token
        token = self._get_token()
        
        # Build request
        endpoint = f"contents/{content_id}/historical_data/{year}/{month}"
        url = f"{self.base_url}/{endpoint}"
        params = {"token": token}
        
        print(f"Downloading data for content_id={content_id}, year={year}, month={month}")
        print(f"URL: {url}")
        
        # Make request
        response = requests.get(url, params=params)
        response.raise_for_status()
        
        # Save ZIP file
        filename = f"content_{content_id}_{year}_{month:02d}.zip"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'wb') as f:
            f.write(response.content)
        
        print(f"Data downloaded successfully: {filepath}")
        print(f"File size: {len(response.content)} bytes")
        
        return filepath
    
    def extract_and_filter_date(self, zip_filepath, target_date, extract_dir=None):
        """
        Extract ZIP file and filter for specific date.
        
        Args:
            zip_filepath (str): Path to ZIP file
            target_date (str): Date in format 'YYYY-MM-DD'
            extract_dir (str): Directory to extract to (optional)
            
        Returns:
            list: Filtered data for the target date
        """
        if extract_dir is None:
            extract_dir = zip_filepath.replace('.zip', '_extracted')
        
        # Extract ZIP file
        print(f"Extracting {zip_filepath} to {extract_dir}")
        with ZipFile(zip_filepath, 'r') as zip_ref:
            zip_ref.extractall(extract_dir)
        
        # Find and process JSON files
        extracted_path = Path(extract_dir)
        json_files = list(extracted_path.rglob('*.json'))
        
        print(f"Found {len(json_files)} JSON files")
        
        filtered_data = []
        target_date_str = target_date
        
        for json_file in json_files:
            print(f"Processing {json_file.name}")
            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)
                
                # Filter data for target date
                if 'data' in data and data['data']:
                    for item in data['data']:
                        if 'date' in item and target_date_str in str(item.get('date', '')):
                            filtered_data.append(item)
                
                # Also check issue_date
                issue_date = data.get('issue_date', '')
                if target_date_str in issue_date:
                    print(f"Found data with issue_date: {issue_date}")
                    
            except Exception as e:
                print(f"Error processing {json_file}: {e}")
        
        print(f"Found {len(filtered_data)} data points for {target_date}")
        return filtered_data


def main():
    """Main function to download specific data."""
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Download Meteologica data for specific content_id and date")
    parser.add_argument("--content_id", type=int, default=743, help="Content ID to download (default: 743)")
    parser.add_argument("--year", type=int, default=2024, help="Year to download (default: 2024)")
    parser.add_argument("--month", type=int, default=11, help="Month to download (1-12, default: 11)")
    parser.add_argument("--target_date", type=str, default=None, help="Specific date to filter for (YYYY-MM-DD format, optional)")
    parser.add_argument("--output_dir", type=str, default="./meteologica_download", help="Output directory (default: ./meteologica_download)")
    parser.add_argument("--username", type=str, default=None, help="Meteologica username (overrides env var)")
    parser.add_argument("--password", type=str, default=None, help="Meteologica password (overrides env var)")
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.month < 1 or args.month > 12:
        print("Error: Month must be between 1 and 12")
        sys.exit(1)
    
    # Configuration from arguments
    CONTENT_ID = args.content_id
    YEAR = args.year
    MONTH = args.month
    TARGET_DATE = args.target_date if args.target_date else f"{YEAR}-{MONTH:02d}-xx"
    OUTPUT_DIR = args.output_dir
    
    print(f"Configuration:")
    print(f"  Content ID: {CONTENT_ID}")
    print(f"  Year: {YEAR}")
    print(f"  Month: {MONTH}")
    print(f"  Target Date: {TARGET_DATE}")
    print(f"  Output Directory: {OUTPUT_DIR}")
    
    try:
        # Initialize downloader
        print("Initializing Meteologica downloader...")
        downloader = MeteologicaDownloader(username=args.username, password=args.password)
        
        # Download historical data
        print(f"\nDownloading data for content_id {CONTENT_ID}, {YEAR}-{MONTH:02d}")
        zip_filepath = downloader.download_historical_data(
            content_id=CONTENT_ID,
            year=YEAR,
            month=MONTH,
            output_dir=OUTPUT_DIR
        )
        
        # Extract and filter for specific date
        print(f"\nExtracting and filtering for date: {TARGET_DATE}")
        filtered_data = downloader.extract_and_filter_date(zip_filepath, TARGET_DATE)
        
        # Save filtered data
        if filtered_data:
            filtered_filepath = os.path.join(OUTPUT_DIR, f"content_{CONTENT_ID}_{TARGET_DATE}.json")
            with open(filtered_filepath, 'w') as f:
                json.dump(filtered_data, f, indent=2)
            print(f"Filtered data saved to: {filtered_filepath}")
        else:
            print(f"No data found for the specific date: {TARGET_DATE}")
            print("The ZIP file contains the full month's data - you may need to examine it manually")
        
        print(f"\nDownload completed successfully!")
        print(f"Files saved in: {OUTPUT_DIR}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Usage examples:
    # python test_download.py --content_id 743 --year 2024 --month 11 --target_date 2024-11-29
    # python test_download.py --content_id 13 --year 2024 --month 10
    # python test_download.py --username myuser --password mypass --content_id 743
    
    # You can also set credentials as environment variables:
    # export METEOLOGICA_USERNAME="your_username"
    # export METEOLOGICA_PASSWORD="your_password"
    
    main()