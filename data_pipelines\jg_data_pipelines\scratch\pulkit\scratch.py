from utils.date_utils import get_now, resolve_relative_date
from datetime import datetime, date
import pandas as pd

if __name__ == "__main__":
    end_date = datetime(2025, 5, 7, 0, 0)
    start_date = (pd.Timestamp(end_date) - pd.tseries.offsets.BDay(2)).to_pydatetime()

    if start_date.month < end_date.month or start_date.year < end_date.year:
        start_date = datetime(end_date.year, end_date.month, 1, 0, 0)
    
    print(start_date, end_date)
