echo "Kpler Historical Start"

source $HOME/.bashrc

# python /home/<USER>/code/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_historical.py --dataset avail_per_fuel_type --start_date 2025-03-01 --end_date 2025-03-25 >> /home/<USER>/logs/kpler/historical_avail_per_fueltype.log 2>&1
# python /home/<USER>/code/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_historical.py --dataset avail_per_unit --start_date 2025-02-22 --end_date 2025-03-25 >> /home/<USER>/logs/kpler/historical_avail_per_unit.log 2>&1
# python /home/<USER>/code/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_historical.py --dataset actual_load --start_date 2025-01-28 --end_date 2025-03-25 >> /home/<USER>/logs/kpler/historical_actual_load.log 2>&1

# python /home/<USER>/code/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_historical.py --dataset gen_per_fuel_type --start_date 2025-01-28 --end_date 2025-03-27 >> /home/<USER>/logs/kpler/historical_gen_per_fueltype.log 2>&1
# python /home/<USER>/code/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_historical.py --dataset gen_per_unit --start_date 2025-01-28 --end_date 2025-03-27 >> /home/<USER>/logs/kpler/historical_gen_per_unit.log 2>&1
# python /home/<USER>/code/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_historical.py --dataset forecast_load --start_date 2025-01-15 --end_date 2025-03-27 >> /home/<USER>/logs/kpler/historical_forecast_load.log 2>&1
# python /home/<USER>/code/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_historical.py --dataset consumption_per_fuel_type --start_date 2025-01-24 --end_date 2025-03-27 >> /home/<USER>/logs/kpler/historical_consumption.log 2>&1
python /home/<USER>/code/JG-Data-Pipelines/jg_data_pipelines/kpler/kpler_historical.py --dataset commercial_schedules --start_date 2025-01-28 --end_date 2025-03-27 >> /home/<USER>/logs/kpler/historical_commercial.log 2>&1

echo "Kpler Historical End"
