apiVersion: apps/v1
kind: Deployment
metadata:
  name: openmetadata-ingestion
spec:
  replicas: 1
  selector:
    matchLabels:
      app: openmetadata-ingestion
  template:
    metadata:
      labels:
        app: openmetadata-ingestion
    spec:
      containers:
        - name: openmetadata-ingestion
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-openmetadata-${ENV}:latest
          command: ["/bin/sleep", "3650d"]
  