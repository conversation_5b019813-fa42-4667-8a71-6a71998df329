import logging
from utils.snowflake.adaptor import SnowflakeAdaptor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
            database="KPLER", 
            warehouse="TEST", 
            role="FR_DATA_PLATFORM")
    
    logger.info("Starting to run the Kpler day ahead cache for Availability Per Units")

    availability_per_units_query = """

    CREATE OR REPLACE TABLE KPLER.KPLER_POWER.DAY_AHEAD_AVAILABILITY_PER_UNITS AS
        select apu.TIMESTAMP,
        apu.AVAILABILITY_AMOUNT,
        apu.TYPE,
        apu.AS_OF,
        apu.PROVIDER,
        apu.COUNTRY,
        apu.UNIT,
        apu.GENERATION_CODE,
        apu.PRODUCTION_CODE,
        apu.ASSET_TYPE,
        apu.FUEL_TYPE,
        apu.TIMEZONE,
        apu.JSON_FILE
        from KPLER.KPLER_POWER.VW_DAILY_TIMESTAMPS_10AM v10 join 
        KPLER.KPLER_POWER.VW_AVAILABILITY_PER_UNITS apu on apu.as_of = v10.dayn10am and apu.timestamp = v10.daynplusone
        WHERE v10.dayndate >= DATEADD('YEAR', -5, CURRENT_DATE) and v10.dayndate <= CURRENT_DATE;

    """

    sf_adaptor.execute_query("KPLER_POWER", availability_per_units_query, "FR_DATA_PLATFORM")
    logger.info("Finished running the Kpler day ahead cache for Availability Per Units")

    logger.info("Starting to run the Kpler day ahead cache for Availability by Fuel Type")
    availability_by_fuel_type_query = """

    CREATE OR REPLACE TABLE KPLER.KPLER_POWER.DAY_AHEAD_AVAILABILITY_BY_FUELTYPE AS
        select apf.TIMESTAMP,
        apf.AVAILABILITY_AMOUNT,
        apf.AS_OF,
        apf.PROVIDER,
        apf.COUNTRY,
        apf.TIMEZONE,
        apf.LEVEL,
        apf.FUEL_TYPE,
        apf.JSON_FILE
        from KPLER.KPLER_POWER.VW_DAILY_TIMESTAMPS_10AM v10 join 
        KPLER.KPLER_POWER.VW_AVAILABILITY_BY_FUELTYPE apf on apf.as_of = v10.dayn10am and apf.timestamp = v10.daynplusone
        WHERE v10.dayndate >= DATEADD('YEAR', -5, CURRENT_DATE) and v10.dayndate <= CURRENT_DATE;

    """

    sf_adaptor.execute_query("KPLER_POWER", availability_by_fuel_type_query, "FR_DATA_PLATFORM")
    logger.info("Finished running the Kpler day ahead cache for Availability by Fuel Type")

    logger.info("Starting to run the Kpler day ahead cache for Availability Per Units for CET noon")

    availability_per_units_query = """

    CREATE OR REPLACE TABLE KPLER.KPLER_POWER.DAY_AHEAD_AVAILABILITY_PER_UNITS_CET_NOON AS
        select v10.DAYN12PM_CET,
        v10.DAYNPLUSONE_CET,
        apu.TIMESTAMP,
        apu.AVAILABILITY_AMOUNT,
        apu.TYPE,
        apu.AS_OF,
        apu.PROVIDER,
        apu.COUNTRY,
        apu.UNIT,
        apu.GENERATION_CODE,
        apu.PRODUCTION_CODE,
        apu.ASSET_TYPE,
        apu.FUEL_TYPE,
        apu.TIMEZONE,
        apu.JSON_FILE
        from KPLER.KPLER_POWER.DAILY_DAYAHEAD_TIMESTAMPS_NOON_CET v10 join 
        KPLER.KPLER_POWER.VW_AVAILABILITY_PER_UNITS apu on apu.as_of = v10.DAYN12PM_CET_UTC and apu.timestamp = v10.DAYNPLUSONE_CET_UTC
        WHERE v10.dayndate >= DATEADD('YEAR', -5, CURRENT_DATE) and v10.dayndate <= CURRENT_DATE;

    """

    sf_adaptor.execute_query("KPLER_POWER", availability_per_units_query, "FR_DATA_PLATFORM")
    logger.info("Finished running the Kpler day ahead cache for Availability Per Units  for CET noon")

    logger.info("Starting to run the Kpler day ahead cache for Availability by Fuel Type for CET noon")
    availability_by_fuel_type_query = """

    CREATE OR REPLACE TABLE KPLER.KPLER_POWER.DAY_AHEAD_AVAILABILITY_BY_FUELTYPE_CET_NOON AS
        select v10.DAYN12PM_CET,
        v10.DAYNPLUSONE_CET,
        apf.TIMESTAMP,
        apf.AVAILABILITY_AMOUNT,
        apf.AS_OF,
        apf.PROVIDER,
        apf.COUNTRY,
        apf.TIMEZONE,
        apf.LEVEL,
        apf.FUEL_TYPE,
        apf.JSON_FILE
        from KPLER.KPLER_POWER.DAILY_DAYAHEAD_TIMESTAMPS_NOON_CET v10 join 
        KPLER.KPLER_POWER.VW_AVAILABILITY_BY_FUELTYPE apf on apf.as_of = v10.DAYN12PM_CET_UTC and apf.timestamp = v10.DAYNPLUSONE_CET_UTC
        WHERE v10.dayndate >= DATEADD('YEAR', -5, CURRENT_DATE) and v10.dayndate <= CURRENT_DATE;

    """

    sf_adaptor.execute_query("KPLER_POWER", availability_by_fuel_type_query, "FR_DATA_PLATFORM")
    logger.info("Finished running the Kpler day ahead cache for Availability by Fuel Type for CET noon")