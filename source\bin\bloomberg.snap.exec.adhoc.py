import sys, os, glob, argparse
import subprocess
from strunner import *
setupEnvironment() # sets up environment
from jgdata import *
import stcommon
from stcommon.infra.python.module import function_from_path

JGDATA_PATH = os.environ.get("JGDATA_PATH")

parser = argparse.ArgumentParser()
parser.add_argument("--commands", help="Please enter list of bloomberg snap python commands separated by comma", required=True, type=str)
options = parser.parse_args()

if options.commands:
   commands_list = options.commands.split(',')
   print(f"Parameter passed are:\n {commands_list}")
else:
   print("Missing Arguments..")
 
for command in commands_list:
   print("Execution command:\n", command)
   command_to_be_run = command.split(' ')
   try:
      subprocess.run(command_to_be_run)
      print(f'\n\nExecution completed for {command}\n\n')
   except Exception as e:
      log.error("Unexpected error: {}".format(str(e)))
      raise e
    
