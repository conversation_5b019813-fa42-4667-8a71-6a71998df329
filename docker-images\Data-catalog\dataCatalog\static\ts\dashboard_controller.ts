class DashboardController {
    private model: DashboardModel;

    constructor() {
        this.model = new DashboardModel();

        // Adding an event listener to the slider in the document
        this.attachDocumentListeners();
    }

    private attachDocumentListeners(): void {
        const frequencySlider = document.getElementById('frequencySlider') as HTMLInputElement;
        if (frequencySlider) {
            frequencySlider.addEventListener('input', () => {
                this.adjustTickFrequency(parseInt(frequencySlider.value));
            });
        }
    }

    private adjustTickFrequency(ticksPerSecond: number): void {
        let displayText: string;
        displayText = `${1/(1/(ticksPerSecond/10))} `;
        this.model.freqDisplay = displayText;
        console.log('Updating frequency to:', displayText); // Debugging log

        // Assuming a FastAPI server endpoint is set up to receive frequency updates
        fetch(`${this.model.baseUrl}/api/update-frequency`, {
            method: "POST",
            headers: {
                "Content-Type": "application/json",
            },
            body: JSON.stringify({ frequency: ticksPerSecond / 10 }) // converting slider value to actual frequency
        })
            .then(response => response.json())
            // .then(data => console.log('Server response:', data))
            .catch(error => console.error('Error:', error));
    }
}
