dataset = 'spglobal.financials'
catalog = 'lake'
version = 'mk1'
description = 'spglobal financials'
scope = ['gl']
tables = [
  'financialPeriod',
  'financialInstance',
  'financialInstanceToCollection',
  'financialCollection',
  'financialDataCollectionType',
  'financialInstanceType',
  'statementSpecificRestatementType',
  'financialInstanceToCollectionType',
  'finInstanceDate',
  'finInstanceDateType',
  'financialUnitType',
  'periodType',
  'restatementType',
  'financialCollectionData',
  'financialCollectionTextData', 
  'financialCollectionDataRatio'
  ]  