apiVersion: v1
kind: PersistentVolume
metadata:
  name: grafana-agent-pv
spec:
  capacity:
    storage: 1Gi
  accessModes:
    - ReadWriteOnce
  hostPath:
    path: /etc/agent/data
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-agent-pvc
spec:
  storageClassName: efs-sc
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-agent-config
data:
  agent.yaml: |
    server:
      log_level: debug

    integrations:
      prometheus_remote_write:
      - basic_auth:
          password: ${GRAFANA_PROM_PASSWORD}
          username: ${GRAFANA_PROM_ACCOUNT_ID}
        url: https://prometheus-prod-13-prod-us-east-0.grafana.net/api/prom/push

      agent:
        enabled: true
        relabel_configs:
        - action: replace
          source_labels:
          - agent_hostname
          target_label: instance
        - action: replace
          target_label: job
          replacement: "integrations/agent-check"
        metric_relabel_configs:
        - action: keep
          regex: (prometheus_target_sync_length_seconds_sum|prometheus_target_scrapes_.*|prometheus_target_interval.*|prometheus_sd_discovered_targets|agent_build.*|agent_wal_samples_appended_total|process_start_time_seconds)
          source_labels:
          - __name__

      statsd_exporter:
        enabled: true
        instance: 'jg-${ENV}-airflow'
        listen_udp: '0.0.0.0:8125'
        scrape_interval: 60s
        scrape_integration: true
        relabel_configs:
        - target_label: job
          replacement: 'integrations/apache-airflow'
        mapping_config:
          mappings:
            # Airflow StatsD metrics mappings (https://airflow.apache.org/docs/apache-airflow/stable/logging-monitoring/metrics.html)
            # === Counters ===
            - match: "(.+)\\.(.+)_start$"
              match_metric_type: counter
              name: "airflow_job_start"
              match_type: regex
              labels:
                airflow_id: "$1"
                job_name: "$2"
            - match: "(.+)\\.(.+)_end$"
              match_metric_type: counter
              name: "airflow_job_end"
              match_type: regex
              labels:
                airflow_id: "$1"
                job_name: "$2"
            - match: "(.+)\\.operator_failures_(.+)$"
              match_metric_type: counter
              name: "airflow_operator_failures"
              match_type: regex
              labels:
                airflow_id: "$1"
                operator_name: "$2"
            - match: "(.+)\\.operator_successes_(.+)$"
              match_metric_type: counter
              name: "airflow_operator_successes"
              match_type: regex
              labels:
                airflow_id: "$1"
                operator_name: "$2"
            - match: "*.ti_failures"
              match_metric_type: counter
              name: "airflow_ti_failures"
              labels:
                airflow_id: "$1"
            - match: "*.ti_successes"
              match_metric_type: counter
              name: "airflow_ti_successes"
              labels:
                airflow_id: "$1"
            - match: "*.zombies_killed"
              match_metric_type: counter
              name: "airflow_zombies_killed"
              labels:
                airflow_id: "$1"
            - match: "*.scheduler_heartbeat"
              match_metric_type: counter
              name: "airflow_scheduler_heartbeat"
              labels:
                airflow_id: "$1"
            - match: "*.dag_processing.processes"
              match_metric_type: counter
              name: "airflow_dag_processing_processes"
              labels:
                airflow_id: "$1"
            - match: "*.scheduler.tasks.killed_externally"
              match_metric_type: counter
              name: "airflow_scheduler_tasks_killed_externally"
              labels:
                airflow_id: "$1"
            - match: "*.scheduler.tasks.running"
              match_metric_type: counter
              name: "airflow_scheduler_tasks_running"
              labels:
                airflow_id: "$1"
            - match: "*.scheduler.tasks.starving"
              match_metric_type: counter
              name: "airflow_scheduler_tasks_starving"
              labels:
                airflow_id: "$1"
            - match: "*.scheduler.orphaned_tasks.cleared"
              match_metric_type: counter
              name: "airflow_scheduler_orphaned_tasks_cleared"
              labels:
                airflow_id: "$1"
            - match: "*.scheduler.orphaned_tasks.adopted"
              match_metric_type: counter
              name: "airflow_scheduler_orphaned_tasks_adopted"
              labels:
                airflow_id: "$1"
            - match: "*.scheduler.critical_section_busy"
              match_metric_type: counter
              name: "airflow_scheduler_critical_section_busy"
              labels:
                airflow_id: "$1"
            - match: "*.sla_email_notification_failure"
              match_metric_type: counter
              name: "airflow_sla_email_notification_failure"
              labels:
                airflow_id: "$1"
            - match: "*.ti.start.*.*"
              match_metric_type: counter
              name: "airflow_ti_start"
              labels:
                airflow_id: "$1"
                dag_id: "$2"
                task_id: "$3"
            - match: "*.ti.finish.*.*.*"
              match_metric_type: counter
              name: "airflow_ti_finish"
              labels:
                airflow_id: "$1"
                dag_id: "$2"
                task_id: "$3"
                state: "$4"
            - match: "*.dag.callback_exceptions"
              match_metric_type: counter
              name: "airflow_dag_callback_exceptions"
              labels:
                airflow_id: "$1"
            - match: "*.celery.task_timeout_error"
              match_metric_type: counter
              name: "airflow_celery_task_timeout_error"
              labels:
                airflow_id: "$1"

            # === Gauges ===
            - match: "*.dagbag_size"
              match_metric_type: gauge
              name: "airflow_dagbag_size"
              labels:
                airflow_id: "$1"
            - match: "*.dag_processing.import_errors"
              match_metric_type: gauge
              name: "airflow_dag_processing_import_errors"
              labels:
                airflow_id: "$1"
            - match: "*.dag_processing.total_parse_time"
              match_metric_type: gauge
              name: "airflow_dag_processing_total_parse_time"
              labels:
                airflow_id: "$1"
            - match: "*.dag_processing.last_runtime.*"
              match_metric_type: gauge
              name: "airflow_dag_processing_last_runtime"
              labels:
                airflow_id: "$1"
                dag_file: "$2"
            - match: "*.dag_processing.last_run.seconds_ago.*"
              match_metric_type: gauge
              name: "airflow_dag_processing_last_run_seconds"
              labels:
                airflow_id: "$1"
                dag_file: "$2"
            - match: "*.dag_processing.processor_timeouts"
              match_metric_type: gauge
              name: "airflow_dag_processing_processor_timeouts"
              labels:
                airflow_id: "$1"
            - match: "*.executor.open_slots"
              match_metric_type: gauge
              name: "airflow_executor_open_slots"
              labels:
                airflow_id: "$1"
            - match: "*.executor.queued_tasks"
              match_metric_type: gauge
              name: "airflow_executor_queued_tasks"
              labels:
                airflow_id: "$1"
            - match: "*.executor.running_tasks"
              match_metric_type: gauge
              name: "airflow_executor_running_tasks"
              labels:
                airflow_id: "$1"
            - match: "*.pool.open_slots.*"
              match_metric_type: gauge
              name: "airflow_pool_open_slots"
              labels:
                airflow_id: "$1"
                pool_name: "$2"
            - match: "*.pool.queued_slots.*"
              match_metric_type: gauge
              name: "airflow_pool_queued_slots"
              labels:
                airflow_id: "$1"
                pool_name: "$2"
            - match: "*.pool.running_slots.*"
              match_metric_type: gauge
              name: "airflow_pool_running_slots"
              labels:
                airflow_id: "$1"
                pool_name: "$2"
            - match: "*.pool.starving_tasks.*"
              match_metric_type: gauge
              name: "airflow_pool_starving_tasks"
              labels:
                airflow_id: "$1"
                pool_name: "$2"
            - match: "*.smart_sensor_operator.poked_tasks"
              match_metric_type: gauge
              name: "airflow_smart_sensor_operator_poked_tasks"
              labels:
                airflow_id: "$1"
            - match: "*.smart_sensor_operator.poked_success"
              match_metric_type: gauge
              name: "airflow_smart_sensor_operator_poked_success"
              labels:
                airflow_id: "$1"
            - match: "*.smart_sensor_operator.poked_exception"
              match_metric_type: gauge
              name: "airflow_smart_sensor_operator_poked_exception"
              labels:
                airflow_id: "$1"
            - match: "*.smart_sensor_operator.exception_failures"
              match_metric_type: gauge
              name: "airflow_smart_sensor_operator_exception_failures"
              labels:
                airflow_id: "$1"
            - match: "*.smart_sensor_operator.infra_failures"
              match_metric_type: gauge
              name: "airflow_smart_sensor_operator_infra_failures"
              labels:
                airflow_id: "$1"

            # === Timers ===
            - match: "*.dagrun.dependency-check.*"
              match_metric_type: observer
              name: "airflow_dagrun_dependency_check"
              labels:
                airflow_id: "$1"
                dag_id: "$2"
            - match: "*.dag.*.*.duration"
              match_metric_type: observer
              name: "airflow_dag_task_duration"
              labels:
                airflow_id: "$1"
                dag_id: "$2"
                task_id: "$3"
            - match: "*.dag_processing.last_duration.*"
              match_metric_type: observer
              name: "airflow_dag_processing_duration"
              labels:
                airflow_id: "$1"
                dag_file: "$2"
            - match: "*.dagrun.duration.success.*"
              match_metric_type: observer
              name: "airflow_dagrun_duration_success"
              labels:
                airflow_id: "$1"
                dag_id: "$2"
            - match: "*.dagrun.duration.failed.*"
              match_metric_type: observer
              name: "airflow_dagrun_duration_failed"
              labels:
                airflow_id: "$1"
                dag_id: "$2"
            - match: "*.dagrun.schedule_delay.*"
              match_metric_type: observer
              name: "airflow_dagrun_schedule_delay"
              labels:
                airflow_id: "$1"
                dag_id: "$2"
            - match: "*.scheduler.critical_section_duration"
              match_metric_type: observer
              name: "airflow_scheduler_critical_section_duration"
              labels:
                airflow_id: "$1"
            - match: "*.dagrun.*.first_task_scheduling_delay"
              match_metric_type: observer
              name: "airflow_dagrun_first_task_scheduling_delay"
              labels:
                airflow_id: "$1"
                dag_id: "$2"
    logs:
      configs:
      - clients:
        - basic_auth:
            password: ***********************************************************************************************************************************************************************tZWFzdC0wIn19
            username: 792480
          url: https://logs-prod-006.grafana.net/loki/api/v1/push
        name: integrations
        positions:
          filename: /tmp/positions.yaml
        scrape_configs:
        - job_name: integrations/apache-airflow
          static_configs:
          - targets: [localhost]
            labels:
              job: integrations/apache-airflow-atob
              instance: 'jg-tech1prod-airflow'
              __path__: '/logs/dag_id=A_to_B*/run_id=*/task_id=*/attempt=*.log'
          - targets: [localhost]
            labels:
              job: integrations/apache-airflow-jgetl
              instance: 'jg-tech1prod-airflow'
              __path__: '/logs/dag_id=jg*/run_id=*/task_id=*/attempt=*.log'
          - targets: [localhost]
            labels:
              job: integrations/apache-airflow-positions
              instance: 'jg-tech1prod-airflow'
              __path__: '/logs/dag_id=positions*/run_id=*/task_id=*/attempt=*.log'
          - targets: [localhost]
            labels:
              job: integrations/apache-airflow
              instance: 'jg-tech1prod-airflow'
              __path__: '/logs/scheduler/latest/*.py.log'
          pipeline_stages:
          - match:
              selector: '{job="integrations/apache-airflow-atob",instance="jg-tech1prod-airflow"}'
              stages:
              - regex:
                  source: filename
                  expression: '/logs/dag_id=A_to_B*/run_id=(?P<run_id>\\S+?)/task_id=(?P<task_id>\\S+?)/attempt=*.log'
              - labels:
                  run_id:
                  task_id:
          - match:
              selector: '{job="integrations/apache-airflow-jgetl",instance="jg-tech1prod-airflow"}'
              stages:
              - regex:
                  source: filename
                  expression: '/logs/dag_id=jg*/run_id=(?P<run_id>\\S+?)/task_id=(?P<task_id>\\S+?)/attempt=*.log'
              - labels:
                  run_id:
                  task_id:
          - match:
              selector: '{job="integrations/apache-airflow-positions",instance="jg-tech1prod-airflow"}'
              stages:
              - regex:
                  source: filename
                  expression: '/logs/dag_id=positions*/run_id=(?P<run_id>\\S+?)/task_id=(?P<task_id>\\S+?)/attempt=*.log'
              - labels:
                  run_id:
                  task_id:
          - match:
              selector: '{job="integrations/apache-airflow",instance="jg-tech1prod-airflow"}'
              stages:
              - regex:
                  source: filename
                  expression: "/logs/scheduler/latest/(?P<dag_file>\\S+?)\\.log"
              - labels:
                  dag_file:   
          - multiline:
              # match on timestamp. Format should be like '[2023-05-09T11:58:53.205+0000]'
              firstline: '\[\d+-\d+-\d+T\d+:\d+:\d+\.\d+\+\d+\]'
    metrics:
      configs:
      - name: integrations
        remote_write:
        - basic_auth:
            password: ${GRAFANA_PROM_PASSWORD}
            username: ${GRAFANA_PROM_ACCOUNT_ID}
          url: https://prometheus-prod-13-prod-us-east-0.grafana.net/api/prom/push
        scrape_configs:
          # Add here any snippet that belongs to the `metrics.configs.scrape_configs` section.
          # For a correct indentation, paste snippets copied from Grafana Cloud at the beginning of the line.
      global:
        scrape_interval: 60s
      wal_directory: /tmp/grafana-agent-wal
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana-agent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana-agent
  template:
    metadata:
      labels:
        app: grafana-agent
    spec:
      containers:
      - name: grafana-agent
        image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-grafanaagent:latest
        env:
        - name: GCLOUD_HOSTED_METRICS_URL
          value: "https://prometheus-prod-13-prod-us-east-0.grafana.net/api/prom/push"
        - name: GCLOUD_HOSTED_METRICS_ID
          value: "${GRAFANA_PROM_ACCOUNT_ID}"
        - name: GCLOUD_SCRAPE_INTERVAL
          value: "60s"
        - name: GCLOUD_HOSTED_LOGS_URL
          value: "https://logs-prod-006.grafana.net/loki/api/v1/push"
        - name: GCLOUD_HOSTED_LOGS_ID
          value: "${GRAFANA_LOKI_ACCOUNT_ID}"
        - name: GCLOUD_RW_API_KEY
          value: "${GRAFANA_LOKI_PASSWORD}"
        - name: STATSD_EXPORTER_ENABLED
          value: "true"
        - name: STATSD_EXPORTER_HOST
          value: "0.0.0.0"  # Listen all available network
        - name: STATSD_EXPORTER_PORT
          value: "8125"
        ports:
        - containerPort: 8125
          protocol: UDP
        volumeMounts:
          - name: config
            mountPath: /etc/agent
            readOnly: false
          - name: grafana-agent-data
            mountPath: /etc/agent/data
          - name: airflow-logs
            mountPath: /logs
      volumes:
        - name: config
          configMap:
            name: grafana-agent-config
        - name: grafana-agent-data
          persistentVolumeClaim:
            claimName: grafana-agent-pvc
        - name: airflow-logs
          persistentVolumeClaim:
            claimName: common-logs-airflow
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-agent
spec:
  selector:
    app: grafana-agent
  ports:
  - protocol: TCP
    port: 80
    targetPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: grafana-agent-statsd
spec:
  selector:
    app: grafana-agent
  ports:
  - protocol: UDP
    port: 8125
    targetPort: 8125

---

apiVersion: batch/v1
kind: Job
metadata:
  name: grafana-cloud-job
spec:
  template:
    metadata:
      name: grafana-cloud-job
    spec:
      containers:
      - name: grafana-cloud-job
        image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/alpine:latest
        command:
        - /bin/sh
        - -c
        - |
          ARCH="amd64" GCLOUD_HOSTED_METRICS_URL="https://prometheus-prod-13-prod-us-east-0.grafana.net/api/prom/push" GCLOUD_HOSTED_METRICS_ID="${GRAFANA_PROM_ACCOUNT_ID}" GCLOUD_SCRAPE_INTERVAL="60s" GCLOUD_HOSTED_LOGS_URL="https://logs-prod-006.grafana.net/loki/api/v1/push" GCLOUD_HOSTED_LOGS_ID="${GRAFANA_LOKI_ACCOUNT_ID}" GCLOUD_RW_API_KEY="${GRAFANA_LOKI_PASSWORD}" /bin/sh -c "$(curl -fsSL https://storage.googleapis.com/cloud-onboarding/agent/scripts/static/install-linux.sh)"
      restartPolicy: Never
