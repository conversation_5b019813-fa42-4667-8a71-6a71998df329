from data_analyzer.data_catalog.data_catalog_data_analyzer  import DataCatalogDataAnaltyzer

import sys
from icecream import ic

def main():
    api_key = "***************************************************"
    input_datapath = "data/data_catalog.json"
    output_datapath = "data/data_catalog_with_embeddings_1k"

    data_catalog_analyzer = DataCatalogDataAnaltyzer(api_key, input_datapath, output_datapath, debug=True)
    
    try:
        data_catalog_analyzer.check_data_availability()
        data_catalog_analyzer.load_or_create_embeddings()
        results = data_catalog_analyzer.search_embeddings('DataSet1')
        ic(results)
        # tsne_plot_json = data_catalog_analyzer.visualize_embeddings_with_tsne()
        # data_catalog_analyzer.perform_regression()
        # data_catalog_analyzer.classify_and_evaluate()
        # data_catalog_analyzer.plot_precision_recall()
        # clustering_results = data_catalog_analyzer.find_clusters(n_clusters=4)
        # cluster_summary = data_catalog_analyzer.name_and_sample_clusters(n_samples=5)
        # data_catalog_analyzer.start_server()
    except FileNotFoundError as e:
        print(e)

if __name__ == "__main__":
    main()
