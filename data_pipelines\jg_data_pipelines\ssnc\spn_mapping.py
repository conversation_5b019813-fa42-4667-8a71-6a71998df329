import pandas as pd
import re
import logging
import os, sys, io, json, csv
from datetime import datetime
from paramiko import Transport, SFTPClient, RSAKey
import fnmatch
import json
from io import StringIO, BytesIO
sys.path.append(os.getcwd())
current_file_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(os.path.abspath(os.path.join(current_file_dir, '../../../source/bin')))
from strunner import *
setupEnvironment()
DATA_PIPELINE_PATH = os.environ.get("DATA_PIPELINE_PATH")
sys.path.append(DATA_PIPELINE_PATH)
from utils.snowflake.adaptor import SnowflakeAdaptor

from loguru import logger as log

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SFTPDataLoader:
    def __init__(self, jfs_base_directory, pattern, objconfig):
        self.pattern = pattern
        self.jfs_base_directory = jfs_base_directory
        self.config=objconfig

    def extract_date_from_filename(self, file):
        parts = file.split("_")
        file_date = parts[-1].split('.')[0]

        month = file_date[:2]
        day = file_date[2:4]
        year = file_date[4:]

        formatted_file_date = f"{year}-{month}-{day}"
        
        return formatted_file_date
    
    def connect_sftp(self):
        private_key_path = default_password = f'{jg_config_path()}/pem/id_rsa_arcesium'
        
        # Load the private key
        private_key = RSAKey.from_private_key_file(private_key_path)
        
        transport = Transport((self.config['flex_sod_arcesium_sftp_server'], int(self.config['flex_sod_arcesium_sftp_port'])))
        transport.connect(username=self.config['flex_sod_arcesium_sftp_user'], pkey=private_key)
        sftp = SFTPClient.from_transport(transport)
        return sftp, transport

    def get_latest_file(self, sftp):
        files = sftp.listdir(self.config['flex_sod_sftp_directory'])
        filtered_files = fnmatch.filter(files, self.pattern)
        latest_file = None
        latest_date = None
        file_date = None
        for file in filtered_files:
            file_date = self.extract_date_from_filename(file)
            if file_date and (latest_date is None or file_date > latest_date):
                latest_date = file_date
                latest_file = file

        if latest_file is None:
            raise ValueError("No valid files found in the directory.")
        
        logger.info(f"File Found: {latest_file}")
        return latest_file, latest_date

    def download_file(self, sftp, file_path):
        bio = BytesIO()
        sftp.getfo(file_path, bio)
        sftp.close()
        bio.seek(0)
        logger.info(f"File '{file_path}' downloaded from SFTP location and is to be moved to {self.jfs_base_directory}")
        return bio

    def load_to_dataframe(self, bio, latest_file, latest_date):
        df = pd.read_csv(bio)
        df.columns = df.columns.str.upper()
        df.rename( columns={'UNNAMED: 0':'TYPE'}, inplace=True )
        if not os.path.exists(self.jfs_base_directory):
            os.makedirs(self.jfs_base_directory)
        file_with_path = os.path.join(self.jfs_base_directory, latest_file)
        df.to_csv(file_with_path, index=False)
        logger.info(f"File downloaded and loaded to SS&C rawdata location {self.jfs_base_directory}")
        
        return df

    def run(self):
        sftp, transport = self.connect_sftp()
        latest_file, latest_date = self.get_latest_file(sftp)
        file_path = os.path.join(self.config['flex_sod_sftp_directory'], latest_file)
        bio = self.download_file(sftp, file_path)
        transport.close()

        df = self.load_to_dataframe(bio, latest_file, latest_date)
        return latest_file


def process_data(file_path):
    expect_currency = False
    capture_data=True
    data = []
    temp_data=[]
    columns=[]
    
    with open(file_path,'r') as file:
        lines = file.readlines()
        for line in lines:
            line = line.strip()
            if line.startswith('GlobeOp Profit and Loss (Detailed) with Amortization'):
                if temp_data:
                    data.extend(temp_data)
                temp_data = []
                capture_data=False
                continue
                
            elif line.startswith('Accounting Calendar'):
                expect_currency=True
                continue
    
            elif expect_currency and line.strip():
                expect_currency=False
                capture_data=True
                continue
            
            if capture_data and not columns:
                columns = line.split(',')
                
            elif capture_data and line and not line.startswith('GlobeOp Profit and Loss (Detailed) with Amortization'):
                if columns:
                    row = next(csv.reader([str(line)], quotechar='"', skipinitialspace=True))
                    
                    columns[0] = "TYPE"
                    if 'Ending Quantity' not in row:
                        temp_data.append({
                            **{columns[i].replace(' ', '_').replace('/', '_').replace('"', '').split('.')[0]: row[i].replace('"', '').replace(',', '').strip() for i in range(len(columns)) if i != 1}
                        })
    
        if temp_data:
            data.extend(temp_data)
        
        data_df = pd.DataFrame(data)
        
        return data_df         

if __name__ == "__main__":
   
    configPath = os.environ.get('CONFIG_PATH', os.getcwd())
    with open(f'{configPath}/config.json', 'r') as f:
        config = json.load(f)

    def jg_config_path():
        return config["JG_CONFIG_PATH"]
    
    def jg_rawdata_path():
        return config["JG_DATA_PATH"]

    def read_config_secrets():
        config_secret_path = os.path.join(jg_config_path(), 'config_secret.json')
        with open(config_secret_path, 'r') as f:
            config_secret = json.load(f)
        return config_secret
    objconfig = read_config_secrets()
    
    os.environ['SF_USERNAME'] = objconfig['sf_user']
    os.environ['SF_PASSWORD'] = objconfig['sf_password']
    os.environ['SF_DATABASE'] = objconfig['ssnc_database']
    os.environ['SF_WAREHOUSE'] = objconfig['ssnc_warehouse']
    os.environ['SF_ROLE'] = objconfig['ssnc_role']
    os.environ['SF_SCHEMA'] = schema_name = objconfig['ssnc_schema']
    
    table_name = f'{schema_name}.SPN_MAPPING'
    stage_name = f'{schema_name}.STG_SPN_MAPPING'
    
    jfs_rawdata_location = f'{jg_rawdata_path()}/SSandC/SPN_Mapping'
    
    try:    
        # Download latest SPM_Mapping file from SFTP
        loader = SFTPDataLoader(jfs_rawdata_location, 'PNL_Detailed_MTD_*.csv', objconfig)
        latest_file = loader.run()
        # Processing raw file
        data_df = process_data(f'{jfs_rawdata_location}/{latest_file}')
        # Loading processed data to the table
        sf_adaptor = SnowflakeAdaptor(database=objconfig['ssnc_database'], warehouse=objconfig['ssnc_warehouse'], role=objconfig['ssnc_role'])
        sf_adaptor.execute_query(schema_name, f'TRUNCATE {table_name}')
        sf_adaptor.load_df(data_df, latest_file, schema_name, table_name, stage_name, True)
        logger.info('SPN_Mapping data is successfully loaded to the table')
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        raise