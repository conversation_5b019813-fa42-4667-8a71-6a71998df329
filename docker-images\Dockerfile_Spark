FROM bitnami/spark:3.5.0
ARG USERNAME
ARG LOGIN_KEY
ENV USERNAME=$USERNAME
ENV LOGIN_KEY=$LOGIN_KEY
USER root

RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    wget \
    curl \
    vim \
    traceroute \
    inetutils-tools \
    openjdk-17-jdk \
    fuse3 \
    findutils \
    alien \
 && rm -rf /var/lib/apt/lists/*

ENV JAVA_HOME /usr/lib/jvm/java-17-openjdk-amd64
ENV PATH $PATH:$JAVA_HOME/bin

RUN pip3 install apache-airflow[kubernetes] apache-airflow-providers-apache-spark

COPY docker-images/requirements_Spark.txt /usr/local/spark/app/requirements_Spark.txt
RUN pip install -r /usr/local/spark/app/requirements_Spark.txt

RUN mkdir -p /home/<USER>/spark/logs \
    /home/<USER>/spark/app/jars \
 && chown -R 1001:0 /home/<USER>/spark/logs \
 && chown -R 1001:0 /home/<USER>/spark/app/jars

RUN curl -L -o /home/<USER>/spark/app/jars/iceberg-spark-extensions.jar https://repo1.maven.org/maven2/org/apache/iceberg/iceberg-spark-extensions-3.5_2.12/1.4.2/iceberg-spark-extensions-3.5_2.12-1.4.2.jar
RUN curl -L -o /home/<USER>/spark/app/jars/iceberg-spark-runtime.jar https://repo1.maven.org/maven2/org/apache/iceberg/iceberg-spark-runtime-3.5_2.12/1.4.2/iceberg-spark-runtime-3.5_2.12-1.4.2.jar

ENV SPARK_DIST_CLASSPATH $SPARK_DIST_CLASSPATH:/home/<USER>/spark/app/jars/iceberg-spark-extensions.jar:/home/<USER>/spark/app/jars/iceberg-spark-runtime.jar
ENV JAVA_OPTS "$JAVA_OPTS -Djava.library.path=/usr/local/spark/jars"

RUN chmod -R 777 /home/<USER>/spark

COPY /docker-images/cwiqfs/fuse-libs-2.9.2-11.el7.x86_64.rpm  /tmp
COPY /docker-images/cwiqfs/fuse-sshfs-2.10-1.el7.x86_64.rpm /tmp

COPY docker-images/cwiqfs/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm /tmp
RUN  alien -i /tmp/cwiqfs.client-2.2.39_JG-1.stable.x86_64.rpm
RUN ln -sf /etc/ssl/certs/ca-bundle.crt /etc/ssl/certs/ca-certificates.crt
COPY docker-images/cwiqfs/cwiqfs.yaml /etc/cwiq/cwiqfs/config_temp.yaml
RUN envsubst '${USERNAME},${LOGIN_KEY}' < /etc/cwiq/cwiqfs/config_temp.yaml > /etc/cwiq/cwiqfs/config.yaml
RUN sed -i -e's/# user_allow_other/user_allow_other/g' /etc/fuse.conf
RUN mkdir /jfs
RUN useradd jsvc-tech1
ENV USER="jsvc-datait"
ENV JGDATA_PATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/"
ENV STCOMMON_PATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/stcommon"
ENV RAWSTORE_ROOT="/jfs/tech1/apps/rawdata/"
ENV BUILDINPARALLEL=False
ENV nohup python3 dags/main_write_cmg.py 2>&1 > runcmg.out &
ENV SPARK_DIST_CLASSPATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source/jars/iceberg-spark-runtime.jar"
ENV CONFIG_PATH="/jfs/tech1/apps/datait/jg-code/prod/JG-DATA-PLATFORM/source"

COPY docker-images/docker_conf/spark/run-spark.sh /opt/run-spark.sh
RUN chmod +x /opt/run-spark.sh 
WORKDIR /home/<USER>/spark
RUN useradd -u 1001 -m -s /bin/bash sparkuser
RUN mkdir -p /var/tmp/cache /var/log/cwiq && chmod 777 /var/tmp/cache
RUN chown -R sparkuser:sparkuser /var/tmp/cache /var/log/cwiq
ENV BITNAMI_APP_NAME="spark" 
ENTRYPOINT ["/opt/run-spark.sh"]

