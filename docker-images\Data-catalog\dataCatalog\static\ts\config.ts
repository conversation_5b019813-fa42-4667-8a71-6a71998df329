class Config {
    private static config: { [key: string]: any } = {
      msgPerSecInterval: 10000,  // assuming it's in milliseconds
      succesfullStatusMessageDuration: 5000,  // assuming it's in milliseconds
      errorStatusMessageDuration: 20000,  // assuming it's in milliseconds
      uiText: {
        streamPriceButtonStart: "Start streaming prices",
        streamPriceButtonStop: "Stop streaming prices",
        // Other UI texts can be added here
      },
    };
  
    // Use a generic method with a type assertion
    public static get<T>(key: string): T {
      return this.config[key] as T;
    }

    public static getUI(key: string): string {
        return this.config.uiText[key];
      }
  }
  