from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType
from bloomberg.utils import get_bbg_full_ticker_for_futures
from utils.snowflake.adaptor import SnowflakeAdaptor
from utils.snowflake.snowpark_adaptor import SnowparkAdaptor

from utils.date_utils import get_now

import pandas as pd


if __name__ == "__main__":
    sf_adaptor = SnowflakeAdaptor(
        database="BLOOMBERG", warehouse="BLOOMBERG_HUB_WH", role="DR_BBGH_OWNER"
    )

    df_futures = sf_adaptor.read_data(
        "BBGH_FUTURES",
        """WITH CTE_FUTURE_ENTITY AS
            (select bbg_e.ID_BPL_ENTITY,
                frc.ID_BB_GLOBAL_COMPANY,
                frc.FUTURE_ROOT,
                frc.JG_FUT_CATEGORY
            from BBG_DLPLUS.CORE.ENTITY bbg_e join
            FUTURE_SERIES frc on bbg_e.ID_BB_GLOBAL_COMPANY = frc.ID_BB_GLOBAL_COMPANY
            where frc.ENABLE_BULK = TRUE)
            select bbg_e.JG_FUT_CATEGORY,
                bbg_e.FUTURE_ROOT,
                bbg_e.ID_BB_GLOBAL_COMPANY,
                bbg_i.TICKER AS BBG_TICKER,
                bbg_i.ID_BB_GLOBAL,
                bbg_i.FUT_CONTRACT_DT,
                bbg_i.FUT_FIRST_TRADE_DT,
                bbg_i.LAST_TRADEABLE_DT,
                bbg_i.FUT_NOTICE_FIRST,
                bbg_i.FUT_DLV_DT_FIRST,
                bbg_i.FUT_DLV_DT_LAST,
                bbg_i.QUOTE_UNITS,
                bbg_m.QUOTED_CRNCY,
                bbg_i.FUT_CONT_SIZE,
                bbg_i.FUT_TICK_VAL,
                bbg_i.FUT_VAL_PT,
                bbg_i.FUT_TICK_SIZE,
                bbg_m.CDR_SETTLE_CODE,
                bbg_m.ID_MIC_PRIM_EXCH
            from BBG_DLPLUS.CORE.INSTRUMENT bbg_i join
            BBG_DLPLUS.CORE.MARKET bbg_m ON bbg_m.ID_BPL_INSTRUMENT = bbg_i.ID_BPL_INSTRUMENT join
            CTE_FUTURE_ENTITY bbg_e on bbg_i.ID_BPL_ENTITY = bbg_e.ID_BPL_ENTITY
            where bbg_m.BPL_ACTIVE = TRUE and 
            bbg_m.FUT_PX_SESSION IN ('S', 'C') and 
            bbg_i.fut_contract_dt IS NOT NULL and 
            bbg_i.last_tradeable_dt >= CURRENT_DATE;
    """)
    
    if df_futures['FUT_CONTRACT_DT'].isnull().any():
        raise ValueError("There are rows with FUT_CONTRACT_DT as null")
    
    df_futures['FUT_CONTRACT_DT'] = pd.to_datetime(df_futures['FUT_CONTRACT_DT'], format='%m/%Y')
    df_futures["BBG_FULL_TICKER"] = df_futures.apply(lambda x: get_bbg_full_ticker_for_futures(x["FUTURE_ROOT"], x["FUT_CONTRACT_DT"].month, x["FUT_CONTRACT_DT"].year), axis=1)

    df_futures["FUT_CONT_SIZE"] = pd.to_numeric(df_futures["FUT_CONT_SIZE"], errors="coerce")
    df_futures["FUT_TICK_VAL"] = pd.to_numeric(df_futures["FUT_TICK_VAL"], errors="coerce")
    df_futures["FUT_VAL_PT"] = pd.to_numeric(df_futures["FUT_VAL_PT"], errors="coerce")
    df_futures["FUT_TICK_SIZE"] = pd.to_numeric(df_futures["FUT_TICK_SIZE"], errors="coerce")

    df_futures["UPDATE_SOURCE"] = 1
    df_futures["LAST_UPDATED"] = get_now()
    df_futures["UPDATED_BY"] = "bbg_dlplus_futures_ref.py"

    df_futures.rename(columns={"BBG_TICKER": "BBG_ORIG_TICKER"}, inplace=True)    
    df_futures = df_futures[["ID_BB_GLOBAL", "BBG_FULL_TICKER", "ID_BB_GLOBAL_COMPANY", "FUT_CONTRACT_DT", "FUT_FIRST_TRADE_DT", "LAST_TRADEABLE_DT", "FUT_NOTICE_FIRST", "FUT_DLV_DT_FIRST", "FUT_DLV_DT_LAST", "QUOTE_UNITS", "QUOTED_CRNCY", "FUT_CONT_SIZE", "FUT_TICK_VAL", "FUT_VAL_PT", "FUT_TICK_SIZE", "CDR_SETTLE_CODE", "ID_MIC_PRIM_EXCH", "BBG_ORIG_TICKER", "UPDATE_SOURCE", "LAST_UPDATED", "UPDATED_BY"]]

    df_futures.drop_duplicates(subset=["ID_BB_GLOBAL"], keep="last", inplace=True)

    adaptor = SnowparkAdaptor(
        database="BLOOMBERG", 
        schema="BBGH_FUTURES",
        warehouse="BLOOMBERG_HUB_WH", 
        role="DR_BBGH_OWNER"
    )

    ret_val = adaptor.upsert(df_futures, "FUTURE_REF_DL", ["ID_BB_GLOBAL"], only_insert=True)
    print(ret_val)