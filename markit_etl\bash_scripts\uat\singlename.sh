#!/bin/bash

# Define variables
SCRIPT_NAME="main.py"
ENVIRONMENT="uat"
LOG_FILE="logs/singlename_process.log"
MODULE="singlename"

# Ensure logs directory exists
if [ ! -d "logs" ]; then
    mkdir -p logs
    echo "Created logs directory"
fi

# Function to start the process
start_process() {
    echo "Starting single name processing..."
    python3 $SCRIPT_NAME $ENVIRONMENT $MODULE >> $LOG_FILE 2>&1 &
    echo $! > logs/singlename_process.pid
    echo "Process started with PID $(cat logs/singlename_process.pid)"
}

# Function to stop the process
stop_process() {
    if [ -f logs/singlename_process.pid ]; then
        PID=$(cat logs/singlename_process.pid)
        echo "Stopping process with PID $PID..."
        kill $PID
        rm logs/singlename_process.pid
        echo "Process stopped."
    else
        echo "No process found to stop."
    fi
}

# Function to check the status of the process
status_process() {
    if [ -f logs/singlename_process.pid ]; then
        PID=$(cat logs/singlename_process.pid)
        if ps -p $PID > /dev/null; then
            echo "Process is running with PID $PID."
        else
            echo "Process is not running, but PID file exists."
        fi
    else
        echo "No process found."
    fi
}

# Main script logic
case "$1" in
    start)
        start_process
        ;;
    stop)
        stop_process
        ;;
    status)
        status_process
        ;;
    *)
        echo "Usage: $0 {start|stop|status}"
        exit 1
        ;;
esac
