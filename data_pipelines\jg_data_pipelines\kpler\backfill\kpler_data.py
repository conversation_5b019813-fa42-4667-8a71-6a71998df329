import logging
import polars as pl
import pandas as pd
from kpler.kpler_parser import _KPLER_AVAILABILITY_UNITS_PROCESSED, flatten_json
from pathlib import Path

import pyarrow.parquet as pq

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def sample_json_file(json_file):
    all_dfs = []
    list_dict = flatten_json(json_file)
    print(len(list_dict))
    
    for el_data in list_dict:
        all_dfs.append(pd.DataFrame(el_data))
    
    df = pd.concat(all_dfs, ignore_index=True)
    print(df.head())
    print(df.dtypes)

    for col in ["as_of", "provider", "country", "fuel_type", "timezone", "unit", "unit_name", "production_code", "asset_type", "json_file"]:
        df_grouped = df.groupby(col).size().reset_index(name="count")
        print(df_grouped.head())

def read_parquet_file(parquet_file):
    df = pd.read_parquet(parquet_file)
    print(df.dtypes)
    print(df.shape)
    
    for col in ["as_of", "provider", "country", "fuel_type", "timezone", "unit", "unit_name", "production_code", "asset_type"]:
        df_grouped = df.groupby(col).size().reset_index(name="count")
        print(df_grouped.head())

def get_stats_by_country_asset_type_provider(parquet_file):
    df = pd.read_parquet(parquet_file)
    df_grouped = df.groupby(["country", "asset_type", "provider"]).size().reset_index(name="count")
    return df_grouped

def get_data_by_country_asset_type_provider(parquet_file, country, asset_type, provider):
    df = pd.read_parquet(parquet_file)
    df_filtered = df[(df["country"] == country) & (df["asset_type"] == asset_type) & (df["provider"] == provider)]
    return df_filtered

def get_data_by_unit(parquet_file, unit):
    lazy_df = pl.scan_parquet(parquet_file)
    filtered_df = lazy_df.filter(pl.col("unit") == unit).select(["timestamp", "availability_amount", "json_file"])
    result_df = filtered_df.collect()
    df_filtered = result_df.to_pandas()
    return df_filtered

def get_metadata_by_unit(parquet_file, unit):
    lazy_df = pl.scan_parquet(parquet_file)
    filtered_df = lazy_df.filter(pl.col("unit") == unit).select(["unit_name", "country", "provider", "asset_type", "fuel_type", "timezone"])
    result_df = filtered_df.collect()
    df_filtered = result_df.to_pandas()
    return df_filtered


if __name__ == "__main__":
    # read_parquet_file(f"{_KPLER_AVAILABILITY_UNITS_PROCESSED}2025-01-15T10:00:00+00:00.parquet")
    # sample_json_file("2025-02-15T10\:00\:00+00\:00_BE_nuclear_2025-01.json")
    
    # df = get_data_by_unit(f"{_KPLER_AVAILABILITY_UNITS_PROCESSED}2025-01-15T10:00:00+00:00.parquet", "11W0-0000-0245-J")
    # print(df.head())
    # print(df.shape)
    # unique_ts = df["json_file"].unique()
    # print(len(unique_ts))
    # print(unique_ts)

    # df = get_metadata_by_unit(f"{_KPLER_AVAILABILITY_UNITS_PROCESSED}2025-01-15T10:00:00+00:00.parquet", "11W0-0000-0245-J")
    # print(df.head())

    # Loop through all parquet files and get row counts
    
    # parquet_dir = _KPLER_AVAILABILITY_UNITS_PROCESSED
    # total_rows = 0
    # chunk_size = 1000000
    
    # for parquet_file in Path(parquet_dir).glob('*.parquet'):
    #     rows = 0
    #     df = pq.ParquetFile(parquet_file)
    #     for batch in df.iter_batches(batch_size=chunk_size):
    #         rows += batch.num_rows
    #     total_rows += rows
    #     logger.info(f"{parquet_file.name}: {rows:,}")
    
    # logger.info(f"Total rows: {total_rows:,}")
    file_path = "/jfs/tech1/apps/rawdata/kpler_power/availability_fueltype_yearly_processed/"
    for parquet_file in Path(file_path).glob('*.parquet'):
        print(parquet_file)
        df = pd.read_parquet(parquet_file)
        df_grouped = df.groupby(["level"]).size().reset_index(name="count")
        print(df_grouped)
        print(df.shape)
        
    