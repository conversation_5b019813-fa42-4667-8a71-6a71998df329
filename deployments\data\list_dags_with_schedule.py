from airflow.models import DagBag

dag_bag = DagBag()
dags = dag_bag.dags

print(f"{'DAG ID':<40} {'Schedule':<30}")
print("="*70)
for dag_id, dag in dags.items():
    schedule_interval = dag.schedule_interval
    if schedule_interval is None:
        schedule_interval = "None"
    elif hasattr(schedule_interval, 'summary'):
        schedule_interval = schedule_interval.summary()  # For cron schedules
    print(f"{dag_id:<40} {schedule_interval:<30}")
