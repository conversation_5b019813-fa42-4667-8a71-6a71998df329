import pytz
from pandas.tseries.offsets import BDay
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
import re

def get_n_bday(n_days, tz_str='America/New_York'):
    past_date = get_today(tz_str) - BDay(n_days)
    return past_date

def get_n_bday_yyyymmdd(n_days, tz_str='America/New_York'):
    return get_n_bday(n_days, tz_str).strftime('%Y%m%d')


def get_today(tz_str='America/New_York'):
    return get_now(tz_str).date()


def get_now(tz_str='America/New_York'):
    nyc_tz = pytz.timezone(tz_str)
    current_date_time = datetime.now(nyc_tz).replace(tzinfo=None)
    return current_date_time


def resolve_relative_date(pattern: str, reference_date=None, tz_str='America/New_York'):
    if reference_date is None:
        reference_date = get_today(tz_str)

    match = re.match(r"T([+-])(\d+)([DY])", pattern)
    if not match:
        raise ValueError(f"Invalid date pattern: {pattern}")

    sign, value, unit = match.groups()
    value = int(value)
    
    if sign == "-":
        value = -value

    if unit == "D":
        return reference_date + timedelta(days=value)
    elif unit == "Y":
        return reference_date + relativedelta(years=value)
    else:
        raise ValueError(f"Unsupported unit: {unit}")


def get_current_hour(tz_str='America/New_York'):
    current_time = get_now(tz_str)
    return current_time.replace(minute=0, second=0, microsecond=0)

if __name__ == "__main__":
    # print(get_today())
    # print(get_now())
    print(resolve_relative_date("T+1Y"))
    print(get_current_hour())
    print(get_now('UTC'))