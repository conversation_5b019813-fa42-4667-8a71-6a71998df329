[core]
airflow_home = /home/<USER>
dags_folder = /jfs/tech1/apps/datait/source/dags/etl
base_log_folder = /home/<USER>/logs
remote_base_log_folder =
remote_log_conn_id =
encrypt_s3_logs = False
executor = LocalExecutor
sql_alchemy_conn = postgresql+psycopg2://#AIRFLOW_USERNAME#:#AIRFLOW_PASSWORD#@service-postgres:5432/airflow
sql_alchemy_pool_size = 5
sql_alchemy_pool_recycle = 3600
parallelism = 32
dag_concurrency = 16
dags_are_paused_at_creation = True
non_pooled_task_slot_count = 128
max_active_runs_per_dag = 16
load_examples = False
plugins_folder = /usr/local/airflow/plugins
fernet_key = hMjFQ7oRxq0IBDnbnUbm5fWv4j_70jK2ZvxpdbXYO40=
donot_pickle = False
dagbag_import_timeout = 30


[operators]
default_owner = Airflow

[webserver]
base_url = http://localhost:8080
web_server_host = 0.0.0.0
web_server_port = 8080
web_server_worker_timeout = 1200
secret_key = a932a01d109494bba2891046387897c1cb58ee86c55f69645b8f750896a0
workers = 4
worker_class = sync
expose_config = false
authenticate = True
auth_backend = airflow.contrib.auth.backends.ldap_auth
filter_by_owner = False
warn_deployment_exposure=False

[email]
email_backend = airflow.utils.email.send_email_smtp

[smtp]
smtp_host = [SMTP host i.e. smtp.gmail.com]
smtp_starttls = True
smtp_ssl = False
smtp_user = [some e-mail]
smtp_port = [port i.e. 587]
smtp_password = [e-mail password]
smtp_mail_from = <EMAIL>

[celery]
celery_app_name = airflow.executors.celery_executor
celeryd_concurrency = 16
worker_log_server_port = 8793
broker_url = None
celery_result_backend = None
flower_port = 5555
default_queue = default

[scheduler]
job_heartbeat_sec = 5
scheduler_heartbeat_sec = 5
max_threads = 2

[mesos]
master = localhost:5050
framework_name = Airflow
task_cpu = 1
task_memory = 256
checkpoint = False
authenticate = False

[spark]
spark_master = spark://spark:7077
spark_home = /usr/local/spark
spark_submit_deploy_mode = client
spark_submit_bin = /usr/local/spark/bin/spark-submit

[metrics]
statsd_on = True
statsd_host = localhost
statsd_port = 8125
statsd_prefix = airflow

[ldap]
uri = ldap://***********:389
bind_user = #BIND#@jainglobal.local
bind_password = insecure

#[oauth]
#provider_key = google
#redirect_uri = http://a75dcc9bb29af4686a607e39041e91d1-**********.us-east-2.elb.amazonaws.com:8080
#client_id = 1007745115948-ievcvhhltq9a9rgqae5s0rlkpi8d520q.apps.googleusercontent.com
#client_secret = 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

#[lineage]
#backend = airflow_provider_openmetadata.lineage.backend.OpenMetadataLineageBackend
#airflow_service_name = service-airflow
#openmetadata_api_endpoint = http://internal-k8s-default-openmeta-e85dbd1e9b-316426303.us-east-2.elb.amazonaws.com/api
#jwt_token = "eyJraWQiOiJHYjM4OWEtOWY3Ni1nZGpzLWE5MmotMDI0MmJrOTQzNTYiLCJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.********************************************************************************************************************************************.tS8um_5DKu7HgzGBzS1VTA5uUjKWOCU0B_j08WXBiEC0mr0zNREkqVfwFDD-d24HlNEbrqioLsBuFRiwIWKc1m_ZlVQbG7P36RUxhuv2vbSp80FKyNM-Tj93FDzq91jsyNmsQhyNv_fNr3TXfzzSPjHt8Go0FMMP66weoKMgW2PbXlhVKwEuXUHyakLLzewm9UMeQaEiRzhiTMU3UkLXcKbYEJJvfNFcLwSl9W8JCO_l0Yj3ud-qt_nQYEZwqW6u5nfdQllN133iikV4fM5QZsMCnm8Rq1mvLR0y9bmJiD7fwM1tmJ791TUWqmKaTnP49U493VanKpUAfzIiOiIbhg"
