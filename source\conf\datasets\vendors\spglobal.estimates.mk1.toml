dataset = 'spglobal.estimates'
catalog = 'lake'
version = 'mk1'
description = 'spglobal estimates'
scope = ['gl']
tables = [
  'estimateAccountingStandard',
  'estimateAnalyst',
  'estimateBroker',
  'estimateChain',
  'estimateConsensus',
  'estimatePeriod',
  'estimatePeriodRelativeConstant',
  'estimatePeriodType',
  'estimatePrimaryAccountingStandard',
  'estimatePrimaryEarningsMetric',
  'estimateScaleType',
  'estimateConsensusNote',
  'estimateConsensusNumericData',
  'estimateCoverage',
  'estimateGuidanceData',
  'estimateBrokerRecommendationScale',
  'estimateDetail',
  'estimateDetailNote',
  'estimateDetailTimeline',
  'estimateExpirationType',
  'estimateOperationType',
  'estimateDailyRevision',
  'estimateRevisionsType',
  'dataItem',
  'dataItemEstimate',
  'dataItemEstimateRel',
  'dataItemEstimateRelType',
  'dataItemToCompustatMnemonic',
  'estimateConsensusAnalysis',
  'consensus'
  ]