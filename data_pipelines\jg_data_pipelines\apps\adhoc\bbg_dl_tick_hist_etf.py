import os
from datetime import datetime, date
import pandas as pd
import calendar

from bloomberg.per_security.request_runner import PerSecurityRequestRunner
from bloomberg.per_security.request_builder import PerSecurityRequestType

from utils.postgres.adaptor import PostgresAdaptor

def create_monthly_calendar_df(start_date, end_date):
    """
    Create a DataFrame with first and last day of each month between start_date and end_date.
    Last day will be capped at current date for future months.
    
    Parameters:
    start_date: datetime or str - Start date (will include the full month)
    end_date: datetime or str - End date (will include the full month)
    
    Returns:
    pandas.DataFrame with columns 'year', 'month', 'first_day', and 'last_day'
    """
    # Convert to datetime if strings are provided
    if isinstance(start_date, str):
        start_date = pd.to_datetime(start_date)
    if isinstance(end_date, str):
        end_date = pd.to_datetime(end_date)
    
    # Get today's date for comparison
    today = datetime.now().date()
    
    # Get the first day of the start month and last day of the end month
    start_year, start_month = start_date.year, start_date.month
    end_year, end_month = end_date.year, end_date.month
    
    years = []
    months = []
    first_days = []
    last_days = []
    
    current_year, current_month = start_year, start_month
    
    while (current_year, current_month) <= (end_year, end_month):
        # Get first day of month
        first_day = date(current_year, current_month, 1)
        
        # Get last day of month
        _, last_day_num = calendar.monthrange(current_year, current_month)
        last_day = date(current_year, current_month, last_day_num)
        
        # Cap last_day at today's date if it's in the future
        if last_day > today:
            last_day = today
            
        years.append(current_year)
        months.append(current_month)
        first_days.append(first_day)
        last_days.append(last_day)
        
        # Move to next month
        current_month += 1
        if current_month > 12:
            current_month = 1
            current_year += 1
    
    # Create DataFrame
    df = pd.DataFrame({
        'year': years,
        'month': months,
        'first_day': first_days,
        'last_day': last_days
    })
    
    return df

if __name__ == "__main__":
    pg_adaptor = PostgresAdaptor(
        # host="tech1-rdsdb-prod-1.cluster-cfkie8cke8ry.us-east-1.rds.amazonaws.com",
        host="apfo1-cluster.cluster-c3soyomw2flk.us-east-1.rds.amazonaws.com",
        database="fe_risk",
        schema="eqvol",
        user=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_USER"],
        password=os.environ["RDS_FE_RISK_EXTERNAL_WRITE_PASSWORD"],
    )

    df_etfs = pg_adaptor.execute_query(
        """select distinct bbg_full_ticker from eqvol.sr_security_ref""",
    )
    securities = df_etfs["bbg_full_ticker"].tolist()
    df_months = create_monthly_calendar_df('2024-01-01', '2025-01-15')
    print(df_months)
    for index, row in df_months.iterrows():
        yr = row["year"] - 2000
        batch_name = f"dp_etf_1mb_{yr}_{row['month']}_trd"
        print(f"Processing {batch_name}")
        from_date = row["first_day"].strftime('%Y%m%d')
        to_date = row["last_day"].strftime('%Y%m%d')
        date_range = f"{from_date}|{to_date}"
        per_sec_req_type = PerSecurityRequestType.gettickhistory
        request_dict = {
            "firm_name": "dl47544",
            "program_flag": "adhoc",
            "date_range":date_range,
            "sec_id": "TICKER",
            "content_type": "ohlc",
            "bar_interval": "1m",
            "event_type": "TRADE",
            "securities": securities,
            "fields": [],
        }
        target_folder = "/jfs/tech1_share/pulkit.vora/bbg_per_security/eqvol/etf_1mb/"
        PerSecurityRequestRunner(
            batch_name, per_sec_req_type, request_dict, target_folder
        ).run()