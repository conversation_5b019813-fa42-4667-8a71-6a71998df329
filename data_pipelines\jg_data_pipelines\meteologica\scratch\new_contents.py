import requests
import pandas as pd


url = "https://api-markets.meteologica.com/api/v1/login"

# response = requests.post(url, json={"user": "", "password": ""})

token = response.json()["token"]

url = "https://api-markets.meteologica.com/api/v1/contents"
params = {"token": token}
contents = requests.get(url, params=params)
data = contents.json()
df = pd.DataFrame(data["contents"])
df.to_csv("DW_all_meteo_contents.csv")

