apiVersion: v1
kind: Service
metadata:
  name: datacatalog
  namespace: default
spec:
  selector:
    app: datacatalog
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000
  type: NodePort
  externalTrafficPolicy: Local
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: datacatalog
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: datacatalog
  template:
    metadata:
      labels:
        app: datacatalog
    spec:
      containers:
        - name: datacatalog
          image: ${ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PREFIX}-ecr-datacatalog-${ENV}:latest
          ports:
            - containerPort: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: "datacatalog-ingress"
  namespace: default
  annotations:
    alb.ingress.kubernetes.io/scheme: internal
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS":443}, {"HTTP":80}]'
    alb.ingress.kubernetes.io/certificate-arn: ${SSL_CERTIFICATE_ARN}
  labels:
    app: datacatalog-nginx-ingress
spec:
  ingressClassName: alb
  rules:
    - http:
        paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: "datacatalog"
              port:
                number: 8000