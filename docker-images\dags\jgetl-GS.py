from airflow import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.contrib.operators.spark_submit_operator import SparkSubmitOperator
from datetime import datetime, timedelta
from airflow.hooks.base_hook import BaseHook

now = datetime.now()
spark_master_conn = BaseHook.get_connection("spark_default")
spark_master = spark_master_conn.host

default_args = {
    "owner": "airflow",
    "depends_on_past": False,
    "start_date": datetime(now.year, now.month, now.day),
    "email": ["<EMAIL>"],
    "email_on_failure": False,
    "email_on_retry": False,
    "retries": 1,
    "retry_delay": timedelta(minutes=1)
}

dag = DAG(
    dag_id="jg-etl-Goldmansachs", 
    description="This DAG runs an ETL pipeline to load data into Goldmansachs tables [ss_vol_asia]",
    default_args=default_args, 
    schedule_interval='@once',
    catchup=False
)

start = DummyOperator(task_id="start", dag=dag)

etl_job = SparkSubmitOperator(
    task_id="jg-etl-<PERSON><PERSON>chs",
    conn_id="jg_spark",
    application="/home/<USER>/spark/app/dags/main_write_gs.py",
    name="jg-etl-Goldmansachs",
    verbose=1,
    conf={
        "spark.master": spark_master,
        "spark.jars": "/home/<USER>/spark/app/jars/jg-iceberg-extensions-1.0-SNAPSHOT.jar,/home/<USER>/spark/app/jars/iceberg-spark-runtime.jar,/home/<USER>/spark/app/jars/iceberg-spark-extensions.jar"
    },
    spark_binary="/home/<USER>/.local/bin/spark-submit",
    dag=dag
)

end = DummyOperator(task_id="end", dag=dag)

start >> etl_job >> end
